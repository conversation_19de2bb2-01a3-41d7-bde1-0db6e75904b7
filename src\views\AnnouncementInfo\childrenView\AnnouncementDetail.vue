<template>
  <div class="announcement-detail-container">
    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <span class="breadcrumb-title" @click="handleGoBack">
        {{ previousPageName }}
      </span>
      <span class="separator">></span>
      <span class="current">{{ announcementInfo?.title || "公告详情" }}</span>
    </div>

    <!-- 公告内容 -->
    <div class="announcement-content" v-if="announcementInfo">
      <!-- 标题 -->
      <span class="announcement-title">{{ announcementInfo.title }}</span>

      <!-- 发布时间 -->
      <div class="announcement-meta">
        <span class="publish-time"
          >发布时间：{{ announcementInfo.addtime }}</span
        >
      </div>

      <!-- 内容区域 -->
      <div class="announcement-body" v-html="announcementInfo.content"></div>
    </div>

    <!-- 加载状态 -->
    <div v-else-if="loading" class="loading-container" v-loading="true" element-loading-text="加载中...">
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <div class="error-content">
        <SvgIcon iconName="warning" class="error-icon" />
        <h3>公告不存在或已被删除</h3>
        <p>您访问的公告可能已被删除或不存在，请检查链接是否正确。</p>
        <el-button type="primary" @click="goBack">返回上一页</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus"; // ElButton 由 unplugin-element-plus 自动引入
import { otherApi, auctionSessionApi } from "@/utils/api";

// 类型定义
interface AnnouncementInfo {
  id: number;
  title: string;
  content: string;
  addtime: string;
  cate_name: string;
  author?: string;
  view_count?: number;
  category_id?: number;
}

interface RelatedAnnouncement {
  id: number;
  title: string;
  addtime: string;
}

// 响应式数据
const route = useRoute();
const router = useRouter();

const announcementInfo = ref<AnnouncementInfo | null>(null);
const relatedAnnouncements = ref<RelatedAnnouncement[]>([]);
const loading = ref(true);
const error = ref(false);

// 计算属性
const announcementId = computed(() => {
  return route.query.id || route.params.id;
});

// 计算上一个页面名称
const previousPageName = computed(() => {
  // 直接从query参数中获取上一个页面名称
  return route.query.crumbsTitle || '公告信息';
});

// 方法
const fetchAnnouncementDetail = async () => {
  if (!announcementId.value) {
    error.value = true;
    loading.value = false;
    return;
  }

  try {
    loading.value = true;
    error.value = false;

    // 根据路由参数判断公告类型
    const announcementType = route.query.type;
    let response;

    if (announcementType === "auction") {
      // 拍卖会公告使用 auctionSessionApi.getAuctionSessionAnnouncement
      response = await auctionSessionApi.getAuctionSessionAnnouncement({
        id: announcementId.value,
      });

      if (response.code === 1 && response.data) {
        // 处理拍卖会公告数据格式
        const data = response.data;
        announcementInfo.value = {
          id: Number(announcementId.value),
          title: data.pmh_name || "",
          content: data.pmh_gonggao || "",
          addtime: data.addtime ? formatTimestamp(data.addtime) : "",
          cate_name: "拍卖会公告",
        };
      }
    } else {
      // 采购和销售公告使用 otherApi.getTenderDetail
      response = await otherApi.getTenderDetail({
        id: announcementId.value,
      });

      if (response.code === 1 && response.data) {
        // 处理采购/销售公告数据格式
        const data = response.data;

        announcementInfo.value = {
          id: Number(announcementId.value),
          title: data.title || "",
          content: data.content || "",
          addtime: data.addtime || "",
          cate_name: announcementType === "purchase" ? "采购公告" : "销售公告",
        };
      }
    }

    if (!announcementInfo.value) {
      error.value = true;
      ElMessage.error("公告不存在或已被删除");
    } else {
      // 获取相关公告
      await fetchRelatedAnnouncements();
    }
  } catch (err) {
    console.error("获取公告详情失败:", err);
    error.value = true;
    ElMessage.error("获取公告详情失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 时间戳转换函数（毫秒转YYYY-MM-DD格式）
const formatTimestamp = (timestamp: number | string): string => {
  const date = new Date(Number(timestamp));
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const fetchRelatedAnnouncements = async () => {
  try {
    // 这里应该调用实际的API获取相关公告
    // const response = await api.getRelatedAnnouncements(announcementId.value)
    // relatedAnnouncements.value = response.data

    // 模拟数据
    relatedAnnouncements.value = [
      {
        id: 2,
        title: "关于调整拍卖保证金比例的通知",
        addtime: "2024-03-10 14:20:00",
      },
      {
        id: 3,
        title: "2024年春季拍卖会征集公告",
        addtime: "2024-03-05 09:15:00",
      },
      {
        id: 4,
        title: "拍卖规则更新说明",
        addtime: "2024-02-28 16:45:00",
      },
    ];
  } catch (err) {
    console.error("获取相关公告失败:", err);
  }
};

// 点击面包屑回到公告信息页面
const handleGoBack = () => {
  router.back();
};

const goBack = () => {
  router.go(-1);
};

// 生命周期
onMounted(() => {
  fetchAnnouncementDetail();
});

// 监听路由变化
watch(
  () => route.query.id,
  (newId) => {
    if (newId) {
      fetchAnnouncementDetail();
    }
  }
);
</script>

<style lang="scss" scoped>
.announcement-detail-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 20px;
  margin-top: 20px;
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 10px;
}

.breadcrumb {
  display: flex;
  max-width: 1280px;
  margin: 0 auto;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  color: #888;
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 10px;

  .breadcrumb-title {
    cursor: pointer;
    color: #888;
    transition: color 0.2s;

    &:hover {
      color: #004c66;
    }
  }

  .separator {
    color: #888;
  }

  .current {
    color: #004c66;
    font-weight: 500;
    max-width: 600px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.announcement-content {
  background: white;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
  min-height: 610px;
}

.announcement-title {
  display: block;
  width: 100%;
  text-align: center;
  font-size: 32px;
  font-family: 'PingFang Bold';
  text-align: center;
  color: #333;
  margin: 20px 0;
  line-height: 1.4;
}

.announcement-meta {
  text-align: center;
  margin-bottom: 30px;

  .publish-time {
    font-size: 16px;
    color: #666;
  }
}

.announcement-body {
  width: 90%;
  margin: 0 auto;
  font-size: 16px;
  line-height: 1.8;
  color: #333;

  // 内容样式
  :deep(h1),
  :deep(h2),
  :deep(h3),
  :deep(h4),
  :deep(h5),
  :deep(h6) {
    margin: 30px 0 15px 0;
    color: #333;
    font-weight: bold;
  }

  :deep(h3) {
    font-size: 20px;
    border-left: 4px solid #409eff;
    padding-left: 10px;
  }

  :deep(p) {
    margin: 15px 0;
    text-indent: 2em;
  }

  :deep(ul),
  :deep(ol) {
    margin: 15px 0;
    padding-left: 30px;

    li {
      margin: 8px 0;
    }
  }

  :deep(img) {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 20px 0;
  }

  :deep(table) {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;

    th,
    td {
      border: 1px solid #ddd;
      padding: 12px;
      text-align: left;
    }

    th {
      background-color: #f5f5f5;
      font-weight: bold;
    }
  }

  :deep(blockquote) {
    border-left: 4px solid #ddd;
    margin: 20px 0;
    padding: 10px 20px;
    background-color: #f9f9f9;
    font-style: italic;
  }

  :deep(code) {
    background-color: #f5f5f5;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: "Courier New", monospace;
  }

  :deep(pre) {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 20px 0;

    code {
      background: none;
      padding: 0;
    }
  }
}

.loading-container {
  background: white;
  border-radius: 10px;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.error-container {
  background: white;
  padding: 60px 40px;
  text-align: center;
  min-height: 610px;
}

.error-content {
  .error-icon {
    font-size: 64px;
    color: #f56c6c;
    margin-bottom: 20px;
  }

  h3 {
    font-size: 24px;
    color: #333;
    margin-bottom: 15px;
  }

  p {
    font-size: 16px;
    color: #666;
    margin-bottom: 30px;
    line-height: 1.6;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .announcement-detail-container {
    padding: 10px;
  }

  .announcement-content {
    padding: 20px;
  }

  .announcement-title {
    font-size: 24px;
  }

  .announcement-body {
    width: 100%;
    font-size: 14px;

    :deep(p) {
      text-indent: 1em;
    }
  }
}

@media (max-width: 480px) {
  .breadcrumb {
    font-size: 12px;
    height: 40px;
  }

  .announcement-title {
    font-size: 20px;
  }

  .announcement-meta {
    .publish-time {
      font-size: 14px;
    }
  }
}

// 打印样式
@media print {
  .breadcrumb{
    display: none;
  }

  .announcement-content {
    box-shadow: none;
    border: none;
    padding: 0;
  }

  .announcement-body {
    width: 100%;
  }
}
</style>
