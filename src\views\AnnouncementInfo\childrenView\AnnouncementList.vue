<template>
  <div class="announcement-list-page">
    <div class="tab-nav">
      <div
        v-for="tab in tabs"
        :key="tab.value"
        :class="['tab-item', { active: currentTab === tab.value }]"
        @click="changeTab(tab.value)"
      >
        {{ tab.label }}
      </div>
    </div>
    <div class="list-container">
      <div class="auction-list">
        <template v-for="(item, index) in pagedList" :key="item.id">
          <AuctionListItem
            :productId="item.id"
            :productName="item.title"
            :productImage="item.image || ''"
            :description="item.description"
            :companyName="item.companyName"
            :timeValue="item.timeValue"
            :likeCount="item.likeCount"
            :commentCount="item.commentCount"
            :viewCount="item.viewCount"
            @click="() => handleListItemClick(item.id)"
          />
          <div v-if="index < pagedList.length - 1" class="item-divider"></div>
        </template>
      </div>
      <el-pagination
        v-if="total > pageSize"
        class="pagination"
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="pageSize"
        :current-page="currentPage"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { useRouter } from "vue-router";
import AuctionListItem from "@/components/AuctionListItem.vue";
import { otherApi, auctionSessionApi } from "@/utils/api";

const router = useRouter();

// 标签页配置
const tabs = [
  // { label: "全部", value: "all" },
  { label: "采购公告", value: "purchase" },
  { label: "销售公告", value: "sale" },
  { label: "拍卖会公告", value: "auction" },
];
const currentTab = ref("purchase");

// 数据状态
const allList = ref<any[]>([]);
const loading = ref(false);
const pageSize = 10;
const currentPage = ref(1);
const total = ref(0);

// 默认图片配置
const defaultImages = {
  purchase: 'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/purchase_1754962265712.png', // 采购公告默认图片
  sale: 'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/sale_1754962281689.png', // 销售公告默认图片
};

/**
 * 获取采购公告数据
 * @param page 页码
 */
const getPurchaseAnnouncements = async (page: number = 1) => {
  try {
    const response = await otherApi.getTenderList({
      cateid: "29", // 采购公告分类ID
      limit: pageSize,
      page: page,
    });

    if (response.code === 1 && response.data) {
      total.value = response.data.total;
      return response.data.data.map((item: any) => ({
        id: item.id.toString(),
        type: "purchase",
        title: item.title,
        image: defaultImages.purchase, // 使用默认图片
        description: item.title, // 使用标题作为描述
        companyName: item.cate_name || "采购公告",
        timeValue: item.addtime,
        likeCount: 0,
        commentCount: 0,
        viewCount: 0,
      }));
    }
    return [];
  } catch (error) {
    console.error("获取采购公告失败:", error);
    return [];
  }
};

/**
 * 获取销售公告数据
 * @param page 页码
 */
const getSaleAnnouncements = async (page: number = 1) => {
  try {
    const response = await otherApi.getTenderList({
      cateid: "30", // 销售公告分类ID
      limit: pageSize,
      page: page,
    });

    if (response.code === 1 && response.data) {
      total.value = response.data.total;
      return response.data.data.map((item: any) => ({
        id: item.id.toString(),
        type: "sale",
        title: item.title,
        image: defaultImages.sale, // 使用默认图片
        description: item.title, // 使用标题作为描述
        companyName: item.cate_name || "销售公告",
        timeValue: item.addtime,
        likeCount: 0,
        commentCount: 0,
        viewCount: 0,
      }));
    }
    return [];
  } catch (error) {
    console.error("获取销售公告失败:", error);
    return [];
  }
};

/**
 * 获取拍卖会公告数据
 * @param page 页码
 */
const getAuctionAnnouncements = async (page: number = 1) => {
  try {
    const response = await auctionSessionApi.getAuctionSessionAnnouncementList({
      page: page,
    });

    // console.log("拍卖会公告数据: ", response);

    if (response.code === 1 && response.data) {
      total.value = response.data.total;
      return response.data.data.map((item: any) => ({
        id: item.id.toString(),
        type: "auction",
        title: item.pmh_name,
        image: item.pmh_pic
          ? `${
              import.meta.env.VITE_API_BASE_URL ||
              "https://huigupaimai.oss-cn-beijing.aliyuncs.com"
            }/${item.pmh_pic}`
          : "", // 拍卖会公告有图片
        description: item.pmh_gonggao || item.pmh_name,
        companyName: item.parentname || "拍卖会公告",
        timeValue: item.start_time_name || item.addtime,
        likeCount: 0,
        commentCount: 0,
        viewCount: 0,
      }));
    }
    return [];
  } catch (error) {
    console.error("获取拍卖会公告失败:", error);
    return [];
  }
};

/**
 * 加载数据
 * @param page 页码
 */
const loadData = async (page: number = 1) => {
  loading.value = true;

  try {
    let data: any[] = [];

    switch (currentTab.value) {
      case "purchase":
        data = await getPurchaseAnnouncements(page);
        break;
      case "sale":
        data = await getSaleAnnouncements(page);
        break;
      case "auction":
        data = await getAuctionAnnouncements(page);
        break;
    }

    allList.value = data;
  } catch (error) {
    console.error("加载数据失败:", error);
    allList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 计算属性
const filteredList = computed(() => {
  return allList.value.filter((item) => item.type === currentTab.value);
});

const pagedList = computed(() => {
  return filteredList.value;
});

/**
 * 切换标签页
 * @param tab 标签页值
 */
function changeTab(tab: string) {
  currentTab.value = tab;
  currentPage.value = 1;
  loadData(1);
}

/**
 * 处理分页变化
 * @param page 页码
 */
function handlePageChange(page: number) {
  currentPage.value = page;
  loadData(page);
}

/**
 * 处理列表项点击
 * @param id 公告ID
 */
function handleListItemClick(id: string) {
  // console.log("点击了公告:", id);

  router.push({ 
    name: "announcementInfo-detail",
    query: {
      id,
      type: currentTab.value,
      crumbsTitle: '公告信息'
    }
  });
}

// 组件挂载时加载数据
onMounted(() => {
  loadData(1);
});
</script>

<style scoped>
.announcement-list-page {
  background: #f5f6fa;
  min-height: 100vh;
  padding: 0 0 40px 0;
}
.tab-nav {
  display: flex;
  max-width: 1280px;
  margin: 0 auto;
  padding: 13px 0;
  align-items: center;
  gap: 40px;
  font-size: 16px;
  color: #888;
}
.tab-item {
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: color 0.2s, border-color 0.2s;
  min-width: 72px;
  text-align: center;
  margin-right: 32px;
}
.tab-item.active {
  color: #004c66;
  font-family: "PingFang Bold";
  background: none;
}
.list-container {
  max-width: 1280px;
  margin: 0 auto;
  background: #fff;
  border-radius: 10px;
  padding: 20px;
}
.auction-list {
  display: flex;
  flex-direction: column;
}
.item-divider {
  height: 1px;
  background-color: #dddddd;
  margin: 20px 0;
}
.pagination {
  margin: 40px 0 0 0;
  display: flex;
  justify-content: center;
}
</style>
