<template>
  <div class="property-card" @click="handleCardClick">
    <!-- 状态标签 -->
    <div class="status-tag" :class="{ 'status-active': status==='5','status-inactive':status==='2' }">
      <span>{{ statusName }}</span>
    </div>

    <!-- 资产图片 -->
    <div class="product-image">
      <img :src="productImage" :alt="productName" />
    </div>

    <!-- 资产信息 - 上中下结构 -->
    <div class="product-info">
      <!-- 上部分：资产标题 -->
      <div class="product-title">
        <div class="product-name">{{ productName }}</div>
        <div class="product-parameter">
          <!-- 数量 -->
          <span>{{ productCount }}{{ productCountUnit }}</span>
          <!-- 重量 -->
          <span>{{ productWeight }}{{ productWeightUnit }}</span>
          <!-- 功率 -->
          <span v-if="productPower"
            >{{ productPower }}{{ productPowerUnit }}</span
          >
        </div>
      </div>

      <!-- 中间部分：价格和围观次数 -->
      <div class="price-view-container">
        <!-- 左侧：价格 -->
        <div class="price-section">
          <div class="current-price">
            <span class="price-decimal">￥</span>
            <span class="price-integer">{{ priceInteger }}</span>
            <span class="price-decimal">.{{ priceDecimal }}</span>
            <span class="price-unit" style="font-size: 14px">{{
              priceUnit
            }}</span>
          </div>
        </div>

        <!-- 右侧：围观次数 -->
        <div class="view-section">
          <div class="view-count">{{ viewCount }}人看过</div>
        </div>
      </div>

      <!-- 下部分：企业信息 -->
      <div class="enterprise-info">
        <div class="enterprise-info-container">
          <div class="enterprise-logo">
            <img
              v-if="enterpriseLogo"
              :src="enterpriseLogo"
              :alt="enterpriseName"
            />
            <span v-else>{{ status === "upcoming" ? "企业" : "商家" }}</span>
          </div>
          <div class="enterprise-name">{{ enterpriseName }}</div>
          <div class="enterprise-type">
            <CompanyTag :enterpriseType="enterpriseType" />
          </div>
        </div>
        <div class="enterprise-info-arrow">
          <SvgIcon iconName="freedom-arrow" className="arrow-icon" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import SvgIcon from "@/components/SvgIcon.vue";
import type { PropertyItem } from "@/types/property";
import CompanyTag from "./CompanyTag.vue";

interface Props extends PropertyItem {}

const props = withDefaults(defineProps<Props>(), {
  status: "upcoming",
  buttonType: "register",
});

const emit = defineEmits<{
  (e: "click", payload: {productId:string,productName:string}): void;
}>();

// 价格格式化
const priceInteger = computed(() => {
  return Math.floor(props.currentPrice);
});

const priceDecimal = computed(() => {
  const decimal = (props.currentPrice - Math.floor(props.currentPrice)) * 100;
  return decimal.toFixed(0).padStart(2, "0");
});

// 方法
const handleButtonClick = () => {
  emit("click", {productId: props.productId, productName: props.productName});
};

// 处理卡片点击事件
const handleCardClick = () => {
  emit("click", {productId:props.productId,productName:props.productName});


};
</script>

<style lang="scss" scoped>
.property-card {
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  position: relative;
  width: 295px;
  height: 349px;
  border-radius: 10px;
  border: 1px solid #eeeeee;
  background-color: #ffffff;
  overflow: hidden;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;

  &:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  }

  // 状态标签样式
  .status-tag {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 42px;
    height: 23px;
    background: linear-gradient(90deg, #ff6b00, #ff0000);
    color: white;
    border-top-left-radius: 10px;
    border-bottom-right-radius: 10px;
    font-size: 12px;
    z-index: 10;

    &.status-active {
      background: linear-gradient(90deg, #009678, #004c66);
    }
    &.status-inactive {
      background: #004c66;
      padding: 0 3px;
    }

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -150%;
      width: 30%;
      height: 100%;
      background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.3) 50%,
        rgba(255, 255, 255, 0) 100%
      );
      transform: skewX(-25deg);
      z-index: 1;
      transition: none;
      pointer-events: none; // 确保不影响鼠标事件
    }
  }

  &:hover {
    .status-tag::before {
      // 给左上角标签添加闪光动画
      animation: shinee 1s ease-in-out;
    }
  }

  // 定义亮光扫过动画
  @keyframes shinee {
    0% {
      left: -150%;
    }
    100% {
      left: 80%;
    }
  }

  // 资产图片样式
  .product-image {
    width: 100%;
    height: 198px;
    overflow: hidden;
    position: relative; // 添加相对定位

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.5s ease;
      transform: scale(1.02);
    }

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -150%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.3) 50%,
        rgba(255, 255, 255, 0) 100%
      );
      transform: skewX(-25deg);
      z-index: 1;
      transition: none;
      pointer-events: none; // 确保不影响鼠标事件
    }
  }

  &:hover {
    .product-image img {
      transform: scale(1.08);
    }

    .product-image::before {
      // animation: shine 1s ease-in-out;
    }

    .product-name {
      color: #004c66;
    }
  }

  // 定义亮光扫过动画
  @keyframes shine {
    0% {
      left: -150%;
    }
    100% {
      left: 150%;
    }
  }

  // 资产信息样式 - 上中下结构
  .product-info {
    display: flex;
    flex-direction: column;
    height: 154px; // 总高度减去图片高度和padding
    color: #333;
    padding: 10px 10px 13px 10px;

    // 上部分：资产标题
    .product-title {
      .product-name {
        font-size: 16px;
        font-family: "PingFang Bold";
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-bottom: 5px;
      }

      .product-parameter {
        font-size: 14px;
        color: #999;
        span {
          // 为所有span之间添加分割线，但最后一个不添加
          &:not(:last-child)::after {
            content: "|";
            margin: 0 5px;
            color: #999;
          }
        }
      }
    }

    // 中间部分：价格和围观次数
    .price-view-container {
      display: flex;
      align-items: baseline;
      margin-top: 11px;
      margin-bottom: 10px;

      // 左侧：价格
      .price-section {
        display: flex;
        align-items: baseline;
        justify-content: end;
        // flex-direction: column;

        .current-price {
          display: flex;
          align-items: baseline;
          color: #004c66;
          font-family: "DIN Bold";

          .price-integer {
            font-size: 28px;
          }

          .price-decimal {
            font-size: 20px;
          }

          .price-unit {
            font-size: 14px;
            margin-left: 2px;
            font-family: "PingFang Bold";
          }
        }
      }

      // 右侧：围观次数
      .view-section {
        margin-left: 8px;
        .view-count {
          font-size: 14px;
          color: #999;
        }
      }
    }

    // 下部分：企业信息
    .enterprise-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .enterprise-info-container {
        display: flex;
        align-items: center;
        justify-content: center;
        .enterprise-logo {
          width: 27px;
          height: 27px;
          border-radius: 50%;
          overflow: hidden;
          margin-right: 5px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid #ddd;
          img {
            width: 14.52px;
            height: 17px;
            object-fit: cover;
          }
          span {
            font-size: 12px;
          }
        }
        .enterprise-name {
          font-size: 16px;
          color: #999;
          margin-right: 5px;
        }
      }

      .enterprise-info-arrow {
        margin-left: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        .arrow-icon {
          width: 15px;
          height: 9.5px;
        }
      }
    }
  }
}
</style>
