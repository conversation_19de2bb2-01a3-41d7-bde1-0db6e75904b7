import type { TableConfig, ButtonItem, FilterItem, TabItem, TableColumn } from '@/types/table'

// 发布列表配置工厂
export class PublishListConfigFactory {
  
  /**
   * 创建供应信息列表配置
   */
  static createSupplyListConfig(): TableConfig {
    return {
      // 按钮切换栏
      buttonBar: {
        show: true,
        buttons: [
          {
            key: 'all',
            label: '全部任务',
            type: 'primary',
            active: true
          },
          {
            key: 'increase',
            label: '增值委托',
            type: 'default'
          },
          {
            key: 'independent',
            label: '自主委托',
            type: 'default'
          },
          {
            key: 'supply',
            label: '供求信息',
            type: 'default'
          }
        ]
      },
      
      // 筛选栏
      filterBar: {
        show: true,
        filters: [
          {
            key: 'keyword',
            label: '关键词',
            type: 'input',
            placeholder: '请输入关键词搜索',
            width: '200px'
          },
          {
            key: 'category',
            label: '分类',
            type: 'select',
            placeholder: '请选择分类',
            width: '150px',
            options: [
              { label: '废钢', value: 'steel' },
              { label: '废铜', value: 'copper' },
              { label: '废铝', value: 'aluminum' },
              { label: '废纸', value: 'paper' },
              { label: '废塑料', value: 'plastic' }
            ]
          },
          {
            key: 'region',
            label: '地区',
            type: 'select',
            placeholder: '请选择地区',
            width: '150px',
            options: [
              { label: '北京', value: 'beijing' },
              { label: '上海', value: 'shanghai' },
              { label: '广州', value: 'guangzhou' },
              { label: '深圳', value: 'shenzhen' }
            ]
          },
          {
            key: 'dateRange',
            label: '发布时间',
            type: 'daterange',
            placeholder: '请选择时间范围',
            width: '300px'
          }
        ]
      },
      
      // 水平导航栏
      tabBar: {
        show: true,
        activeKey: 'all',
        tabs: [
          {
            key: 'all',
            label: '全部供应',
            count: 156
          },
          {
            key: 'publishing',
            label: '发布中',
            count: 45
          },
          {
            key: 'pending',
            label: '待审核',
            count: 12
          },
          {
            key: 'expired',
            label: '已过期',
            count: 23
          },
          {
            key: 'failed',
            label: '未通过',
            count: 5
          }
        ]
      },
      
      // 表格列配置
      columns: [
        {
          key: 'title',
          label: '标题',
          width: '300px',
          align: 'left'
        },
        {
          key: 'category',
          label: '分类',
          width: '120px',
          align: 'center',
          type: 'tag'
        },
        {
          key: 'quantity',
          label: '数量',
          width: '100px',
          align: 'right',
          formatter: (value) => `${value} 吨`
        },
        {
          key: 'price',
          label: '价格',
          width: '120px',
          align: 'right',
          type: 'money'
        },
        {
          key: 'region',
          label: '地区',
          width: '120px',
          align: 'center'
        },
        {
          key: 'publishTime',
          label: '发布时间',
          width: '150px',
          align: 'center',
          type: 'date',
          sortable: true
        },
        {
          key: 'status',
          label: '状态',
          width: '100px',
          align: 'center',
          type: 'tag',
          formatter: (value) => {
            const statusMap: Record<string, string> = {
              'publishing': '发布中',
              'pending': '待审核',
              'expired': '已过期',
              'failed': '未通过'
            }
            return statusMap[value] || value
          }
        },
        {
          key: 'actions',
          label: '操作',
          width: '150px',
          align: 'center',
          type: 'action'
        }
      ],
      
      // 分页配置
      pagination: {
        show: true,
        current: 1,
        pageSize: 10,
        total: 156,
        showSizeChanger: true,
        showQuickJumper: true,
        pageSizes: [10, 20, 50, 100]
      },
      
      // 数据
      data: [],
      loading: false,

      // 表格样式配置
      tableStyle: {
        maxHeight: 380,
        border: true,
        stripe: true,
        showSelection: false
      }
    }
  }
  
  /**
   * 创建求购信息列表配置
   */
  static createDemandListConfig(): TableConfig {
    const config = this.createSupplyListConfig()
    
    // 修改标签页配置
    config.tabBar!.tabs = [
      {
        key: 'all',
        label: '全部求购',
        count: 89
      },
      {
        key: 'publishing',
        label: '发布中',
        count: 32
      },
      {
        key: 'pending',
        label: '待审核',
        count: 8
      },
      {
        key: 'expired',
        label: '已过期',
        count: 15
      },
      {
        key: 'failed',
        label: '未通过',
        count: 3
      }
    ]
    
    // 修改分页总数
    config.pagination!.total = 89

    // 保持相同的表格样式配置
    config.tableStyle = {
      maxHeight: 380,
      border: true,
      stripe: true,
      showSelection: false
    }

    return config
  }
  
  /**
   * 创建简化版配置（只有表格，无筛选栏等）
   */
  static createSimpleListConfig(): TableConfig {
    return {
      buttonBar: {
        show: false,
        buttons: []
      },
      filterBar: {
        show: false,
        filters: []
      },
      tabBar: {
        show: false,
        tabs: [],
        activeKey: ''
      },
      columns: [
        {
          key: 'title',
          label: '标题',
          align: 'left'
        },
        {
          key: 'category',
          label: '分类',
          width: '120px',
          align: 'center'
        },
        {
          key: 'publishTime',
          label: '发布时间',
          width: '150px',
          align: 'center',
          type: 'date'
        },
        {
          key: 'status',
          label: '状态',
          width: '100px',
          align: 'center',
          type: 'tag'
        }
      ],
      pagination: {
        show: true,
        current: 1,
        pageSize: 10,
        total: 0
      },
      data: [],
      loading: false,

      // 表格样式配置
      tableStyle: {
        maxHeight: 300,
        border: true,
        stripe: true,
        showSelection: false
      }
    }
  }
}

// 导出默认配置
export const defaultTableConfig: TableConfig = PublishListConfigFactory.createSimpleListConfig()
