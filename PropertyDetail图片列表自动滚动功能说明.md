# PropertyDetail 图片列表自动滚动功能实现说明

## 功能概述
为自由交易页面（PropertyDetail.vue）的垂直图片列表添加了鼠标移动时的自动滚动功能。当用户鼠标移动到**滚动区域的上边缘或下边缘**时，会自动向上或向下滚动一张图片的距离。

## 实现的功能特性

### 1. 智能边缘检测
- 检测鼠标在垂直滚动区域的位置
- 上边缘80px区域触发向上滚动
- 下边缘80px区域触发向下滚动
- 使用 `getBoundingClientRect()` 精确计算鼠标位置

### 2. 自动滚动机制
- **向上滚动**：当鼠标移动到滚动区域上边缘80px区域时触发
- **向下滚动**：当鼠标移动到滚动区域下边缘80px区域时触发
- **单次滚动**：每次只滚动一张图片的距离（120px）
- **平滑滚动**：使用 `smooth` 行为，提供流畅的用户体验

### 3. 用户体验优化
- **快速响应**：鼠标移动到边缘后100ms开始滚动
- **即时停止**：鼠标移出滚动区域时立即停止自动滚动
- **防重复**：滚动过程中阻止新的滚动操作
- **视觉提示**：边缘区域有淡色渐变提示，鼠标悬停时显示

## 核心方法说明

### `isMouseAtScrollEdge(mouseY: number)`
检查鼠标是否在滚动区域的边缘位置
- 返回 `{ isTopEdge: boolean; isBottomEdge: boolean }`
- 通过鼠标的clientY坐标和容器位置计算
- 边缘触发区域高度为80px

### `autoScrollUp()` 和 `autoScrollDown()`
执行自动滚动操作
- 每次滚动120px（一张图片的高度）
- 只滚动一次，避免过度滚动
- 滚动完成后400ms重置状态

### `handleScrollAreaMouseMove(event: MouseEvent)`
处理滚动区域的鼠标移动事件
- 检测鼠标是否在滚动区域边缘
- 根据位置和滚动能力决定是否启动自动滚动
- 100ms延迟启动滚动，避免频繁触发

### `handleScrollAreaMouseLeave()`
处理滚动区域的鼠标移出事件
- 立即停止所有自动滚动操作
- 清理定时器

## 技术实现细节

### 1. 状态管理
```typescript
const autoScrollTimer = ref<number | null>(null);  // 自动滚动定时器
const isAutoScrolling = ref<boolean>(false);       // 滚动状态标记
```

### 2. 事件绑定
在图片列表容器上添加了鼠标事件：
```vue
<div 
  class="image-list" 
  ref="imageListRef"
  @mousemove="handleScrollAreaMouseMove"
  @mouseleave="handleScrollAreaMouseLeave"
>
```

### 3. 生命周期管理
在组件卸载时清理定时器，防止内存泄漏：
```typescript
onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
  stopAutoScroll();
});
```

### 4. 视觉提示样式
```scss
.image-list {
  // 上边缘触发区域
  &::before {
    background: linear-gradient(180deg, rgba(0, 76, 102, 0.05) 0%, transparent 100%);
  }
  
  // 下边缘触发区域
  &::after {
    background: linear-gradient(0deg, rgba(0, 76, 102, 0.05) 0%, transparent 100%);
  }
}
```

## 与 AuctionDetail 的区别

| 特性 | AuctionDetail | PropertyDetail |
|------|---------------|----------------|
| 滚动方向 | 水平滚动 | 垂直滚动 |
| 触发区域 | 左右边缘80px | 上下边缘80px |
| 滚动距离 | 120px（图片宽度） | 120px（图片高度） |
| 边缘检测 | clientX坐标 | clientY坐标 |
| 渐变方向 | 90deg/270deg | 180deg/0deg |

## 使用场景
- 当图片列表较长，超出容器可视范围时
- 用户想要快速浏览更多图片时
- 解决了图片未完全显示时无法触发滚动的问题
- 提供更好的垂直图片浏览体验

## 兼容性
- 支持现代浏览器
- 使用标准的DOM API
- 平滑滚动需要浏览器支持 `scroll-behavior: smooth`

## 注意事项
1. 只有在可以滚动的情况下才会触发自动滚动
2. 滚动过程中会阻止新的滚动操作
3. 鼠标移出滚动区域后会立即停止滚动
4. 每次只滚动一张图片的距离，避免滚动过度
5. 边缘触发区域为80px，可根据需要调整
6. 鼠标悬停时会显示边缘区域的视觉提示
7. 适配了垂直滚动的特殊需求
