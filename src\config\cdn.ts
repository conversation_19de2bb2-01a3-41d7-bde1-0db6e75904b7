/**
 * CDN 配置文件
 * 用于配置外部CDN资源，减少打包体积
 * 注意：使用CDN需要确保网络稳定性
 */

export interface CDNConfig {
  name: string
  var: string
  js: string
  css?: string
}

// CDN 资源配置
export const cdnConfigs: CDNConfig[] = [
  // Vue 3 (可选，如果网络环境允许)
  // {
  //   name: 'vue',
  //   var: 'Vue',
  //   js: 'https://unpkg.com/vue@3/dist/vue.global.prod.js'
  // },
  
  // Element Plus (可选，如果网络环境允许)
  // {
  //   name: 'element-plus',
  //   var: 'ElementPlus',
  //   js: 'https://unpkg.com/element-plus@latest/dist/index.full.min.js',
  //   css: 'https://unpkg.com/element-plus@latest/dist/index.css'
  // },
  
  // Axios (可选)
  // {
  //   name: 'axios',
  //   var: 'axios',
  //   js: 'https://unpkg.com/axios@latest/dist/axios.min.js'
  // }
]

// 生产环境是否启用CDN
export const enableCDN = false // 默认关闭，可根据需要开启

// 获取外部化配置
export function getExternals() {
  if (!enableCDN) return {}
  
  const externals: Record<string, string> = {}
  cdnConfigs.forEach(config => {
    externals[config.name] = config.var
  })
  return externals
}

// 获取CDN链接
export function getCDNLinks() {
  if (!enableCDN) return { js: [], css: [] }
  
  const js: string[] = []
  const css: string[] = []
  
  cdnConfigs.forEach(config => {
    js.push(config.js)
    if (config.css) {
      css.push(config.css)
    }
  })
  
  return { js, css }
}

// 生成HTML中的CDN标签
export function generateCDNTags() {
  const { js, css } = getCDNLinks()
  
  const jsTags = js.map(url => `<script src="${url}"></script>`).join('\n')
  const cssTags = css.map(url => `<link rel="stylesheet" href="${url}">`).join('\n')
  
  return { jsTags, cssTags }
}
