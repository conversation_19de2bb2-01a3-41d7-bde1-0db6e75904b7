<template>
  <!-- 验证码登录表单 -->
  <el-form
    ref="smsFormRef"
    :model="smsForm"
    :rules="smsRules"
    :validate-on-rule-change="false"
    class="login-form"
  >
    <!-- 用户类型选择 -->
    <!-- <el-form-item prop="userType">
      <el-select
        v-model="smsForm.userType"
        placeholder="请选择登录身份"
        class="user-type-select"
        size="large"
      >
        <el-option label="竞买人" value="personal" />
        <el-option label="处置企业" value="enterprise" />
      </el-select>
    </el-form-item> -->

    <!-- 手机号输入框 -->
    <el-form-item prop="phone">
      <el-input
        v-model="smsForm.phone"
        placeholder="请输入手机号"
        size="large"
        class="login-input"
        maxlength="11"
      />
    </el-form-item>

    <!-- 验证码输入框 -->
    <el-form-item prop="smsCode" class="password">
      <el-input
        v-model="smsForm.smsCode"
        placeholder="请输入验证码"
        size="large"
        class="login-input sms-input-with-button"
        maxlength="6"
      >
        <template #suffix>
          <span
            class="sms-text-button"
            :class="{ disabled: isSmsButtonDisabled() }"
            @click="handleSendSms"
          >
            {{ getSmsButtonText() }}
          </span>
        </template>
      </el-input>
    </el-form-item>

    <!-- 短信验证码提示 -->
    <el-form-item class="form-options">
      <div class="sms-tips">
        <span>验证即登录，未注册将自动创建账号</span>
      </div>
    </el-form-item>

    <!-- 登录按钮 -->
    <el-form-item>
      <el-button
        type="primary"
        size="large"
        class="login-button"
        :loading="loginLoading"
        @click="handleSmsLogin"
      >
        登录
      </el-button>
    </el-form-item>

    <!-- 注册链接 -->
    <div class="register-link">
      <span>还没有账号？</span>
      <a href="#" @click.prevent="handleRegister">立即注册</a>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { authApi } from "@/utils/api";
import { useUserStore } from "@/stores/user";
import { useSms } from "../composables/useSms";

// 定义组件事件
const emit = defineEmits<{
  switchTab: [tab: string];
  loginSuccess: [];
}>();

// 用户状态管理
const userStore = useUserStore();

// 使用短信验证码组合式函数
const { sendSms, getSmsButtonText, isSmsButtonDisabled } = useSms();

// 登录加载状态
const loginLoading = ref(false);

// 表单引用
const smsFormRef = ref<FormInstance>();

// 验证码登录表单数据
const smsForm = reactive({
  userType: "", // 用户类型
  phone: "", // 手机号
  smsCode: "", // 短信验证码
});

// 验证码登录验证规则
const smsRules: FormRules = {
  userType: [{ required: true, message: "请选择登录身份", trigger: "change" }],
  phone: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号格式",
      trigger: "blur",
    },
  ],
  smsCode: [
    { required: true, message: "请输入验证码", trigger: "blur" },
    {
      pattern: /^\d{4}$/,
      message: "验证码为4位数字",
      trigger: "blur",
    },
  ],
};

/**
 * 发送短信验证码
 */
const handleSendSms = async () => {
  // 先验证手机号
  if (!smsFormRef.value) return;

  try {
    await smsFormRef.value.validateField("phone");
    // 调用公共的发送短信方法
    await sendSms(smsForm.phone);
  } catch (error) {
    // 验证失败，不发送短信
    // console.log("手机号验证失败"); 已移除
  }
};

/**
 * 处理验证码登录
 */
const handleSmsLogin = async () => {
  if (!smsFormRef.value) return;

  try {
    // 表单验证
    await smsFormRef.value.validate();

    loginLoading.value = true;

    // 调用验证码登录API
    const response = await authApi.smsLogin({
      mobile: smsForm.phone,
      yzcode: smsForm.smsCode,
    });

    if (response.code === 1) {
      // 登录成功
      const userInfo = response.data

      // 保存用户状态
      userStore.login(userInfo);

      ElMessage.success("登录成功");
      emit("loginSuccess");
    } else {
      ElMessage.error(response.msg || "登录失败，请检查手机号和验证码");
    }
  } catch (error) {
    console.error("验证码登录失败:", error);
    ElMessage.error("登录失败，请检查手机号和验证码");
  } finally {
    loginLoading.value = false;
  }
};

/**
 * 处理注册
 */
const handleRegister = () => {
  emit("switchTab", "register");
};
</script>

<style lang="scss" scoped>
.login-form {
  .el-form-item {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .form-options {
    margin-bottom: 38px;
  }

  .password {
    margin-bottom: 8px;
  }

  :deep(.el-select__wrapper) {
    height: 46px;
  }

  .user-type-select,
  .login-input {
    width: 100%;
    height: 46px;
  }

  .sms-input-with-button {
    :deep(.el-input__suffix) {
      padding-right: 0px;
    }
  }

  .sms-text-button {
    color: #004c66;
    font-size: 12px;
    font-weight: 400;
    cursor: pointer;
    user-select: none;
    transition: opacity 0.3s ease;

    &:hover:not(.disabled) {
      opacity: 0.8;
    }

    &.disabled {
      color: #ccc;
      cursor: not-allowed;
    }
  }

  .sms-tips {
    color: #666;
    width: 100%;
    text-align: center;
  }

  .login-button {
    width: 100%;
    height: 48px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;

    &:hover {
      background: rgba($color: #004c66, $alpha: 0.9);
    }
  }
}

.register-link {
  text-align: center;
  margin-top: 24px;
  color: #666;
  font-size: 14px;

  a {
    color: #004c66;
    text-decoration: none;
    margin-left: 4px;

    &:hover {
      text-decoration: underline;
      background-color: transparent;
    }
  }
}
</style>
