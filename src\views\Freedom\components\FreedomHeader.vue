<script setup lang="ts">
import { ref, watch, nextTick, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";

const router = useRouter();
const route = useRoute();
const activeIndex = ref("/freedom/freedomHome");
// 初始化下划线样式为空，避免闪烁效果
const underlineStyle = ref({ left: "0px", width: "0px" });

interface NavItem {
  name: string;
  path: string;
  activeIndex: string;
  active: boolean;
}

const navItems = ref<NavItem[]>([
  {
    name: "首页",
    path: "/freedom/freedomHome",
    activeIndex: "freedomHome",
    active: true,
  },
  {
    name: "服务支持",
    path: "/freedom/freedomSupport",
    activeIndex: "freedomSupport",
    active: false,
  },
]);

// 更新下划线位置
const updateUnderlinePosition = (targetPath?: string) => {
  nextTick(() => {
    // 使用传入的路径或当前activeIndex值
    const pathToFind = targetPath || activeIndex.value;
    const activeMenuItem = document.querySelector(
      `[data-path="${pathToFind}"]`
    ) as HTMLElement;
    if (activeMenuItem) {
      const menuContainer = activeMenuItem.parentElement;
      if (menuContainer) {
        const containerRect = menuContainer.getBoundingClientRect();
        const activeRect = activeMenuItem.getBoundingClientRect();
        const left = activeRect.left - containerRect.left;
        underlineStyle.value = {
          left: `${left}px`,
          width: `${activeRect.width}px`,
        };
      }
    }
  });
};

// 监听路由变化，更新activeIndex和下划线位置
watch(
  () => route.path,
  (newPath) => {
    // 只有当路径真正改变时才更新
    if (activeIndex.value !== newPath) {
      activeIndex.value = newPath;
      // 延迟更新下划线位置，确保DOM已更新
      nextTick(() => {
        updateUnderlinePosition();
      });
    }
  },
  { immediate: true }
);

// 组件挂载后初始化下划线位置
onMounted(() => {
  updateUnderlinePosition();
});

const handleSelect = (key: string) => {
  // 先基于目标路径更新下划线位置，避免DOM查询混乱
  updateUnderlinePosition(key);
  // 然后更新activeIndex
  activeIndex.value = key;
  // 最后进行路由跳转
  router.push(key);
};
</script>

<template>
  <header class="header">
    <div class="header-content">
      <el-menu
        :default-active="activeIndex"
        class="nav-menu"
        mode="horizontal"
        :ellipsis="false"
        @select="handleSelect"
        background-color="#f2f2f2"
        text-color="#999"
        active-text-color="#004C66"
      >
        <el-menu-item
          v-for="item in navItems"
          :key="item.path"
          :index="item.path"
          :data-path="item.path"
          class="menu-item-wrapper"
        >
          <span class="menu-item-name">
            {{ item.name }}
          </span>
        </el-menu-item>

        <!-- 滑动下划线 -->
        <div
          class="sliding-underline"
          :style="{
            left: underlineStyle.left,
            width: underlineStyle.width,
          }"
        ></div>
      </el-menu>
    </div>
  </header>
</template>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 45px;
  background-color: #f2f2f2;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid #ddd;

  .header-content {
    max-width: 1280px;
    height: 100%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;

    // 修改el-menu-item的hover样式，去掉鼠标移入时的背景色
    :deep(.el-menu--horizontal) .el-menu-item:not(.is-disabled):hover {
      background-color: transparent;
    }

    :deep(.el-menu--horizontal) .el-menu-item:not(.is-disabled):focus {
      background-color: transparent;
    }

    // 隐藏菜单原本的下划线
    .el-menu--horizontal {
      .el-menu-item.is-active {
        border-bottom: none;
      }
    }

    .nav-menu {
      border: none;
      width: 100%;
      height: 45px;
      background-color: #f2f2f2;
      position: relative;
      border-bottom: 1px solid #ddd;

      :deep(.el-menu-item) {
        padding: 0;
        margin-right: 72px;
        position: relative;
        transition: color 0.3s ease;
        font-size: 18px;
        font-family: "PingFang Medium";
        border-bottom: none;

        &:hover {
          color: #999;
        }

        &.is-active {
          font-family: "PingFang Bold";
        }

        // 鼠标悬停时的下划线动画效果
        &::before {
          content: "";
          position: absolute;
          bottom: -1px;
          left: 0;
          width: 0;
          height: 2px;
          border-radius: 2px;
          background-color: #004c66;
          transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          z-index: 5;
        }

        // 鼠标悬停时触发下划线从左至右的动画
        &:hover::before {
          width: 100%;
        }

        // 确保激活状态的菜单项不显示悬停下划线（因为已有滑动下划线）
        &.is-active::before {
          display: none;
        }
      }

      .menu-item-name {
        width: 72px;
        text-align: justify;
        text-align-last: justify;
        font-size: 18px;
        height: 25px;
        line-height: 25px;
        // font-family: "PingFang Medium";
        &:active {
          // font-family: "PingFang Bold";
        }
      }

      // 滑动下划线样式
      .sliding-underline {
        position: absolute;
        bottom: -1px;
        height: 2px;
        // background: linear-gradient(90deg, #004c66, #0066cc);
        background-color: #004c66;
        border-radius: 2px;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 10;
      }
    }
  }
}
</style>
