import { ref, computed } from "vue";
import { defineStore } from "pinia";

export interface UserInfo {
  apptoken: string;
  avatar: string;
  cnname: string;
  enterqiye: number;
  enteruser: number;
  id: number;
  jifen: string;
  logintime: number;
  mobile: string;
  paypassword: string;
  qiyemingcheng: string;
  vip: number;
}

export const useUserStore = defineStore("user", () => {
  // 用户信息
  const userInfo = ref<UserInfo | null>(null);

  // 登录状态
  const isLoggedIn = computed(() => {
    return userInfo.value !== null;
  });

  // 登录
  const login = (user: UserInfo) => {
    userInfo.value = user;
    // 保存到localStorage
    localStorage.setItem("apptoken", user.apptoken);
    localStorage.setItem("userInfo", JSON.stringify(user));
    localStorage.setItem("isLoggedIn", "true");
  };

  // 登出
  const logout = () => {
    userInfo.value = null;
    // 清除localStorage
    localStorage.removeItem("userInfo");
    localStorage.removeItem("isLoggedIn");
    localStorage.removeItem("apptoken");
  };

  // 初始化用户状态（从localStorage恢复）
  const initUserState = () => {
    const savedUserInfo = localStorage.getItem("userInfo");
    const savedLoginState = localStorage.getItem("isLoggedIn");

    if (savedLoginState === "true" && savedUserInfo) {
      try {
        userInfo.value = JSON.parse(savedUserInfo);
      } catch (error) {
        console.error("解析用户信息失败:", error);
        logout();
      }
    }
  };

  return {
    userInfo,
    isLoggedIn,
    login,
    logout,
    initUserState,
  };
});
