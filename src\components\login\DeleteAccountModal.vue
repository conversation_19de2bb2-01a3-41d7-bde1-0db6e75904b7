<template>
  <!-- 注销账号确认弹窗组件 -->
  <Modal
    v-model="visible"
    title="注销账号"
    width="658px"
    :show-footer="false"
  >
    <div class="delete-account-content">
      <!-- 主要提示信息 -->
      <p class="delete-message">
        您好！若您确认需要注销当前账号，请仔细阅读以下信息：
      </p>

      <!-- 注销后果说明 -->
      <div class="consequence-section">
        <h4 class="section-title danger">一、注销后果</h4>
        <p class="consequence-text danger">
          账号注销后，系统将永久删除您的个人资料、发布内容、互动记录等所有相关数据，且无法恢复。请慎重考虑身份需要保留的信息。
        </p>
      </div>

      <!-- 注销条件说明 -->
      <div class="condition-section">
        <h4 class="section-title">二、注销条件</h4>
        <ul class="condition-list">
          <li>账号无未完成订单，未结清款项或其他未处理</li>
          <li>需通过绑定手机号/邮箱所有权的验证</li>
          <li>注销后30天内若未重新激活，账号将彻底删除关闭</li>
        </ul>
      </div>

      <!-- 特别说明 -->
      <div class="special-section">
        <h4 class="section-title">三、特别说明</h4>
        <ul class="special-list">
          <li>注销不影响第三方平台授权（如需解除除单独操作）</li>
          <li>根据《网络安全法》等法律，我们将继续保留必要信息6个月</li>
        </ul>
      </div>

      <!-- 如有疑问 -->
      <div class="question-section">
        <h4 class="section-title">四、如有疑问</h4>
        <p class="question-text">
          请致电客服(400-XXX-XXXX)或通过官网在线咨询，我们将协助您处理相关事宜。
        </p>
      </div>

      <!-- 感谢语 -->
      <div class="thanks-section">
        <p class="thanks-text">感谢您选择我们的服务，祝您未来一切顺利！</p>
      </div>

      <!-- 按钮 -->
      <div class="button-section">
        <el-button class="confirm-button" @click="handleConfirm" plain :loading="loading"
          >确定</el-button
        >
        <el-button class="cancel-button" type="primary" @click="handleCancel">取消</el-button>
      </div>
    </div>
  </Modal>
</template>

<script setup lang="ts">
import { computed } from "vue";
import Modal from "@/components/Modal.vue";

/**
 * 注销账号确认弹窗组件
 * 用于显示注销账号的详细说明和确认操作
 */

// 组件属性定义
interface Props {
  /** 控制弹窗显示/隐藏 */
  modelValue: boolean;
  /** 确认按钮加载状态 */
  loading?: boolean;
}

// 组件事件定义
interface Emits {
  /** 更新modelValue */
  (e: "update:modelValue", value: boolean): void;
  /** 确认注销账号 */
  (e: "confirm"): void;
  /** 取消注销账号 */
  (e: "cancel"): void;
}

// 接收props
const props = withDefaults(defineProps<Props>(), {
  loading: false,
});

// 定义事件
const emit = defineEmits<Emits>();

// 计算属性：控制弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit("update:modelValue", value),
});

/**
 * 处理确认注销账号
 * 触发confirm事件，由父组件处理具体的注销逻辑
 */
const handleConfirm = () => {
  emit("confirm");
};

/**
 * 处理取消注销账号
 * 关闭弹窗并触发cancel事件
 */
const handleCancel = () => {
  emit("cancel");
  visible.value = false;
};
</script>

<style lang="scss" scoped>
/**
 * 注销账号确认弹窗样式
 * 包含多个信息区块的样式定义
 */
.delete-account-content {
  padding: 20px;
  line-height: 1.6;

  // 主要提示消息样式
  .delete-message {
    font-size: 16px;
    color: #333;
    margin-bottom: 20px;
    font-weight: 500;
  }

  // 各个信息区块的通用样式
  .consequence-section,
  .condition-section,
  .special-section,
  .question-section,
  .thanks-section {
    padding: 0 20px;
    margin-bottom: 20px;

    .danger {
      color: #ef0004;
    }

    // 区块标题样式
    .section-title {
      font-size: 16px;
      color: #333;
      font-weight: 600;
      margin-bottom: 8px;
      margin-top: 0;
    }
  }

  // 注销后果文本样式
  .consequence-text {
    font-size: 14px;
    color: #666;
    margin: 0;
  }

  // 条件列表样式
  .condition-list,
  .special-list {
    margin: 0;
    padding-left: 20px;

    li {
      font-size: 14px;
      color: #666;
      margin-bottom: 4px;
      list-style-type: disc;
    }
  }

  // 疑问文本样式
  .question-text {
    font-size: 14px;
    color: #666;
    margin: 0;
  }

  // 感谢语样式
  .thanks-text {
    font-size: 16px;
    color: #333;
    margin: 0;
    text-align: right;
    font-weight: 500;
  }
}

// 重写按钮颜色，确定按钮为主色调
:deep(.modal-footer) {
  .el-button--primary {
    background-color: #007bff;
    border-color: #007bff;

    &:hover {
      background-color: #0056b3;
      border-color: #0056b3;
    }
  }
}

// 底部按钮区域
.button-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  .el-button{
    width: 112px;
    height: 37px;
  }
  .confirm-button {
    background-color: rgba($color: #004c66, $alpha: 0.2);
    color: #004c66;
    &:hover {
      background-color: rgba($color: #004c66, $alpha: 0.4);
      border: none;
    }
  }

  .cancel-button{
    margin-left: 12px;
  }
}
</style>
