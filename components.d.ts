/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AuctionCard: typeof import('./src/components/AuctionCard.vue')['default']
    AuctionListItem: typeof import('./src/components/AuctionListItem.vue')['default']
    BiddingCard: typeof import('./src/components/BiddingCard.vue')['default']
    Carousel: typeof import('./src/components/Carousel.vue')['default']
    CommentEditor: typeof import('./src/components/CommentEditor.vue')['default']
    CompanyTag: typeof import('./src/components/CompanyTag.vue')['default']
    CustomScrollbar: typeof import('./src/components/CustomScrollbar.vue')['default']
    DataTable: typeof import('./src/components/DataTable/index.vue')['default']
    DeleteAccountModal: typeof import('./src/components/login/DeleteAccountModal.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCarousel: typeof import('element-plus/es')['ElCarousel']
    ElCarouselItem: typeof import('element-plus/es')['ElCarouselItem']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    Example: typeof import('./src/components/FileUpload/example.vue')['default']
    FileUpload: typeof import('./src/components/FileUpload/index.vue')['default']
    FreedomUserFloat: typeof import('./src/components/FreedomUserFloat/index.vue')['default']
    LocalScrollbar: typeof import('./src/components/LocalScrollbar.vue')['default']
    LogoutConfirmModal: typeof import('./src/components/login/LogoutConfirmModal.vue')['default']
    Modal: typeof import('./src/components/Modal.vue')['default']
    NewUploadCard: typeof import('./src/components/NewUploadCard.vue')['default']
    PropertyCard: typeof import('./src/components/PropertyCard.vue')['default']
    RouteLoader: typeof import('./src/components/RouteLoader.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ServiceSupport: typeof import('./src/components/ServiceSupport.vue')['default']
    ServiceSupportExample: typeof import('./src/components/ServiceSupportExample.vue')['default']
    Steps: typeof import('./src/components/Steps/index.vue')['default']
    SupplyDemandDetail: typeof import('./src/components/SupplyDemandDetail/index.vue')['default']
    SupplyDemandDetailModal: typeof import('./src/components/SupplyDemandDetailModal/index.vue')['default']
    SvgIcon: typeof import('./src/components/SvgIcon.vue')['default']
    TiptapEditor: typeof import('./src/components/TiptapEditor/index.vue')['default']
    UploadCard: typeof import('./src/components/UploadCard.vue')['default']
  }
  export interface GlobalDirectives {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
