// 表格组件配置接口

// 表格组件配置接口
export interface TableConfig {
  // 按钮切换栏配置
  buttonBar?: ButtonBarConfig
  // 筛选栏配置
  filterBar?: FilterBarConfig
  // 水平导航栏配置
  tabBar?: TabBarConfig
  // 表格列配置
  columns: TableColumn[]
  // 表格数据
  data?: any[]
  // 分页配置
  pagination?: PaginationConfig
  // 加载状态
  loading?: boolean
  // 表格样式配置
  tableStyle?: TableStyleConfig
}

// 表格样式配置
export interface TableStyleConfig {
  // 表格最大高度
  maxHeight?: number | string
  // 是否显示边框
  border?: boolean
  // 是否显示斑马纹
  stripe?: boolean
  // 是否显示选择列
  showSelection?: boolean
}

// 按钮切换栏配置
export interface ButtonBarConfig {
  show: boolean
  buttons: ButtonItem[]
}

// 按钮项配置
export interface ButtonItem {
  key: string
  label: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text' | 'default'
  icon?: any // 支持 Element Plus 图标组件
  active?: boolean
  size?: 'large' | 'default' | 'small'
  loading?: boolean
}

// 筛选栏配置
export interface FilterBarConfig {
  show: boolean
  filters: FilterItem[]
}

// 筛选项配置
export interface FilterItem {
  key: string
  label: string
  type: 'input' | 'select' | 'date' | 'daterange' | 'cascader'
  placeholder?: string
  options?: SelectOption[]
  value?: any
  width?: string
}

// 选择项配置
export interface SelectOption {
  label: string
  value: any
  children?: SelectOption[]
}

// 水平导航栏配置
export interface TabBarConfig {
  show: boolean
  tabs: TabItem[]
  activeKey?: string
  // 导航栏右侧按钮
  rightButtons?: ButtonItem[]
}

// 导航项配置
export interface TabItem {
  key: string
  label: string
  count?: number
  badge?: string
}

// 操作按钮配置
export interface ActionButton {
  key: string
  label: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text'
  size?: 'large' | 'default' | 'small'
  icon?: string
  disabled?: boolean | ((row: any) => boolean)
  visible?: boolean | ((row: any) => boolean)
}

// 表格列配置
export interface TableColumn {
  key: string
  label: string
  width?: string | number
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  fixed?: 'left' | 'right'
  type?: 'text' | 'image' | 'tag' | 'action' | 'date' | 'money'
  formatter?: (value: any, row: any) => string
  render?: (value: any, row: any) => any
  // 操作列专用配置
  actions?: ActionButton[]
}

// 分页配置
export interface PaginationConfig {
  show: boolean
  current: number
  pageSize: number
  total: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  pageSizes?: number[]
}

// 表格事件接口
export interface TableEvents {
  onButtonClick?: (key: string) => void
  onFilterChange?: (filters: Record<string, any>) => void
  onTabChange?: (key: string) => void
  onPageChange?: (page: number, pageSize: number) => void
  onSortChange?: (column: string, order: 'asc' | 'desc' | null) => void
  onRowClick?: (row: any, index: number) => void
  onRowDoubleClick?: (row: any, index: number) => void
  onSelectionChange?: (selectedRows: any[]) => void
}
