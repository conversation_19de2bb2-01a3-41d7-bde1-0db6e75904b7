<template>
  <div class="divider-test-page">
    <div class="test-header">
      <h2>水平导航栏分割线测试</h2>
      <p>测试不同配置下分割线的显示效果</p>
    </div>
    
    <div class="test-controls">
      <el-checkbox v-model="showButtonBar" @change="updateConfig">显示按钮栏</el-checkbox>
      <el-checkbox v-model="showFilterBar" @change="updateConfig">显示筛选栏</el-checkbox>
      <el-checkbox v-model="showTabBar" @change="updateConfig">显示水平导航栏</el-checkbox>
    </div>
    
    <div class="test-info">
      <h3>分割线显示规则</h3>
      <ul>
        <li>✅ <strong>显示分割线</strong>：当页面中有按钮栏或筛选栏时</li>
        <li>✅ <strong>隐藏分割线</strong>：当页面中只有水平导航栏时</li>
        <li>✅ <strong>分割线颜色</strong>：#dddddd</li>
        <li>✅ <strong>分割线高度</strong>：1px</li>
      </ul>
    </div>
    
    <div class="current-config">
      <h4>当前配置</h4>
      <p>按钮栏：{{ showButtonBar ? '显示' : '隐藏' }}</p>
      <p>筛选栏：{{ showFilterBar ? '显示' : '隐藏' }}</p>
      <p>水平导航栏：{{ showTabBar ? '显示' : '隐藏' }}</p>
      <p>分割线：{{ shouldShowDivider ? '显示' : '隐藏' }}</p>
    </div>
    
    <DataTable :config="tableConfig" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import DataTable from '@/components/DataTable/index.vue'
import type { TableConfig } from '@/types/table'

// 控制变量
const showButtonBar = ref(true)
const showFilterBar = ref(true)
const showTabBar = ref(true)

// 计算是否显示分割线
const shouldShowDivider = computed(() => {
  return showButtonBar.value || showFilterBar.value
})

// 表格配置
const tableConfig = ref<TableConfig>({
  buttonBar: {
    show: showButtonBar.value,
    buttons: [
      { key: 'add', label: '新增', type: 'primary' },
      { key: 'export', label: '导出', type: 'default' }
    ]
  },
  
  filterBar: {
    show: showFilterBar.value,
    filters: [
      {
        key: 'name',
        label: '名称',
        type: 'input',
        placeholder: '请输入名称',
        width: '200px'
      },
      {
        key: 'status',
        label: '状态',
        type: 'select',
        placeholder: '请选择状态',
        width: '150px',
        options: [
          { label: '启用', value: 'active' },
          { label: '禁用', value: 'inactive' }
        ]
      }
    ]
  },
  
  tabBar: {
    show: showTabBar.value,
    activeKey: 'all',
    tabs: [
      { key: 'all', label: '全部' },
      { key: 'active', label: '启用' },
      { key: 'inactive', label: '禁用' }
    ]
  },
  
  columns: [
    { key: 'id', label: 'ID', width: '80px', align: 'center' },
    { key: 'name', label: '名称', width: '200px' },
    { key: 'status', label: '状态', width: '100px', align: 'center', type: 'tag' },
    { key: 'actions', label: '操作', width: '150px', align: 'center', type: 'action' }
  ],
  
  pagination: {
    show: true,
    current: 1,
    pageSize: 10,
    total: 20
  },
  
  data: [
    { id: 1, name: '测试项目1', status: 'active' },
    { id: 2, name: '测试项目2', status: 'inactive' },
    { id: 3, name: '测试项目3', status: 'active' }
  ],
  loading: false,
  
  tableStyle: {
    maxHeight: 300,
    border: true,
    stripe: true,
    showSelection: false
  }
})

// 更新配置
const updateConfig = () => {
  if (tableConfig.value.buttonBar) {
    tableConfig.value.buttonBar.show = showButtonBar.value
  }
  if (tableConfig.value.filterBar) {
    tableConfig.value.filterBar.show = showFilterBar.value
  }
  if (tableConfig.value.tabBar) {
    tableConfig.value.tabBar.show = showTabBar.value
  }
}
</script>

<style scoped lang="scss">
.divider-test-page {
  padding: 20px;
  
  .test-header {
    margin-bottom: 20px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    border-bottom: 1px solid #e5e5e5;
    
    h2 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 24px;
      font-weight: 600;
    }
    
    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }
  
  .test-controls {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    display: flex;
    gap: 20px;
  }
  
  .test-info {
    margin-bottom: 20px;
    padding: 20px;
    background: #f0f8ff;
    border-radius: 8px;
    border-left: 4px solid #004C66;
    
    h3 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 18px;
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        color: #555;
        font-size: 14px;
        
        strong {
          color: #004C66;
        }
      }
    }
  }
  
  .current-config {
    margin-bottom: 20px;
    padding: 16px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    
    h4 {
      margin: 0 0 8px 0;
      color: #856404;
      font-size: 16px;
    }
    
    p {
      margin: 4px 0;
      color: #856404;
      font-size: 14px;
    }
  }
}
</style>
