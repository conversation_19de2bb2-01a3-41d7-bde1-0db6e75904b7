# 新API配置使用说明

## 概述

为了支持新的后端服务，我们创建了新的HTTP配置和API封装，新的服务地址为：`http://************:8080/jeecg-boot`

## 文件说明

- `http-new.ts`: 新的HTTP请求配置，专门用于新的后端服务
- `api-new.ts`: 新的API接口封装，包含新功能相关的API方法
- `index.ts`: 统一导出文件，方便模块引用

## 使用方法

### 1. 直接使用新的HTTP实例

```typescript
import httpNew from '@/utils/http-new'

// 发送GET请求
const response = await httpNew.get('/api/endpoint')

// 发送POST请求
const response = await httpNew.post('/api/endpoint', data)
```

### 2. 使用封装好的API方法

```typescript
import { newApi } from '@/utils/api-new'
// 或者
import newApi from '@/utils/api-new'

// 获取用户信息
const userInfo = await newApi.getUserInfo({ id: 123 })

// 获取数据列表
const dataList = await newApi.getDataList({ page: 1, size: 10 })
```

### 3. 通过统一导出使用

```typescript
import { httpNew, newApi } from '@/utils'

// 使用新的HTTP实例
const response = await httpNew.get('/api/endpoint')

// 使用新的API方法
const data = await newApi.getUserInfo({ id: 123 })
```

## 特点

1. **独立配置**: 新的HTTP配置与原有配置完全独立，不会影响现有功能
2. **无Token校验**: 新的API暂时不校验token，适合无需认证的接口
3. **统一错误处理**: 继承了原有的错误处理机制，保持用户体验一致
4. **类型安全**: 提供了完整的TypeScript类型定义

## 扩展

如需添加新的API方法，请在 `api-new.ts` 文件中的 `newApi` 对象中添加相应的方法。

```typescript
export const newApi = {
  // 现有方法...
  
  /**
   * 新的API方法
   * @param {object} params - 请求参数
   * @returns {Promise<ApiResponse>} 响应数据
   */
  newMethod(params: BaseParams): Promise<ApiResponse> {
    return httpNew
      .post('/api/new-endpoint', params)
      .then((res) => res.data);
  },
};
```

## 注意事项

1. 新的API配置暂时不包含token认证，如后续需要，请在 `http-new.ts` 的请求拦截器中添加
2. 请确保新的后端服务地址 `http://************:8080/jeecg-boot` 可正常访问
3. 建议在开发环境中先测试新的API配置，确保功能正常后再部署到生产环境