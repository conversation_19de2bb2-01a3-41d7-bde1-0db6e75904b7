<template>
  <div class="advantage-card" :class="cardClass">
    <!-- 图片区域 -->
    <div class="image-section">
      <img :src="imgUrl" alt="" class="card-icon" />
    </div>

    <!-- 内容区域 -->
    <div class="content-section">
      <!-- <div class="card-number">{{ formattedNumber }}</div> -->
      <div class="card-title">
        <span class="card-title-text">{{ title }}</span>
        <h4 class="card-subtitle">{{ subtitle }}</h4>
        <p class="card-description">{{ description }}</p>
      </div>
      <span class="card-number">{{ formattedNumber }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

// 定义组件属性接口
interface Props {
  /** 卡片序号 */
  number: number;
  /** 卡片标题 */
  title: string;
  /** 卡片副标题 */
  subtitle: string;
  /** 卡片描述 */
  description: string;
  /** 图片地址 */
  imgUrl: string;
}

// 定义属性
const props = defineProps<Props>();

// 计算卡片样式类名
const cardClass = computed(() => {
  const isEven = props.number % 2 === 0;
  return {
    "card-even": isEven,
    "card-odd": !isEven,
  };
});

// 格式化序号显示
const formattedNumber = computed(() => {
  return props.number.toString().padStart(2, "0");
});
</script>

<style scoped lang="scss">
.advantage-card {
  display: flex;
  height: 279px;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;

  // 悬停动画效果
  &:hover {
    transform: scale(1.02);

    .card-icon {
      transform: scale(1.02);
    }
  }

  // 奇数卡片样式（白色背景，图片在左侧）
  &.card-odd {
    background-color: #ffffff;
    box-shadow: 0px 0px 15px rgba(85, 85, 85, 0.1);
    border-radius: 10px;

    .image-section {
      order: 1;
      background: linear-gradient(
        135deg,
        transparent 0%,
        transparent 46%,
        #ffffff 46%
      );
    }

    .content-section {
      order: 2;
      color: #333333;
      padding-top: 51px;
      padding-left: 39px;
      padding-right: 63.3px;

      .card-number {
        color: #004c66;
      }
    }

    .card-title {
      color: #004c66;
    }

    .card-subtitle,
    .card-description {
      color: #666;
    }
  }

  // 偶数卡片样式（深蓝色背景，图片在右侧）
  &.card-even {
    background-color: #004c66;
    box-shadow: 0px 0px 15px rgba(85, 85, 85, 0.1);
    border-radius: 10px;

    .image-section {
      order: 2;
      background: linear-gradient(
        225deg,
        transparent 0%,
        transparent 46%,
        #004c66 46%
      );
    }

    .content-section {
      order: 1;
      color: #ffffff;
      padding-top: 51px;
      padding-left: 72.3px;
      padding-right: 11px;

      .card-number {
        position: relative;
        top: -16px;
        color: #ffffff;
      }
    }

    .card-title {
      color: #fff;
    }

    .card-subtitle {
      color: #eee;
    }

    .card-description {
      color: #ddd;
    }
  }
}

.image-section {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .card-icon {
    width: 590px;
    height: 279px;
    z-index: 2;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.content-section {
  width: 53%;
  display: flex;

  .card-title {
    font-size: 24px;
    display: flex;
    flex-direction: column;
    .card-title-text {
      font-size: 38px;
      font-family: "FZZongYi-M05S";
      line-height: 1.5;
    }
  }

  .card-number {
    font-size: 70px;
    font-family: "DIN BlackItalic";
    line-height: 1;
    color: #004c66;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .card-subtitle {
    font-size: 16px;
    font-weight: 400;
    margin: 14px 0 25px 0;
    opacity: 0.8;
    line-height: 1.3;
  }

  .card-description {
    font-size: 16px;
    margin: 0;
    opacity: 0.9;
  }
}

// 响应式设计
@media (max-width: 1440px) {
  .content-section {
    padding: 30px 25px;

    .card-number {
      font-size: 42px;
    }

    .card-title {
      font-size: 22px;
    }
  }

  /* .image-section .card-icon {
    width: 100px;
    height: 100px;
  } */
  .card-icon {
    width: 442.5px;
    height: 209.25px;
  }
}

@media (max-width: 1200px) {
  .content-section {
    padding: 25px 20px;

    .card-number {
      font-size: 36px;
    }

    .card-title {
      font-size: 20px;
    }

    .card-subtitle {
      font-size: 15px;
    }

    .card-description {
      font-size: 13px;
    }
  }

  /* .image-section .card-icon {
    width: 80px;
    height: 80px;
  } */
  .card-icon {
    width: 368.75px;
    height: 174.375px;
  }
}
</style>
