<template>
  <!-- 隐藏的固定header区域 -->
  <div class="fixed-header">
    <!-- 搜索区域 -->
    <div v-if="showSearchContainer && !isScrolled" class="search-container">
      <input
        v-model="searchKeyword"
        type="text"
        class="search-input"
        :placeholder="searchPlaceholder"
        :autofocus="true"
        @keyup.enter="handleSearch"
      />
      <button class="search-button" @click="handleSearch">
        <SvgIcon iconName="search" className="search-icon" />
        <span>搜 索</span>
      </button>
    </div>
  </div>

  <!-- 原本的header区域 -->
  <header
    :class="['header', { 'recommend-header': isRecommendPage }]"
    :style="{ opacity: headerOpacity }"
  >
    <div class="header-container">
      <!-- 导航菜单 -->
      <div v-show="!isScrolled" class="nav-container">
        <div
          class="nav-item"
          :class="{ active: currentRoute === '/' }"
          @click="(event) => navigateTo('/', event)"
        >
          <SvgIcon iconName="home" className="nav-icon" />
          <span>推荐专区</span>
        </div>
        <div
          class="nav-item"
          :class="{ active: currentRoute.includes('/bidding') }"
          @click="(event) => navigateTo('/bidding/biddingHome', event)"
        >
          <SvgIcon iconName="bidding" className="nav-icon" />
          <span>竞价交易</span>
        </div>
        <div
          class="nav-item"
          :class="{ active: currentRoute.includes('/freedom') }"
          @click="(event) => navigateTo('/freedom/freedomHome', event)"
        >
          <SvgIcon iconName="freedom" className="nav-icon" />
          <span>自由交易</span>
        </div>
        <div
          class="nav-item"
          :class="{ active: currentRoute.includes('/announcement') }"
          @click="(event) => handleCompanyClick(event)"
        >
          <SvgIcon iconName="company" className="nav-icon" />
          <span>公告信息</span>
        </div>
        <!-- <div
          class="nav-item"
          :class="{ active: currentRoute === '/exclusive' }"
          @click="(event) => handleExclusiveClick(event)"
        >
          <SvgIcon iconName="hg-exclusive" className="nav-icon" />
          <span>灰谷知享</span>
        </div> -->
      </div>

      <!-- 推荐专区特有的标题区域 -->
      <div v-if="isRecommendPage && !isScrolled" class="recommend-title">
        <div class="title-text">企业物资服务平台</div>
        <div class="recommend-desc" @click="handleAboutUsClick">
          <p>灰谷专注于国企 央企 大型企业物资服务</p>
          <SvgIcon iconName="link" className="link-icon" />
        </div>
      </div>

      <!-- 搜索区域 -->
      <div v-if="showSearchContainer && !isScrolled" class="search-container">
        <input
          v-model="searchKeyword"
          type="text"
          class="search-input"
          :placeholder="searchPlaceholder"
          :autofocus="true"
          @keyup.enter="handleSearch"
        />
        <button class="search-button" @click="handleSearch">
          <SvgIcon iconName="search" className="search-icon" />
          <span>搜 索</span>
        </button>
      </div>
    </div>
  </header>

  <main>
    <!-- Logo和用户操作区域 -->
    <div class="logo-container">
      <div class="logo-title-container">
        <img
          src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/logo_1754960593997.png"
          class="logo-icon"
          @click="(event:any)=>{navigateTo('/about',event)}"
        />
        <!-- <SvgIcon iconName="title" className="title-icon" /> -->
      </div>

      <div
        v-if="showSearchContainer && isScrolled"
        class="scrolled-search-container"
      >
        <input
          v-model="searchKeyword"
          type="text"
          class="search-input"
          :placeholder="searchPlaceholder"
          :autofocus="true"
          @keyup.enter="handleSearch"
        />
        <button class="search-button" @click="handleSearch">
          <SvgIcon iconName="search" className="search-icon" />
          <span>搜 索</span>
        </button>
      </div>

      <div class="user-container">
        <!-- 语言切换下拉菜单 -->
        <!-- <el-dropdown>
            <span class="user-dropdown">
              <SvgIcon iconName="language" className="language-icon" />
              <span>中文</span>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>中文</el-dropdown-item>
                <el-dropdown-item>English</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown> -->
        <!-- 登录按钮/用户信息 - 在登录页面隐藏 -->
        <div v-if="currentRoute !== '/login'" class="user-auth-container">
          <!-- 未登录状态 -->
          <el-button v-if="!userStore.isLoggedIn" @click="handleLogin">
            <SvgIcon iconName="user" className="user-icon" />
            <span>登录/注册</span>
          </el-button>

          <!-- 已登录状态 -->
          <div v-else class="user-dropdown-container" ref="userDropdownRef">
            <!-- 用户头像触发器 -->
            <div class="user-avatar" @click="toggleUserDropdown">
              <SvgIcon iconName="user" className="avatar-icon" />
            </div>

            <!-- 自定义用户信息弹出框 -->
            <!-- 使用Teleport将弹出框传送到body下，避免被layout容器的层叠上下文限制 -->
            <Teleport to="body">
              <div
                v-show="showUserDropdown"
                class="custom-user-dropdown"
                @click.stop
              >
                <!-- 上半部分：用户信息区域 -->
                <div class="user-info-section">
                  <div class="user-info-avatar">
                    <SvgIcon iconName="user" className="info-avatar-icon" />
                  </div>
                  <div class="user-info-content">
                    <span class="user-info-name">{{
                      userStore.userInfo?.cnname
                    }}</span>
                    <span class="user-info-phone">{{
                      userStore.userInfo?.mobile
                    }}</span>
                  </div>
                </div>

                <!-- 下半部分：菜单列表区域 -->
                <div class="menu-list-section">
                  <div class="menu-item" @click="handleUserCommand('profile')">
                    <SvgIcon iconName="login-menu-home" className="menu-icon" />
                    <span>我的资料</span>
                  </div>
                  <!-- <div
                      class="menu-item"
                      @click="handleUserCommand('account')"
                    >
                      <SvgIcon
                        iconName="login-menu-account"
                        className="menu-icon"
                      />
                      <span>我的账户</span>
                    </div>
                    <div
                      class="menu-item"
                      @click="handleUserCommand('messages')"
                    >
                      <SvgIcon
                        iconName="login-menu-message"
                        className="menu-icon"
                      />
                      <span>我的消息</span>
                    </div> -->
                  <div class="menu-item" @click="handleUserCommand('auction')">
                    <SvgIcon
                      iconName="login-menu-auction"
                      className="menu-icon"
                    />
                    <span>竞拍管理</span>
                  </div>
                  <!-- 分割线 -->
                  <div class="menu-divider"></div>
                  <!-- <div class="menu-item identity-item">
                      <div class="identity-content">
                        <div class="identity-header">
                          <SvgIcon
                            iconName="login-menu-identity"
                            className="menu-icon"
                          />
                          <span>身份切换</span>
                        </div>
                        <div class="identity-buttons">
                          <button
                            class="identity-btn"
                            :class="{ selected: selectedIdentity === 'buyer' }"
                            @click.stop="selectIdentity('buyer')"
                          >
                            设定人
                            <div
                              v-if="selectedIdentity === 'buyer'"
                              class="check-icon"
                            >
                              <SvgIcon
                                iconName="login-menu-check"
                                className="check-svg"
                              />
                            </div>
                          </button>
                          <button
                            class="identity-btn"
                            :class="{
                              selected: selectedIdentity === 'enterprise',
                            }"
                            @click.stop="selectIdentity('enterprise')"
                          >
                            处置企业
                            <div
                              v-if="selectedIdentity === 'enterprise'"
                              class="check-icon"
                            >
                              <SvgIcon
                                iconName="login-menu-check"
                                className="check-svg"
                              />
                            </div>
                          </button>
                        </div>
                      </div>
                    </div> -->
                  <div
                    class="menu-item logout-item"
                    @click="handleUserCommand('logout')"
                  >
                    <SvgIcon
                      iconName="login-menu-logout"
                      className="menu-icon logout-icon"
                    />
                    <span>退出登录</span>
                  </div>
                </div>
              </div>
            </Teleport>
          </div>
        </div>
      </div>
    </div>

    <!-- 子导航区域 -->
    <div
      v-if="showSubNavigation"
      :class="['sub-navigation', { sticky: isSubNavSticky }]"
    >
      <div class="sub-nav-content">
        <el-menu
          :default-active="subActiveIndex"
          class="sub-nav-menu"
          mode="horizontal"
          :ellipsis="false"
          @select="handleSubNavSelect"
          background-color="#f2f2f2"
          text-color="#999"
          active-text-color="#004C66"
        >
          <el-menu-item
            v-for="item in currentSubNavItems"
            :key="item.path"
            :index="item.path"
            :data-path="item.path"
            class="sub-menu-item-wrapper"
          >
            <span class="sub-menu-item-name">
              {{ item.name }}
            </span>
          </el-menu-item>

          <!-- 滑动下划线 -->
          <div
            class="sub-sliding-underline"
            :style="{
              left: subUnderlineStyle.left,
              width: subUnderlineStyle.width,
            }"
          ></div>
        </el-menu>
      </div>
    </div>
  </main>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import SvgIcon from "@/components/SvgIcon.vue";
import { useUserStore } from "@/stores/user";

// 定义组件props
interface Props {
  /** 是否为推荐页面模式 */
  isRecommendPage?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isRecommendPage: false,
});

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

// 搜索关键词
const searchKeyword = ref<string>("");

// 用户下拉框状态管理
const showUserDropdown = ref(false);
const userDropdownRef = ref<HTMLDivElement | null>(null);

// 身份切换状态管理
const selectedIdentity = ref<"buyer" | "enterprise">("buyer");

// 滚动状态管理
const isScrolled = ref(false);
const showSearchContainer = ref(true); // 控制搜索组件的显示

// Header透明度管理
const headerOpacity = ref(1);

// 子导航吸附状态管理
const isSubNavSticky = ref(false);

// 语言管理
const currentLanguage = ref("中文");

// 语言切换处理
const handleLanguageChange = (command: string) => {
  if (command === "zh") {
    currentLanguage.value = "中文";
  } else if (command === "en") {
    currentLanguage.value = "English";
  }
  ElMessage.success(`语言已切换为${currentLanguage.value}`);
};

// 子导航相关状态
const subActiveIndex = ref("");
const subUnderlineStyle = ref({ left: "0px", width: "0px" });

// 当前路由路径
const currentRoute = computed(() => route.path);

// 子导航项接口
interface SubNavItem {
  name: string;
  path: string;
  activeIndex: string;
  active: boolean;
}

// 竞价交易子导航
const biddingNavItems: SubNavItem[] = [
  {
    name: "首页",
    path: "/bidding/biddingHome",
    activeIndex: "biddingHome",
    active: true,
  },
  {
    name: "标的信息",
    path: "/bidding/biddingInfo",
    activeIndex: "biddingInfo",
    active: false,
  },
  {
    name: "企业合作",
    path: "/bidding/biddingEnterprise",
    activeIndex: "biddingEnterprise",
    active: false,
  },
  {
    name: "服务支持",
    path: "/bidding/biddingSupport",
    activeIndex: "biddingSupport",
    active: false,
  },
];

// 自由交易子导航
const freedomNavItems: SubNavItem[] = [
  {
    name: "首页",
    path: "/freedom/freedomHome",
    activeIndex: "freedomHome",
    active: true,
  },
  {
    name: "服务支持",
    path: "/freedom/freedomSupport",
    activeIndex: "freedomSupport",
    active: false,
  },
];

// 公告信息子导航
const announcementNavItems: SubNavItem[] = [
  {
    name: "公告信息",
    path: "/announcement/list",
    activeIndex: "announcementInfo-list",
    active: true,
  },
  {
    name: "服务支持",
    path: "/announcement/support",
    activeIndex: "announcementInfo-support",
    active: false,
  },
];

// 是否显示子导航
const showSubNavigation = computed(() => {
  // 竞拍管理页面不显示子导航
  if (currentRoute.value.includes("/biddingManage")) {
    return false;
  }
  // freedom-login页面不显示子导航
  if (currentRoute.value.includes("/freedom-login")) {
    return false;
  }
  // 公告详情页面不显示子导航
  if (currentRoute.value.includes("/announcement/detail")) {
    return false;
  }
  return (
    currentRoute.value.includes("/bidding") ||
    currentRoute.value.includes("/freedom") ||
    currentRoute.value.includes("/announcement")
  );
});

// 当前子导航项
const currentSubNavItems = computed(() => {
  if (currentRoute.value.includes("/bidding")) {
    return biddingNavItems;
  } else if (currentRoute.value.includes("/freedom")) {
    return freedomNavItems;
  } else if (currentRoute.value.includes("/announcement")) {
    return announcementNavItems;
  }
  return [];
});

// 搜索框占位符文本
const searchPlaceholder = computed(() => {
  return props.isRecommendPage
    ? "闲置资产拍卖 · 二手设备拍卖 · 废旧物资拍卖 · 整厂拆除"
    : "闲置资产拍卖 · 二手设备拍卖 · 废旧物资拍卖 · 整厂拆除";
});

/**
 * 导航到指定路径
 * @param path 目标路径
 * @param event 点击事件
 */
const navigateTo = (path: string, event: Event) => {
  // 获取点击的导航项元素
  const navItem = event.currentTarget as HTMLElement;

  // 添加点击动画
  navItem.style.animation = "none";
  // 触发重绘
  void navItem.offsetWidth;
  // 应用点击后的动画
  navItem.style.animation = "clickPulse 0.5s ease";

  // 导航到新路径
  router.push(path);
};

/**
 * 跳转到登录页面
 */
const handleLogin = () => {
  router.push("/login");
};

/**
 * 跳转到关于我们页面
 */
const handleAboutUsClick = () => {
  router.push("/about");
};

/**
 * 处理公告信息点击事件
 * @param event 点击事件
 */
const handleCompanyClick = (event: Event) => {
  // 获取点击的导航项元素
  const navItem = event.currentTarget as HTMLElement;

  // 添加点击动画
  navItem.style.animation = "none";
  // 触发重绘
  void navItem.offsetWidth;
  // 应用点击后的动画
  navItem.style.animation = "clickPulse 0.5s ease";

  // 跳转到公告信息页面
  router.push("/announcement/list");
};

/**
 * 处理灰谷知享点击事件
 * @param event 点击事件
 */
const handleExclusiveClick = (event: Event) => {
  // 阻止默认行为
  event.preventDefault();

  // 获取点击的导航项元素
  const navItem = event.currentTarget as HTMLElement;

  // 添加点击动画
  navItem.style.animation = "none";
  // 触发重绘
  void navItem.offsetWidth;
  // 应用点击后的动画
  navItem.style.animation = "clickPulse 0.5s ease";

  // 显示功能开发中提示
  ElMessage.info("灰谷知享功能开发中，敬请期待...");
};

// 切换用户下拉框显示状态
const toggleUserDropdown = () => {
  showUserDropdown.value = !showUserDropdown.value;
};

// 关闭用户下拉框
const closeUserDropdown = () => {
  showUserDropdown.value = false;
};

// 点击外部关闭下拉框
const handleClickOutside = (event: Event) => {
  if (
    userDropdownRef.value &&
    !userDropdownRef.value.contains(event.target as Node)
  ) {
    closeUserDropdown();
  }
};

// 页面滚动时关闭下拉框和处理Header状态
const handleScroll = () => {
  if (showUserDropdown.value) {
    closeUserDropdown();
  }

  // 检测滚动位置，计算透明度
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

  // 根据滚动距离计算透明度，滚动200px时完全透明
  // 判断是普通页面还是推荐页面
  let maxScrollDistance = 92;
  if (props.isRecommendPage) {
    maxScrollDistance = 520;
  }
  const opacity = Math.max(0, 1 - scrollTop / maxScrollDistance);
  headerOpacity.value = opacity;

  // 处理子导航吸附逻辑
  if (showSubNavigation.value) {
    // 计算子导航原始位置
    let subNavOriginalTop = 195; // 默认header高度
    if (props.isRecommendPage) {
      subNavOriginalTop = 580; // 推荐页面header高度
      // 响应式调整
      if (window.innerWidth <= 1600) {
        subNavOriginalTop = 510;
      }
      if (window.innerWidth <= 1500) {
        subNavOriginalTop = 410;
      }
    }

    // 当滚动距离超过 (子导航原始位置 - 92px) 时，启用吸附
    const stickyThreshold = subNavOriginalTop - 92;
    isSubNavSticky.value = scrollTop >= stickyThreshold;
  }
};

// 选择身份类型
const selectIdentity = (identity: "buyer" | "enterprise") => {
  selectedIdentity.value = identity;
  ElMessage.success(
    `已切换到${identity === "buyer" ? "设定人" : "处置企业"}身份`
  );
};

/**
 * 更新子导航下划线位置
 */
const updateSubUnderlinePosition = (targetPath?: string) => {
  nextTick(() => {
    // 使用传入的路径或当前subActiveIndex值
    const pathToFind = targetPath || subActiveIndex.value;
    const activeMenuItem = document.querySelector(
      `.sub-nav-menu [data-path="${pathToFind}"]`
    ) as HTMLElement;
    if (activeMenuItem) {
      const menuContainer = activeMenuItem.parentElement;
      if (menuContainer) {
        const containerRect = menuContainer.getBoundingClientRect();
        const activeRect = activeMenuItem.getBoundingClientRect();
        const left = activeRect.left - containerRect.left;
        subUnderlineStyle.value = {
          left: `${left}px`,
          width: `${activeRect.width}px`,
        };
      }
    }
  });
};

/**
 * 处理子导航选择
 */
const handleSubNavSelect = (key: string) => {
  // 先基于目标路径更新下划线位置，避免DOM查询混乱
  updateSubUnderlinePosition(key);
  // 然后更新subActiveIndex
  subActiveIndex.value = key;
  // 最后进行路由跳转
  router.push(key);
};

/**
 * 处理搜索功能
 */
const handleSearch = () => {
  const keyword = searchKeyword.value.trim();

  // 如果搜索关键词为空，提示用户
  if (!keyword) {
    ElMessage.warning("请输入搜索关键词");
    return;
  }

  const currentPath = route.path;

  // 根据当前页面路由决定搜索行为
  if (currentPath === "/" || currentPath.includes("/bidding")) {
    // 推荐专区和竞价交易：跳转到标的信息页面
    router.push({
      path: "/bidding/biddingInfo",
      query: { keyword },
    });
  } else if (currentPath.includes("/freedom")) {
    // 自由交易：在当前页面内搜索，通过事件总线或状态管理传递搜索关键词
    // 这里使用路由查询参数的方式
    router.push({
      path: "/freedom/freedomHome",
      query: { keyword },
    });
  } else if (currentPath.includes("/company")) {
    // 企业专区：暂时不处理
    ElMessage.info("企业专区搜索功能开发中...");
  } else if (currentPath.includes("/exclusive")) {
    // 灰谷知享：暂时不处理
    ElMessage.info("灰谷知享搜索功能开发中...");
  } else if (currentPath.includes("/announcement")) {
    // 公告信息：暂时不处理
    ElMessage.info("公告信息搜索功能开发中...");
  } else {
    // 其他页面：默认跳转到标的信息页面
    router.push({
      path: "/bidding/biddingInfo",
      query: { keyword },
    });
  }

  // 清空搜索框
  searchKeyword.value = "";
};

/**
 * 处理用户下拉菜单命令
 */
const handleUserCommand = (command: string) => {
  closeUserDropdown(); // 点击菜单项后关闭下拉框

  switch (command) {
    case "profile":
      router.push("/profile");
      break;
    case "account":
      ElMessage.info("我的账户功能开发中...");
      break;
    case "messages":
      ElMessage.info("我的消息功能开发中...");
      break;
    case "auction":
      router.push("/biddingManage");
      break;
    case "identity":
      ElMessage.info("身份切换功能开发中...");
      break;
    case "logout":
      userStore.logout();
      ElMessage.success("已退出登录");
      // 如果当前在需要登录的页面，跳转到首页
      if (route.meta?.requiresAuth) {
        router.push("/");
      }
      break;
    default:
      break;
  }
};

// 组件挂载时初始化用户状态和事件监听
onMounted(() => {
  userStore.initUserState();
  document.addEventListener("click", handleClickOutside);
  window.addEventListener("scroll", handleScroll);

  // 初始化子导航状态和body类
  const currentPath = route.path;
  const hasSubNav =
    currentPath.includes("/bidding") || 
    currentPath.includes("/freedom") || 
    currentPath.includes("/announcement");

  if (hasSubNav) {
    subActiveIndex.value = currentPath;
    document.body.classList.add("has-sub-nav");
    nextTick(() => {
      updateSubUnderlinePosition();
    });
  } else {
    document.body.classList.remove("has-sub-nav");
  }
});

// 监听路由变化，处理子导航状态
watch(
  () => route.path,
  (newPath, oldPath) => {
    isSubNavSticky.value = false;
    // 获取新旧路径的主路由部分（去掉子路由）
    const getMainRoute = (path: string) => {
      if (path.includes("/bidding")) return "/bidding";
      if (path.includes("/freedom")) return "/freedom";
      if (path.includes("/announcement")) return "/announcement";
      return path;
    };

    const newMainRoute = getMainRoute(newPath);
    const oldMainRoute = oldPath ? getMainRoute(oldPath) : "";

    // 只有主路由发生变化时才重新挂载搜索组件
    if (newMainRoute !== oldMainRoute) {
      showSearchContainer.value = false;
      nextTick(() => {
        showSearchContainer.value = true;
      });
    }

    // 更新子导航状态和body类
    const hasSubNav =
      newPath.includes("/bidding") || 
      newPath.includes("/freedom") || 
      newPath.includes("/announcement");

    if (hasSubNav) {
      subActiveIndex.value = newPath;
      document.body.classList.add("has-sub-nav");
      nextTick(() => {
        updateSubUnderlinePosition();
      });
    } else {
      document.body.classList.remove("has-sub-nav");
    }
  }
);

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
  window.removeEventListener("scroll", handleScroll);

  // 清理body上的状态类
  document.body.classList.remove("has-sub-nav");
});
</script>

<style lang="scss" scoped>
/* 固定的隐藏header区域 */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 92px;
  background: linear-gradient(180deg, #004c66 0%, #aac0c8 100%);
  z-index: 100;
  display: flex;
  align-items: center;
  .search-container {
    margin-top: 0;
  }
}

/* 原本的header区域 */
.header {
  width: 100vw;
  height: 192px;
  min-height: 192px;
  background: linear-gradient(180deg, #004c66 0%, #aac0c8 100%);
  color: #fff;
  position: relative; // 改为相对定位，不脱离文档流
  top: 0;
  left: 0;
  z-index: 1000;
  transition: opacity 0.3s ease;
  // 高度变化的动画
  transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    min-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding-top: 70px;

  // 响应式设计：超小屏幕
  @media (max-width: 768px) {
    height: 240px;
  }

  // 预加载背景图片，避免切换时的闪烁
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/recommend-bg_1754902625553.jpg");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    pointer-events: none;
    z-index: -1;
    // 添加背景图片透明度的平滑过渡动画，使用缓动函数实现渐变效果
    transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  // 背景渐变层，用于实现背景色到背景图的平滑过渡
  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, #004c66 0%, #f2f2f2 142.31%);
    opacity: 1;
    pointer-events: none;
    z-index: -2;
    // 背景色层的透明度过渡，与背景图片形成互补效果
    transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  // 推荐页面样式
  &.recommend-header {
    // 推荐页面的高度，从195px逐渐增长到580px
    height: 580px;
    // min-height: 580px; // 确保最小高度也发生变化，使过渡效果更明显
    transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      min-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    // 推荐页面时显示预加载的背景图片，隐藏背景色
    &::before {
      opacity: 1; // 显示背景图片
    }

    &::after {
      opacity: 0; // 隐藏背景色渐变
    }

    // 媒体查询
    @media (max-width: 1600px) {
      height: 510px;
      background-image: url("https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/recommend-bg-medium_1754902793261.jpg");
    }

    // 媒体查询
    @media (max-width: 1500px) {
      height: 410px;
      background-image: url("https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/recommend-bg-mini_1754902847203.jpg");
    }
  }
}

// 子导航样式
.sub-navigation {
  width: 100%;
  height: 45px;
  background-color: #f2f2f2;
  border-bottom: 1px solid #ddd;
  position: relative;
  top: 0;
  left: 0;
  z-index: 999;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;

  // 吸附状态样式
  &.sticky {
    position: fixed;
    top: 92px; // 吸附在距离顶部92px的位置
    left: 0;
    width: 100%;
    z-index: 1001; // 确保在固定header之上
  }

  .sub-nav-content {
    max-width: 1280px;
    height: 100%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;

    // 修改el-menu-item的hover样式，去掉鼠标移入时的背景色
    :deep(.el-menu--horizontal) .el-menu-item:not(.is-disabled):hover {
      background-color: transparent;
    }

    :deep(.el-menu--horizontal) .el-menu-item:not(.is-disabled):focus {
      background-color: transparent;
    }

    // 隐藏菜单原本的下划线
    .el-menu--horizontal {
      .el-menu-item.is-active {
        border-bottom: none;
      }
    }

    .sub-nav-menu {
      border: none;
      width: 100%;
      height: 45px;
      background-color: #f2f2f2;
      position: relative;
      border-bottom: 1px solid #ddd;

      :deep(.el-menu-item) {
        padding: 0;
        margin-right: 72px;
        position: relative;
        transition: color 0.3s ease;
        font-size: 18px;
        font-family: "PingFang Medium";
        border-bottom: none;

        &:hover {
          color: #999;
        }

        &.is-active {
          font-family: "PingFang Bold";
        }

        // 鼠标悬停时的下划线动画效果
        &::before {
          content: "";
          position: absolute;
          bottom: -1px;
          left: 0;
          width: 0;
          height: 2px;
          border-radius: 2px;
          background-color: #004c66;
          transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          z-index: 5;
        }

        // 鼠标悬停时触发下划线从左至右的动画
        &:hover::before {
          width: 100%;
        }

        // 确保激活状态的菜单项不显示悬停下划线（因为已有滑动下划线）
        &.is-active::before {
          display: none;
        }
      }

      .sub-menu-item-name {
        width: 72px;
        text-align: justify;
        text-align-last: justify;
        font-size: 18px;
        height: 25px;
        line-height: 25px;
      }

      // 滑动下划线样式
      .sub-sliding-underline {
        position: absolute;
        bottom: -1px;
        height: 2px;
        background-color: #004c66;
        border-radius: 2px;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 10;
      }
    }
  }
}

// 子导航位置调整
.sub-navigation {
  // 默认位置：固定在原header高度下方
  &:not(.sticky) {
    top: 0;
  }

  // 推荐页面时的位置
  .recommend-header + &:not(.sticky) {
    top: 580px;

    @media (max-width: 1600px) {
      top: 510px;
    }

    @media (max-width: 1500px) {
      top: 410px;
    }
  }

  // 吸附状态时始终保持在92px位置，不受页面类型影响
  &.sticky {
    top: 92px !important;
  }
}

.header-container {
  display: flex;
  flex-direction: column;
  width: 100%;

  // 响应式设计：中等屏幕
  @media (max-width: 1400px) {
  }

  // 响应式设计：小屏幕
  @media (max-width: 1200px) {
    overflow-x: hidden;
  }

  // 响应式设计：超小屏幕
  @media (max-width: 768px) {
    padding-bottom: 20px;
  }
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20px 0 0 50px;
  animation-duration: 0.8s;
  animation-fill-mode: both;
  animation-delay: 0ms;
  animation-name: fadeInDown;
  transition: margin 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 50px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  opacity: 1 !important;
  pointer-events: none; /* 让logo容器不阻挡下层元素的点击事件 */

  // 滚动状态下的logo容器样式
  /* .scrolled-header & {
    margin-top: 10px;
  } */

  // 响应式设计：中等屏幕
  @media (max-width: 1400px) {
    margin: 20px 0 0 30px;
  }

  // 响应式设计：小屏幕
  @media (max-width: 1200px) {
    margin: 15px 0 0 20px;
    gap: 10px;
  }

  // 响应式设计：超小屏幕
  @media (max-width: 768px) {
    margin: 10px 0 0 15px;
  }

  .logo-title-container {
    display: flex;
    align-items: end;
    pointer-events: auto; /* 恢复logo区域的点击事件 */

    // 响应式设计：小屏幕下调整logo尺寸
    @media (max-width: 1200px) {
      .logo-icon {
        width: 180px;
        height: 31px;
      }

      .title-icon {
        width: 110px;
        height: 15px;
      }
    }

    // 响应式设计：超小屏幕下进一步调整
    @media (max-width: 768px) {
      .logo-icon {
        width: 80px;
        height: 27px;
      }

      .title-icon {
        width: 100px;
        height: 14px;
      }
    }
  }

  .logo-icon {
    width: 251px;
    height: 37px;
    margin-right: 8.82px;
    cursor: pointer;
  }

  .title-icon {
    width: 132.79px;
    height: 18.29px;
  }

  .user-container {
    display: flex;
    align-items: center;
    margin-right: 50px;
    color: #004c66;
    font-size: 14px;
    pointer-events: auto; /* 恢复用户操作区域的点击事件 */

    // 响应式设计：中等屏幕
    @media (max-width: 1400px) {
      margin-right: 30px;
    }

    // 响应式设计：小屏幕
    @media (max-width: 1200px) {
      margin-right: 20px;
      font-size: 13px;
    }

    // 响应式设计：超小屏幕
    @media (max-width: 768px) {
      margin-right: 15px;
      margin-top: 10px;
      font-size: 12px;
      gap: 8px;
    }

    .user-dropdown {
      display: flex;
      align-items: center;
      cursor: pointer;
      color: #fff;
      font-size: 14px;

      // 响应式设计：超小屏幕
      @media (max-width: 768px) {
        width: 100%;
        margin-top: 5px;
        margin-right: 0;
        justify-content: center;
        padding: 5px;
      }

      .language-icon {
        width: 15px;
        height: 15px;
        margin-right: 6px;

        // 响应式设计：超小屏幕
        @media (max-width: 768px) {
          width: 14px;
          height: 14px;
          margin-right: 4px;
        }
      }

      &:hover,
      &:focus {
        outline: none;
        border-color: transparent;
        box-shadow: none;
      }
    }

    :deep(.el-dropdown) {
      // 响应式设计：超小屏幕
      @media (max-width: 768px) {
        width: 100%;

        .el-dropdown-menu {
          min-width: 140px;
          transform: translateX(-50%);
          left: 50%;
          right: auto;
        }
      }

      &:hover,
      &:focus {
        outline: none;
        border-color: transparent;
        box-shadow: none;
      }
    }

    .user-icon {
      width: 14px;
      height: 15px;
      margin-right: 6px;
    }

    .user-auth-container {
      margin-left: 20px;
      :deep(.el-button) {
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fff;
        border: 1px solid #fff;
        color: #004c66;
        transition: all 0.3s ease;
        padding: 7px 15px 8px 15px;
        border-radius: 6px;
        margin-top: 1px;
        font-family: "PingFang Medium";
        font-size: 14px;

        &:hover {
          background-color: #004c66;
          color: #fff;
          border: 1px solid #004c66;
          // transform: scale(1.05);
        }

        &:active {
          // transform: scale(0.95);
          transition: all 0.1s ease;
        }
      }

      .user-avatar {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: #004c66;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        &:hover {
          background: rgba(0, 76, 102, 0.8);
        }

        .avatar-icon {
          width: 14px;
          height: 15px;
          color: #fff;
        }
      }
    }
  }
}

.nav-container {
  display: flex;
  align-items: start;
  font-size: 22px;
  // font-weight: 400;
  justify-content: center;
  position: relative;
  top: -5px;
  padding: 0 50px;
  animation-duration: 0.8s;
  animation-fill-mode: both;
  animation-delay: 200ms;
  animation-name: fadeIn;

  &::before,
  &::after {
    content: "";
    position: absolute;
    width: 0; // 初始宽度为0，用于动画效果
    height: 1px;
    /* transition: width 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94); // 添加缓动动画
    animation-fill-mode: both; */
    animation-delay: 800ms; // 延迟400ms开始动画，让页面先加载
  }

  &::before {
    left: 0;
    top: 50%;
    width: 27vw; // 设置最终宽度
    background: linear-gradient(to left, rgba(255, 255, 255, 0.8), transparent);
    // 左侧分割线从右向左展开动画（使用scaleX实现）
    transform: scaleX(0); // 初始缩放为0
    transform-origin: right; // 设置变换原点为右侧，从右端向左展开
    animation: expandLeftDividerScale 2s cubic-bezier(0.25, 0.46, 0.45, 0.94)
      forwards;
  }

  &::after {
    right: 0;
    top: 50%;
    width: 27vw; // 设置最终宽度
    background: linear-gradient(
      to right,
      rgba(255, 255, 255, 0.8),
      transparent
    );
    // 右侧分割线从左向右展开动画（使用scaleX实现）
    transform: scaleX(0); // 初始缩放为0
    transform-origin: left; // 设置变换原点为左侧，从左端向右展开
    animation: expandRightDividerScale 2s cubic-bezier(0.25, 0.46, 0.45, 0.94)
      forwards;
  }

  // 响应式设计：当屏幕宽度小于1600px时，调整分割线宽度和动画
  @media (max-width: 1600px) {
    &::before {
      width: 350px; // 中等屏幕宽度
      animation: expandLeftDividerScaleMedium 0.8s
        cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }
    &::after {
      width: 350px; // 中等屏幕宽度
      animation: expandRightDividerScaleMedium 0.8s
        cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }
  }

  // 当屏幕宽度小于1400px时，进一步调整分割线宽度和动画
  @media (max-width: 1400px) {
    &::before {
      width: 260px; // 小屏幕宽度
      animation: expandLeftDividerScaleSmall 0.8s
        cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }
    &::after {
      width: 260px; // 小屏幕宽度
      animation: expandRightDividerScaleSmall 0.8s
        cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }
  }

  // 当屏幕宽度小于1200px时，隐藏分割线避免遮挡导航
  @media (max-width: 1200px) {
    &::before,
    &::after {
      display: none;
    }
  }

  // 响应式设计：中等屏幕
  @media (max-width: 1400px) {
    font-size: 20px;
    padding: 0 30px;
  }

  // 响应式设计：小屏幕
  @media (max-width: 1200px) {
    font-size: 18px;
    padding: 0 20px;
    flex-wrap: wrap;
    justify-content: center;
    gap: 5px;
  }

  // 响应式设计：超小屏幕
  @media (max-width: 768px) {
    font-size: 16px;
    padding: 0 15px;
    margin-top: 10px;
  }

  .nav-item {
    display: flex;
    align-items: center;
    padding: 0 35px;
    cursor: pointer;
    height: 100%;
    transition: all 0.3s ease;
    color: #cccccc;
    position: relative;
    overflow: hidden;

    // 响应式设计：小屏幕下调整导航项
    @media (max-width: 1200px) {
      padding: 0 10px;
      margin: 5px 0;
    }

    // 响应式设计：超小屏幕
    @media (max-width: 768px) {
      padding: 0 8px;
      margin: 3px 0;

      span {
        font-size: 14px;
      }
    }

    // 添加底部装饰线
    &::after {
      content: "";
      position: absolute;
      bottom: -2px;
      left: 50%;
      width: 0;
      height: 2px;
      background: linear-gradient(90deg, transparent, #fff, transparent);
      transition: all 0.3s ease;
      transform: translateX(-50%);
    }
  }

  .nav-item:hover {
    color: #ffffff;
    transform: scale(1.05);
    // animation: navScaleEffect 0.3s ease;
    font-family: "PingFang Bold";
  }

  .nav-item:active {
    transform: scale(0.9);
    transition: all 0.1s ease;
  }

  .nav-item:hover::after {
    width: 80%;
  }

  .nav-item.active {
    color: #fff;

    &::after {
      width: 60%;
      background: linear-gradient(90deg, transparent, #fff, transparent);
    }
  }

  .nav-item.active .nav-icon {
    color: #fff;
  }

  .nav-icon {
    width: 30px;
    height: 30px;
    color: inherit;
    margin-right: 10px;

    // 响应式设计：小屏幕下调整图标尺寸
    @media (max-width: 1200px) {
      width: 26px;
      height: 26px;
      margin-right: 4px;
    }

    // 响应式设计：超小屏幕
    @media (max-width: 768px) {
      width: 22px;
      height: 22px;
      margin-right: 3px;
    }
  }
}

// 推荐专区标题样式
.recommend-title {
  text-align: center;
  margin: 155px 0 35px 0;
  color: #fff;
  animation-duration: 0.8s;
  animation-fill-mode: both;
  animation-delay: 500ms;
  animation-name: fadeInDown;
  font-family: "FZZongYi-M05S";

  // 响应式设计：125%
  @media (max-width: 1600px) {
    margin: 112px 0 30px 0;
  }

  // 响应式设计：150%
  @media (max-width: 1500px) {
    margin: 62px 0 30px 0;
  }

  /* // 响应式设计：中等屏幕
  @media (max-width: 1400px) {
    margin: 120px 0 30px 0;
  }

  // 响应式设计：小屏幕
  @media (max-width: 1200px) {
    margin: 70px 0 25px 0;
    padding: 0 20px;
  }

  // 响应式设计：超小屏幕
  @media (max-width: 768px) {
    margin: 50px 0 20px 0;
    padding: 0 15px;
  } */

  .title-text {
    font-size: 72px;
    // font-weight: bold;
    margin-bottom: 10px;
    // text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);

    // 响应式设计：中等屏幕
    @media (max-width: 1400px) {
      font-size: 68px;
    }

    // 响应式设计：小屏幕
    @media (max-width: 1200px) {
      font-size: 60px;
      margin-bottom: 8px;
    }

    // 响应式设计：超小屏幕
    @media (max-width: 768px) {
      font-size: 50px;
      margin-bottom: 6px;
      line-height: 1.2;
    }
  }

  .recommend-desc {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
    animation-duration: 0.8s;
    animation-fill-mode: both;
    animation-delay: 900ms;
    animation-name: fadeInUp;

    // 响应式设计：超小屏幕
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 5px;
      margin-top: 8px;
    }

    p {
      font-size: 24px;
      color: #fff;
      position: relative;
      cursor: pointer;
      opacity: 0.9;
      margin-right: 6px;
      font-family: "PingFang Medium";

      // 响应式设计：中等屏幕
      @media (max-width: 1400px) {
        font-size: 16px;
      }

      // 响应式设计：小屏幕
      @media (max-width: 1200px) {
        font-size: 15px;
        margin-right: 4px;
      }

      // 响应式设计：超小屏幕
      @media (max-width: 768px) {
        font-size: 14px;
        margin-right: 0;
        text-align: center;
        line-height: 1.3;
      }

      &::after {
        content: "";
        position: absolute;
        left: 50%;
        bottom: -2px;
        width: 0;
        height: 1px;
        background-color: #fff;
        transition: width 0.3s ease;
        transform: translateX(-50%);
      }

      &:hover::after {
        width: 100%;
      }
    }

    .link-icon {
      width: 22px;
      height: 22px;
      margin-left: 5px;
      color: #fff;
      cursor: pointer;

      // 响应式设计：小屏幕
      @media (max-width: 1200px) {
        width: 20px;
        height: 20px;
        margin-left: 4px;
      }

      // 响应式设计：超小屏幕
      @media (max-width: 768px) {
        width: 18px;
        height: 18px;
        margin-left: 0;
        margin-top: 2px;
      }
    }
  }
}

.search-container {
  position: relative;
  margin: 25px auto 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: margin 0.5s cubic-bezier(0.4, 0, 0.2, 1);

  // 添加渐入动画
  animation: fadeInSearch 0.5s ease-out forwards;
  opacity: 0;
  transform: translateY(-10px);
  animation-delay: 200ms;

  // 响应式设计：中等屏幕
  @media (max-width: 1400px) {
    margin: 25px auto 0;
  }

  // 响应式设计：小屏幕
  @media (max-width: 1200px) {
    margin: 20px auto 0;
    padding: 0 20px;
  }

  // 响应式设计：超小屏幕
  @media (max-width: 768px) {
    margin: 15px auto 0;
    padding: 0 15px;
  }

  // 推荐页面的搜索容器特殊样式（调整边距和延迟时间）
  .recommend-header & {
    margin: 0 auto 0;
    animation-duration: 0.8s;
    animation-fill-mode: both;
    animation-delay: 1200ms;
    animation-name: fadeInUp;

    // 响应式设计：中等屏幕
    @media (max-width: 1400px) {
      margin: 8px auto 0;
    }

    // 响应式设计：小屏幕
    @media (max-width: 1200px) {
      margin: 6px auto 0;
    }

    // 响应式设计：超小屏幕
    @media (max-width: 768px) {
      margin: 5px auto 0;
    }
  }

  // 滚动状态下的搜索容器样式
  /* .scrolled-header & {
    margin: 6px auto 0; // 调整上边距使搜索框移动到nav区域位置
    animation-delay: 0ms;
    animation-duration: 0s;
    position: absolute;
    top: 0;
  } */

  .search-input {
    width: 764px;
    height: 50px;
    background: #fff;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    padding: 0 136px 0 20px;
    color: #666666;
    font-size: 16px;
    outline: none;
    // transition: all 0.3s ease;
    font-family: "PingFang Medium";

    &::placeholder {
      color: #ccc;
    }

    // 响应式设计：中等屏幕
    @media (max-width: 1400px) {
      width: 650px;
      padding: 0 110px 0 18px;
    }

    // 响应式设计：小屏幕
    @media (max-width: 1200px) {
      width: 550px;
      height: 45px;
      padding: 0 100px 0 16px;
      font-size: 13px;
    }

    // 响应式设计：超小屏幕
    @media (max-width: 768px) {
      width: 70vw;
      height: 40px;
      padding: 0 90px 0 15px;
      font-size: 12px;
      border-radius: 8px;
    }

    /* &:hover {
      box-shadow: 0 0 10px rgba(0, 76, 102, 0.2);
      border-color: rgba(255, 255, 255, 0.8);
    }

    &:focus {
      box-shadow: 0 0 10px rgba(0, 76, 102, 0.2);
      border-color: #fff;
    } */

    &::placeholder {
      color: #cccccc;
    }
  }

  .search-button {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 150px;
    height: 46px;
    background: #004c66;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    color: #fff;
    font-size: 18px;
    // font-weight: 500;
    font-family: "PingFang Bold";
    transition: all 0.3s ease;
    right: 2px;

    // 响应式设计：中等屏幕
    @media (max-width: 1400px) {
      width: 130px;
      height: 46px;
      font-size: 15px;
    }

    // 响应式设计：小屏幕
    @media (max-width: 1200px) {
      width: 110px;
      height: 41px;
      font-size: 14px;
      right: 1.4rem;
    }

    // 响应式设计：超小屏幕
    @media (max-width: 768px) {
      width: 80px;
      height: 36px;
      font-size: 12px;
      border-radius: 8px;
      right: 1.1rem;
    }

    &:hover {
      background: rgba(0, 76, 102, 0.9);
      // transform: scale(1.03, 1.08);
    }

    &:active {
      background: #004c66;
      transform: scale(0.99, 0.95);
    }

    .search-icon {
      width: 17px;
      height: 18px;
      margin-right: 10px;

      // 响应式设计：小屏幕
      @media (max-width: 1200px) {
        width: 18px;
        height: 18px;
        margin-right: 4px;
      }

      // 响应式设计：超小屏幕
      @media (max-width: 768px) {
        width: 16px;
        height: 16px;
        margin-right: 3px;
      }
    }
  }
}
.scrolled-search-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: margin 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  margin-right: 100px;

  // 添加渐入动画
  /* animation: fadeInSearch 0.5s ease-out forwards;
  animation-delay: 200ms;
  opacity: 0;
  transform: translateY(-10px); */

  // 响应式设计：小屏幕
  @media (max-width: 1200px) {
    margin-top: 10px;
    padding: 0 20px;
  }

  // 响应式设计：超小屏幕
  @media (max-width: 768px) {
    margin-top: 10px;
    padding: 0 15px;
  }

  // 推荐页面的搜索容器特殊样式（调整边距和延迟时间）
  .recommend-header & {
    /* animation-duration: 0.8s;
    animation-fill-mode: both;
    animation-delay: 200ms;
    animation-name: fadeInUp; */

    // 响应式设计：中等屏幕
    @media (max-width: 1400px) {
      margin: 8px auto 0;
    }

    // 响应式设计：小屏幕
    @media (max-width: 1200px) {
      margin: 6px auto 0;
    }

    // 响应式设计：超小屏幕
    @media (max-width: 768px) {
      margin: 5px auto 0;
    }
  }

  .search-input {
    width: 764px;
    height: 50px;
    background: #fff;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    padding: 0 136px 0 20px;
    color: #666666;
    font-size: 16px;
    outline: none;
    transition: all 0.3s ease;
    font-family: "PingFang Medium";

    &::placeholder {
      color: #ccc;
    }

    // 响应式设计：中等屏幕
    @media (max-width: 1400px) {
      width: 650px;
      padding: 0 110px 0 18px;
    }

    // 响应式设计：小屏幕
    @media (max-width: 1200px) {
      width: 550px;
      height: 45px;
      padding: 0 100px 0 16px;
      font-size: 13px;
    }

    // 响应式设计：超小屏幕
    @media (max-width: 768px) {
      width: 70vw;
      height: 40px;
      padding: 0 90px 0 15px;
      font-size: 12px;
      border-radius: 8px;
    }

    /* &:hover {
      box-shadow: 0 0 10px rgba(0, 76, 102, 0.2);
      border-color: rgba(255, 255, 255, 0.8);
    }

    &:focus {
      box-shadow: 0 0 10px rgba(0, 76, 102, 0.2);
      border-color: #fff;
    } */

    &::placeholder {
      color: #cccccc;
    }
  }

  .search-button {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 150px;
    height: 46px;
    background: #004c66;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    color: #fff;
    font-size: 18px;
    // font-weight: 500;
    font-family: "PingFang Bold";
    transition: all 0.3s ease;
    right: 2px;

    // 响应式设计：中等屏幕
    @media (max-width: 1400px) {
      width: 130px;
      height: 46px;
      font-size: 15px;
    }

    // 响应式设计：小屏幕
    @media (max-width: 1200px) {
      width: 110px;
      height: 41px;
      font-size: 14px;
      right: 1.4rem;
    }

    // 响应式设计：超小屏幕
    @media (max-width: 768px) {
      width: 80px;
      height: 36px;
      font-size: 12px;
      border-radius: 8px;
      right: 1.1rem;
    }

    &:hover {
      background: rgba(0, 76, 102, 0.9);
      // transform: scale(1.03, 1.08);
    }

    &:active {
      background: #004c66;
      transform: scale(0.99, 0.95);
    }

    .search-icon {
      width: 17px;
      height: 18px;
      margin-right: 10px;

      // 响应式设计：小屏幕
      @media (max-width: 1200px) {
        width: 18px;
        height: 18px;
        margin-right: 4px;
      }

      // 响应式设计：超小屏幕
      @media (max-width: 768px) {
        width: 16px;
        height: 16px;
        margin-right: 3px;
      }
    }
  }
}

.menu-icon {
  width: 16px;
  height: 16px;
  color: #666;
  margin-right: 5px;
}

.logout-icon {
  color: #ff4757;
}

.logout-item {
  span {
    color: #ff4757;
  }
}

/* 用户下拉框容器 */
.user-dropdown-container {
  position: relative;
  display: inline-block;
}

/* 自定义用户信息弹出框 */
.custom-user-dropdown {
  position: fixed; /* 改为fixed定位，相对于视口定位而不是header组件 */
  top: 80px; /* 距离顶部的固定距离 */
  right: 50px; /* 距离右侧的固定距离，与header中用户容器的margin-right保持一致 */
  width: 293px;
  // min-height: 357px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 9999; /* 提高z-index确保弹出框显示在最上层 */
  overflow: hidden;
  display: flex;
  flex-direction: column;

  // 响应式设计：中等屏幕
  @media (max-width: 1400px) {
    right: 30px;
    width: 280px;
  }

  // 响应式设计：小屏幕
  @media (max-width: 1200px) {
    right: 20px;
    width: 270px;
    top: 70px;
  }

  // 响应式设计：超小屏幕
  @media (max-width: 768px) {
    right: 15px;
    left: 15px;
    width: auto;
    top: 60px;
    max-width: 320px;
    margin: 0 auto;
  }
}

/* 用户信息区域 */
.user-info-section {
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #e6eef0;
}

.user-info-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #004c66;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.user-info-avatar .info-avatar-icon {
  width: 24px;
  height: 25px;
  color: white;
}

.user-info-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-info-name {
  font-size: 16px;
  // font-weight: 500;
  color: #333;
}

.user-info-phone {
  font-size: 14px;
  color: #666;
}

/* 菜单列表区域 */
.menu-list-section {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 0;
  cursor: pointer;
  border-radius: 6px;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f5f5;
  }

  .menu-icon {
    width: 20px;
    height: 20px;
    color: #666;
  }

  span {
    font-size: 14px;
    color: #333;
  }
}

/* 菜单列表项之间的分隔线 */
.menu-divider {
  margin: 17px 0;
  border-top: 1px solid #dddddd;
}

/* 身份切换特殊样式 */
.identity-item {
  flex-direction: column;
  align-items: stretch;
}

.identity-content {
  display: flex;
  gap: 10px;

  .identity-header {
    display: flex;
    align-items: center;
    gap: 10px;
  }
}

.identity-buttons {
  display: flex;
  gap: 8px;
}

.identity-btn {
  position: relative;
  padding: 6px 12px;
  border-radius: 4px;
  border: 1px solid #e6e6e6;
  background-color: #f0f0f0;
  color: #666;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.identity-btn:hover {
  background-color: #e6e6e6;
}

/* 选中状态样式 */
.identity-btn.selected {
  border-color: #004c66;
  color: #004c66;
  background-color: #f0f8ff;
}

/* 对号图标容器 */
.check-icon {
  position: absolute;
  top: 0;
  right: 0;
  width: 13px;
  height: 12px;
  background-color: #004c66;
  border-radius: 0 0 0 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 对号图标样式 */
.check-icon .check-svg {
  width: 8px;
  height: 8px;
  color: white;
}

/* 退出登录特殊样式 */
.logout-item {
  margin-top: auto;
}

.logout-item .logout-icon {
  color: #ff4d4f;
}

.logout-item span {
  color: #ff4d4f;
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes clickPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes navScaleEffect {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

// 分割线展开动画定义
// 左侧分割线从右至左展开动画（大屏幕）- 使用scaleX实现
@keyframes expandLeftDividerScale {
  0% {
    transform: scaleX(0);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  100% {
    transform: scaleX(1);
    opacity: 1;
  }
}

// 保留原有的width动画（用于兼容性，如果需要的话）
@keyframes expandLeftDivider {
  0% {
    width: 0;
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  100% {
    width: 28.6vw;
    opacity: 1;
  }
}

// 右侧分割线从左至右展开动画（大屏幕）- 使用scaleX实现
@keyframes expandRightDividerScale {
  0% {
    transform: scaleX(0);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  100% {
    transform: scaleX(1);
    opacity: 1;
  }
}

// 保留原有的width动画（用于兼容性，如果需要的话）
@keyframes expandRightDivider {
  0% {
    width: 0;
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  100% {
    width: 28.6vw;
    opacity: 1;
  }
}

// 左侧分割线从右至左展开动画（中等屏幕 ≤1600px）- 使用scaleX实现
@keyframes expandLeftDividerScaleMedium {
  0% {
    transform: scaleX(0);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  100% {
    transform: scaleX(1);
    opacity: 1;
  }
}

// 保留原有的width动画（中等屏幕 ≤1600px）
@keyframes expandLeftDividerMedium {
  0% {
    width: 0;
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  100% {
    width: 350px;
    opacity: 1;
  }
}

// 右侧分割线从左至右展开动画（中等屏幕 ≤1600px）- 使用scaleX实现
@keyframes expandRightDividerScaleMedium {
  0% {
    transform: scaleX(0);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  100% {
    transform: scaleX(1);
    opacity: 1;
  }
}

// 保留原有的width动画（中等屏幕 ≤1600px）
@keyframes expandRightDividerMedium {
  0% {
    width: 0;
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  100% {
    width: 350px;
    opacity: 1;
  }
}

// 左侧分割线从右至左展开动画（小屏幕 ≤1400px）- 使用scaleX实现
@keyframes expandLeftDividerScaleSmall {
  0% {
    transform: scaleX(0);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  100% {
    transform: scaleX(1);
    opacity: 1;
  }
}

// 保留原有的width动画（小屏幕 ≤1400px）
@keyframes expandLeftDividerSmall {
  0% {
    width: 0;
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  100% {
    width: 260px;
    opacity: 1;
  }
}

// 右侧分割线从左至右展开动画（小屏幕 ≤1400px）- 使用scaleX实现
@keyframes expandRightDividerScaleSmall {
  0% {
    transform: scaleX(0);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  100% {
    transform: scaleX(1);
    opacity: 1;
  }
}

// 滚动状态下左侧分割线从右至左展开动画
@keyframes expandLeftDividerScaleScrolled {
  0% {
    transform: scaleX(0);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  100% {
    transform: scaleX(1);
    opacity: 1;
  }
}

// 滚动状态下右侧分割线从左至右展开动画
@keyframes expandRightDividerScaleScrolled {
  0% {
    transform: scaleX(0);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  100% {
    transform: scaleX(1);
    opacity: 1;
  }
}

// 保留原有的width动画（小屏幕 ≤1400px）
@keyframes expandRightDividerSmall {
  0% {
    width: 0;
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  100% {
    width: 260px;
    opacity: 1;
  }
}

// 搜索框渐入动画
@keyframes fadeInSearch {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
