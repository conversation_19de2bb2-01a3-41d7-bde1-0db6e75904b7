# 代理配置和环境变量说明

## 概述

项目已配置了完整的代理系统和环境变量管理，支持开发环境和生产环境的自动切换。

## 环境变量文件

### `.env` - 通用配置
```bash
# 应用基本信息
VITE_APP_NAME=慧谷云拍卖平台
VITE_APP_VERSION=1.0.0

# API前缀
VITE_API_PREFIX=/api
VITE_NEW_API_PREFIX=/new-api
```

### `.env.development` - 开发环境配置
```bash
# 开发环境配置
NODE_ENV=development

# 旧API服务地址（原有接口）
VITE_OLD_API_BASE_URL=http://************:80

# 新API服务地址（新功能接口）
VITE_NEW_API_BASE_URL=http://************:8080/jeecg-boot

# 开发环境端口
VITE_PORT=8080
VITE_HOST=0.0.0.0
```

### `.env.production` - 生产环境配置
```bash
# 生产环境配置
NODE_ENV=production

# 旧API服务地址（原有接口）- 生产环境地址
VITE_OLD_API_BASE_URL=https://api.hghello.com

# 新API服务地址（新功能接口）- 生产环境地址
VITE_NEW_API_BASE_URL=https://new-api.hghello.com/jeecg-boot

# 生产环境端口
VITE_PORT=80
VITE_HOST=0.0.0.0
```

## 代理配置

### 开发环境代理

在 `vite.config.ts` 中配置了两个代理路径：

1. **旧API代理** (`/api`)
   - 代理目标：`VITE_OLD_API_BASE_URL` 环境变量指定的地址
   - 默认：`http://************:80`
   - 路径重写：`/api/xxx` → `/xxx`

2. **新API代理** (`/new-api`)
   - 代理目标：`VITE_NEW_API_BASE_URL` 环境变量指定的地址
   - 默认：`http://************:8080/jeecg-boot`
   - 路径重写：`/new-api/xxx` → `/xxx`

### 生产环境

生产环境不使用代理，直接请求实际的API地址。

## HTTP配置

### `src/utils/http.ts` - 旧API配置
```typescript
// 开发环境使用代理 /api，生产环境使用实际地址
baseURL: import.meta.env.DEV ? '/api' : (import.meta.env.VITE_OLD_API_BASE_URL || 'http://************:80')
```

### `src/utils/http-new.ts` - 新API配置
```typescript
// 开发环境使用代理 /new-api，生产环境使用实际地址
baseURL: import.meta.env.DEV ? '/new-api' : (import.meta.env.VITE_NEW_API_BASE_URL || 'http://************:8080/jeecg-boot')
```

## 使用方法

### 1. 开发环境启动
```bash
# 启动开发服务器
npm run dev
# 或
yarn dev
```

### 2. 生产环境构建
```bash
# 构建生产版本
npm run build
# 或
yarn build
```

### 3. 在代码中使用环境变量
```typescript
// 获取环境变量
const apiUrl = import.meta.env.VITE_OLD_API_BASE_URL
const newApiUrl = import.meta.env.VITE_NEW_API_BASE_URL
const isDev = import.meta.env.DEV

// 使用全局常量（在vite.config.ts中定义）
const oldApiUrl = __OLD_API_BASE_URL__
const newApiUrl = __NEW_API_BASE_URL__
const isDevelopment = __IS_DEVELOPMENT__
```

## 注意事项

1. **环境变量命名**：所有自定义环境变量必须以 `VITE_` 开头才能在客户端代码中访问

2. **代理调试**：开发环境下，代理请求会在控制台输出日志，方便调试

3. **CORS问题**：代理配置已设置 `changeOrigin: true`，解决跨域问题

4. **生产环境地址**：请根据实际的生产环境API地址修改 `.env.production` 文件

5. **TypeScript支持**：已在 `env.d.ts` 中添加了完整的类型定义，提供智能提示

## 故障排除

### 代理不生效
1. 检查环境变量文件是否正确配置
2. 确认请求路径是否以 `/api` 或 `/new-api` 开头
3. 查看控制台是否有代理错误日志

### 生产环境API请求失败
1. 检查 `.env.production` 中的API地址是否正确
2. 确认生产环境服务器是否可访问
3. 检查CORS配置是否正确

### 环境变量不生效
1. 确认变量名以 `VITE_` 开头
2. 重启开发服务器
3. 检查 `env.d.ts` 中的类型定义是否正确