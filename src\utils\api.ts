import http from "./http";
import { getParams } from "./auth";
import type { AxiosResponse } from "axios";

/**
 * API接口封装
 * 包含所有业务相关的API请求方法
 */

// 定义通用的API响应类型
interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

// 定义通用的API请求参数类型
interface BaseParams {
  [key: string]: any;
}

/**
 * 首页相关API
 */
export const homeApi = {
  /**
   * 获取首页轮播图数据
   * @returns {Promise<ApiResponse>} 轮播图数据
   */
  getBanner(): Promise<ApiResponse> {
    return http
      .post("ypwapi/ptindex/bannerlist", getParams())
      .then((res) => res.data);
  },

  /**
   * 获取竞价会条数
   * @returns {Promise<ApiResponse>} 竞价会条数
   */
  getPmhNum(): Promise<ApiResponse> {
    return http
      .post("ypwapi/ptindex/pmh_num", getParams())
      .then((res) => res.data);
  },

  /**
   * 获取近期竞价会
   * @param {object} params - 请求参数
   * @param {number} params.day - 天数
   * @returns {Promise<ApiResponse>} 近期竞价会数据
   */
  getRecentAuctions(params: { day: number }): Promise<ApiResponse> {
    const requestParams = {
      day: params.day,
      ...getParams(),
    };
    return http
      .post("ypwapi/ptindex/jinqipaimaihui", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取成交案例
   * @returns {Promise<ApiResponse>} 成交案例数据
   */
  getTransactionCases(): Promise<ApiResponse> {
    return http
      .post("ypwapi/ptindex/daycjbiaodi", getParams())
      .then((res) => res.data);
  },

  /**
   * 获取今日拍卖标的
   * @returns {Promise<ApiResponse>} 今日拍卖标的数据
   */
  getTodayAuctions(): Promise<ApiResponse> {
    return http
      .post("ypwapi/ptindex/daybiaodi", getParams())
      .then((res) => res.data);
  },

  /**
   * 获取竞价会公告
   * @returns {Promise<ApiResponse>} 竞价会公告数据
   */
  getAuctionAnnouncements(): Promise<ApiResponse> {
    return http
      .post("ypwapi/ptindex/paimaihui", getParams())
      .then((res) => res.data);
  },

  /**
   * 获取新闻列表
   * @param {object} params - 请求参数
   * @param {number} params.cateid - 分类ID
   * @param {number} params.limit - 限制条数
   * @returns {Promise<ApiResponse>} 新闻列表数据
   */
  getNewsList(params: { cateid: number; limit: number }): Promise<ApiResponse> {
    const requestParams = {
      cateid: params.cateid,
      limit: params.limit,
      ...getParams(),
    };
    return http
      .post("ypwapi/ptindex/newsindex", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取合作企业列表
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 合作企业数据
   */
  getPartnerCompanies(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(),
    };
    return http
      .post("ypwapi/ptindex/hzqiyelist", requestParams)
      .then((res) => res.data);
  },
};

/**
 * 用户认证相关API
 */
export const authApi = {
  /**
   * 用户注册
   * @param {object} params - 注册参数
   * @param {string} params.mobile - 手机号
   * @param {string} params.password - 密码
   * @param {string} params.yzcode - 验证码
   * @returns {Promise<ApiResponse>} 注册结果
   */
  register(params: {
    mobile: string;
    password: string;
    yzcode: string;
  }): Promise<ApiResponse> {
    const requestParams = {
      mobile: params.mobile,
      password: params.password,
      yzcode: params.yzcode,
      ...getParams(),
    };
    return http.post("ypwapi/register", requestParams).then((res) => res.data);
  },

  /**
   * 发送验证码
   * @param {object} params - 请求参数
   * @param {string} params.mobile - 手机号
   * @returns {Promise<ApiResponse>} 发送结果
   */
  sendSms(params: { mobile: string }): Promise<ApiResponse> {
    const requestParams = {
      mobile: params.mobile,
      ...getParams(),
    };
    return http
      .post("setting/alismsnew/sendsms", requestParams)
      .then((res) => res.data);
  },

  /**
   * 密码登录
   * @param {object} params - 登录参数
   * @param {string} params.mobile - 手机号
   * @param {string} params.password - 密码
   * @returns {Promise<ApiResponse>} 登录结果
   */
  login(params: { mobile: string; password: string }): Promise<ApiResponse> {
    const requestParams = {
      mobile: params.mobile,
      password: params.password,
      ...getParams(),
    };
    return http.post("ypwapi/login", requestParams).then((res) => res.data);
  },

  /**
   * 验证码登录
   * @param {object} params - 登录参数
   * @param {string} params.mobile - 手机号
   * @param {string} params.yzcode - 验证码
   * @returns {Promise<ApiResponse>} 登录结果
   */
  smsLogin(params: { mobile: string; yzcode: string }): Promise<ApiResponse> {
    const requestParams = {
      mobile: params.mobile,
      yzcode: params.yzcode,
      ...getParams(),
    };
    return http
      .post("ypwapi/login/dx_login", requestParams)
      .then((res) => res.data);
  },

  /**
   * 忘记密码
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 重置结果
   */
  resetPassword(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/member/zhaohuipwd", requestParams)
      .then((res) => res.data);
  },

  /**
   * 修改密码
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 修改结果
   */
  changePassword(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/member/up_pass", requestParams)
      .then((res) => res.data);
  },
};

/**
 * 标的相关API
 */
export const auctionApi = {
  /**
   * 获取标的详情
   * @param {object} params - 请求参数
   * @param {number} params.id - 标的ID
   * @returns {Promise<ApiResponse>} 标的详情数据
   */
  getTargetDetail(params: { id: number }): Promise<ApiResponse> {
    const requestParams = {
      id: params.id,
      ...getParams(),
    };
    return http
      .post("ypwapi/biaodi/biaodiinfo", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取竞价会标的列表
   * @param {object} params - 请求参数
   * @param {number} params.pmh_id - 竞价会ID
   * @returns {Promise<ApiResponse>} 标的列表数据
   */
  getAuctionTargets(params: { pmh_id: number }): Promise<ApiResponse> {
    const requestParams = {
      pmh_id: params.pmh_id,
      ...getParams(true),
    };
    return http
      .post("ypwapi/biaodi/xq_bdlist", requestParams)
      .then((res) => res.data);
  },

  /**
   * 标的报名
   * @param {object} params - 请求参数
   * @param {number} params.bd_id - 标的ID
   * @param {number} params.member_id - 用户ID
   * @param {number} params.type - 报名类型 (1个人/2企业)
   * @returns {Promise<ApiResponse>} 报名结果
   */
  registerTarget(params: {
    bd_id: number;
    member_id: number;
    type: number;
  }): Promise<ApiResponse> {
    const requestParams = {
      bd_id: params.bd_id,
      member_id: params.member_id,
      type: params.type,
      ...getParams(true),
    };
    return http
      .post("ypwapi/biaodi/baoming_bd", requestParams)
      .then((res) => res.data);
  },

  /**
   * 标的看货确认
   * @param {object} params - 请求参数
   * @param {number} params.bd_id - 标的ID
   * @param {number} params.member_id - 用户ID
   * @returns {Promise<ApiResponse>} 看货结果
   */
  viewTarget(params: {
    bd_id: number;
    member_id: number;
    bm_id: number;
    type: number;
  }): Promise<ApiResponse> {
    const requestParams = {
      bd_id: params.bd_id,
      member_id: params.member_id,
      bm_id: params.bm_id,
      type: params.type,
      ...getParams(true),
    };
    return http
      .post("ypwapi/biaodi/kanhuo_bd", requestParams)
      .then((res) => res.data);
  },

  /**
   * 上传缴纳凭证
   * @param {object} params - 请求参数
   * @param {number} params.bd_id - 标的ID
   * @param {number} params.member_id - 用户ID
   * @param {number} params.bm_id - 报名ID
   * @param {string} params.zhengming - 竞拍凭证
   * @returns {Promise<ApiResponse>} 上传结果
   */
  uploadPaymentProof(params: {
    bd_id: number;
    member_id: number;
    bm_id: number;
    zhengming: string;
  }): Promise<ApiResponse> {
    const requestParams = {
      bd_id: params.bd_id,
      member_id: params.member_id,
      bm_id: params.bm_id,
      zhengming: params.zhengming,
      ...getParams(true),
    };
    return http
      .post("ypwapi/biaodi/zhengming_bd", requestParams)
      .then((res) => res.data);
  },

  /**
   * 标的出价
   * @param {object} params - 请求参数
   * @param {number} params.bd_id - 标的ID
   * @param {number} params.member_id - 用户ID
   * @param {number} params.chujia - 出价金额
   * @param {number} params.dangqianjia - 当前价格
   * @returns {Promise<ApiResponse>} 出价结果
   */
  bidTarget(params: {
    bd_id: number;
    member_id: number;
    chujia: number;
    dangqianjia: number;
  }): Promise<ApiResponse> {
    const requestParams = {
      bd_id: params.bd_id,
      member_id: params.member_id,
      chujia: params.chujia,
      dangqianjia: params.dangqianjia,
      ...getParams(true),
    };
    return http
      .post("ypwapi/biaodi/chujia_bd", requestParams)
      .then((res) => res.data);
  },

  /**
   * 收藏/取消收藏标的
   * @param {object} params - 请求参数
   * @param {number} params.bd_id - 标的ID
   * @param {number} params.member_id - 用户ID
   * @param {number} params.isquxiao - 是否取消收藏 (0收藏/1取消)
   * @returns {Promise<ApiResponse>} 收藏结果
   */
  favoriteTarget(params: {
    bd_id: number;
    member_id: number;
    isquxiao: number;
  }): Promise<ApiResponse> {
    const requestParams = {
      bd_id: params.bd_id,
      member_id: params.member_id,
      isquxiao: params.isquxiao,
      ...getParams(true),
    };
    return http
      .post("ypwapi/member/scbiaodi", requestParams)
      .then((res) => res.data);
  },

  /**
   * 检查标的收藏状态
   * @param {object} params - 请求参数
   * @param {number} params.bd_id - 标的ID
   * @param {number} params.member_id - 用户ID
   * @returns {Promise<ApiResponse>} 收藏状态
   */
  checkFavoriteStatus(params: {
    bd_id: number;
    member_id: number;
  }): Promise<ApiResponse> {
    const requestParams = {
      bd_id: params.bd_id,
      member_id: params.member_id,
      ...getParams(true),
    };
    return http
      .post("ypwapi/biaodi/zybdsc", requestParams)
      .then((res) => res.data);
  },
  /**
   * 获取标的列表
   * @param {object} params - 查询参数
   * @returns {Promise<ApiResponse>} 标的列表数据
   */
  getAuctionList(params: {
    province?: string;
    city?: string;
    type?: number;
    status?: number | string;
    starttime?: string;
    keyword?: string;
    page?: number;
  }): Promise<ApiResponse> {
    const requestParams = {
      province: params.province,
      city: params.city,
      type: params.type,
      status: params.status,
      starttime: params.starttime,
      keyword: params.keyword,
      page: params.page,
      ...getParams(true),
    };
    return http
      .post("ypwapi/biaodi/biaodilist", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取标的筛选条件
   * @returns {Promise<ApiResponse>} 筛选条件数据
   */
  getAuctionFilters(): Promise<ApiResponse> {
    return http
      .post("ypwapi/biaodi/biaoditiaojian", getParams())
      .then((res) => res.data);
  },

  /**
   * 获取未审核标的详情
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 未审核标的详情
   */
  getUnauditedAuctionDetail(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(),
    };
    return http
      .post("ypwapi/biaodi/weishen_biaodiinfo", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取未审核竞价会标的列表
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 未审核竞价会标的列表
   */
  getUnauditedAuctionSessionList(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/biaodi/weishen_xq_bdlist", requestParams)
      .then((res) => res.data);
  },
};

/**
 * 竞价会相关API
 */
export const auctionSessionApi = {
  /**
   * 获取竞价会列表
   * @param {object} params - 查询参数
   * @returns {Promise<ApiResponse>} 竞价会列表数据
   */
  getAuctionSessionList(params: {
    xingshi?: string;
    status?: string;
    starttime?: string;
    keyword?: string;
    page?: number;
  }): Promise<ApiResponse> {
    const requestParams = {
      pmh_xingshi: params.xingshi,
      pmh_status: params.status,
      starttime: params.starttime,
      keyword: params.keyword,
      page: params.page,
      ...getParams(true),
    };
    return http
      .post("ypwapi/paimaihui/paimaihuilist", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取竞价会信息
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 竞价会信息
   */
  getAuctionSessionInfo(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/paimaihui/paimaihuibd", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取竞价会公告
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 竞价会公告
   */
  getAuctionSessionAnnouncement(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/ptindex/pmhinfo", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取竞价会公告列表
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 竞价会公告列表
   */
  getAuctionSessionAnnouncementList(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/ptindex/pmhlist", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取竞价会详情
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 竞价会详情
   */
  getAuctionSessionDetail(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/paimaihui/paimaihuiinfo", requestParams)
      .then((res) => res.data);
  },
};

/**
 * 企业相关API
 */
export const companyApi = {
  /**
   * 获取拍卖企业列表
   * @param {object} params - 查询参数
   * @returns {Promise<ApiResponse>} 企业列表数据
   */
  getCompanyList(params: {
    shouzimu?: string;
    keyword?: string;
    page?: number;
  }): Promise<ApiResponse> {
    const requestParams = {
      shouzimu: params.shouzimu,
      keyword: params.keyword,
      page: params.page,
      ...getParams(),
    };
    return http
      .post("ypwapi/pmqiye/list", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取企业详情
   * @param {object} params - 请求参数
   * @param {number} params.id - 企业ID
   * @returns {Promise<ApiResponse>} 企业详情数据
   */
  getCompanyDetail(params: { id: number }): Promise<ApiResponse> {
    const requestParams = {
      id: params.id,
      ...getParams(),
    };
    return http
      .post("ypwapi/pmqiye/info", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取企业竞价会列表
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 企业竞价会列表
   */
  getCompanyAuctionSessions(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/pmqiye/pmhlist", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取企业标的列表
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 企业标的列表
   */
  getCompanyAuctions(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/pmqiye/bdlist", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取企业拍卖公告列表
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 企业拍卖公告列表
   */
  getCompanyAnnouncements(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/pmqiye/pmhgglist", requestParams)
      .then((res) => res.data);
  },

  /**
   * 收藏/取消收藏企业
   * @param {object} params - 请求参数
   * @param {number} params.member_id - 用户ID
   * @param {number} params.qiye_id - 企业ID
   * @param {number} params.isquxiao - 是否取消收藏
   * @returns {Promise<ApiResponse>} 收藏结果
   */
  favoriteCompany(params: {
    member_id: number;
    qiye_id: number;
    isquxiao: number;
    type: number;
  }): Promise<ApiResponse> {
    const requestParams = {
      member_id: params.member_id,
      qiye_id: params.qiye_id,
      isquxiao: params.isquxiao,
      ...getParams(),
    };
    return http
      .post("ypwapi/member/scqiye", requestParams)
      .then((res) => res.data);
  },

  /**
   * 检查企业是否收藏
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 收藏状态
   */
  checkCompanyFavorite(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/pmqiye/zyqysc", requestParams)
      .then((res) => res.data);
  },

  /**
   * 企业入驻
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 入驻结果
   */
  companyRegister(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(),
    };
    return http
      .post("ypwapi/ruzhu/qiyeruzhu", requestParams)
      .then((res) => res.data);
  },
};

/**
 * 用户个人中心相关API
 */
export const userApi = {
  /**
   * 获取我参与的标的列表
   * @param {object} params - 请求参数
   * @param {number} params.member_id - 用户ID
   * @param {number} params.page - 页码
   * @returns {Promise<ApiResponse>} 参与的标的列表
   */
  getMyParticipatedAuctions(params: {
    member_id: number;
    page: number;
  }): Promise<ApiResponse> {
    const requestParams = {
      member_id: params.member_id,
      page: params.page,
      ...getParams(),
    };
    return http
      .post("ypwapi/member/mycybiaodilist", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取我收藏的企业列表
   * @param {object} params - 请求参数
   * @param {number} params.member_id - 用户ID
   * @param {number} params.page - 页码
   * @returns {Promise<ApiResponse>} 收藏的企业列表
   */
  getMyFavoriteCompanies(params: {
    member_id: number;
    page: number;
    type: number;
  }): Promise<ApiResponse> {
    const requestParams = {
      member_id: params.member_id,
      page: params.page,
      ...getParams(),
    };
    return http
      .post("ypwapi/member/myscqiyelist", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取我收藏的标的列表
   * @param {object} params - 请求参数
   * @param {number} params.member_id - 用户ID
   * @param {number} params.page - 页码
   * @returns {Promise<ApiResponse>} 收藏的标的列表
   */
  getMyFavoriteAuctions(params: {
    member_id: number;
    page: number;
  }): Promise<ApiResponse> {
    const requestParams = {
      member_id: params.member_id,
      page: params.page,
      ...getParams(),
    };
    return http
      .post("ypwapi/member/myscbiaodilist", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取我竞得的标的列表
   * @param {object} params - 请求参数
   * @param {number} params.member_id - 用户ID
   * @param {number} params.page - 页码
   * @returns {Promise<ApiResponse>} 竞得的标的列表
   */
  getMyWonAuctions(params: {
    member_id: number;
    page: number;
  }): Promise<ApiResponse> {
    const requestParams = {
      member_id: params.member_id,
      page: params.page,
      ...getParams(),
    };
    return http
      .post("ypwapi/member/myjdbiaodilist", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取我报名的标的列表
   * @param {object} params - 请求参数
   * @param {number} params.member_id - 用户ID
   * @param {number} params.page - 页码
   * @returns {Promise<ApiResponse>} 报名的标的列表
   */
  getMyRegisteredAuctions(params: {
    member_id: number;
    page: number;
  }): Promise<ApiResponse> {
    const requestParams = {
      member_id: params.member_id,
      page: params.page,
      ...getParams(),
    };
    return http
      .post("ypwapi/member/mybmbiaodilist", requestParams)
      .then((res) => res.data);
  },

  /**
   * 保存用户头像
   * @param {object} params - 请求参数
   * @param {number} params.member_id - 用户ID
   * @param {string} params.avatar - 头像URL
   * @returns {Promise<ApiResponse>} 保存结果
   */
  saveAvatar(params: {
    member_id: number;
    avatar: string;
  }): Promise<ApiResponse> {
    const requestParams = {
      member_id: params.member_id,
      avatar: params.avatar,
      ...getParams(),
    };
    return http
      .post("ypwapi/member/saveheader", requestParams)
      .then((res) => res.data);
  },

  /**
   * 用户认证上传
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 认证结果
   */
  uploadCertification(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/member/rzupload", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取认证信息
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 认证信息
   */
  getCertificationInfo(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/member/rzinfo", requestParams)
      .then((res) => res.data);
  },

  /**
   * 检查是否认证
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 认证状态
   */
  checkCertification(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/member/isrz", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取报名信息
   * @param {object} params - 请求参数
   * @param {number} params.bd_id - 标的ID
   * @param {number} params.member_id - 用户ID
   * @param {number} params.parentid - 父级ID
   * @returns {Promise<ApiResponse>} 报名信息
   */
  getRegistrationInfo(params: {
    bd_id: number;
    member_id: number;
    parentid: number;
  }): Promise<ApiResponse> {
    const requestParams = {
      bd_id: params.bd_id,
      member_id: params.member_id,
      parentid: params.parentid,
      ...getParams(true),
    };
    return http
      .post("ypwapi/member/bmverify", requestParams)
      .then((res) => res.data);
  },
};

/**
 * 出价和发言相关API
 */
export const biddingApi = {
  /**
   * 获取出价记录
   * @param {object} params - 请求参数
   * @param {number} params.bd_id - 标的ID
   * @param {number} params.page - 页码
   * @returns {Promise<ApiResponse>} 出价记录
   */
  getBidRecords(params: { bd_id: number; page: number }): Promise<ApiResponse> {
    const requestParams = {
      bd_id: params.bd_id,
      page: params.page,
      ...getParams(true),
    };
    return http
      .post("ypwapi/chujia/list", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取发言记录
   * @param {object} params - 请求参数
   * @param {number} params.bd_id - 标的ID
   * @param {number} params.pmh_id - 拍卖会ID
   * @param {number} params.page - 页码
   * @returns {Promise<ApiResponse>} 发言记录
   */
  getSpeechRecords(params: {
    bd_id: number;
    pmh_id: number;
    page: number;
  }): Promise<ApiResponse> {
    const requestParams = {
      bd_id: params.bd_id,
      pmh_id: params.pmh_id,
      page: params.page,
      ...getParams(true),
    };
    return http
      .post("ypwapi/fayan/list", requestParams)
      .then((res) => res.data);
  },

  /**
   * 减价标的
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 减价结果
   */
  reducePrice(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/member/jianjiabd", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取减价列表
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 减价列表
   */
  getReducePriceList(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/member/jianjialist", requestParams)
      .then((res) => res.data);
  },

  /**
   * 一次性出价
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 出价结果
   */
  oneTimeBid(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/member/yicixingChujia", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取一次性出价列表
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 一次性出价列表
   */
  getOneTimeBidList(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/member/yicixingChujialist", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取混合竞价列表
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 混合竞价列表
   */
  getMixedBiddingList(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/member/hunJialist", requestParams)
      .then((res) => res.data);
  },
};

/**
 * 新闻相关API
 */
export const newsApi = {
  /**
   * 获取新闻列表
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 新闻列表
   */
  getNewsList(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/ptindex/newslist", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取新闻详情
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 新闻详情
   */
  getNewsDetail(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/ptindex/newsinfo", requestParams)
      .then((res) => res.data);
  },
};

/**
 * 文件上传相关API
 */
export const uploadApi = {
  /**
   * 获取OSS签名信息
   * @returns {Promise<ApiResponse>} OSS签名信息
   */
  getOssPolicy(): Promise<ApiResponse> {
    return http
      .post("setting/alioss/aliosspolicy", getParams())
      .then((res) => res.data);
  },
};

/**
 * 物资租赁相关API
 */
export const rentalApi = {
  /**
   * 获取租赁分类
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 租赁分类
   */
  getRentalCategories(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwweituo/zulinceshi/zulinfenlei", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取新版租赁分类
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 新版租赁分类
   */
  getNewRentalCategories(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwweituo/zulinceshi/zulinfenleinew", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取租赁列表
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 租赁列表
   */
  getRentalList(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwweituo/zulinceshi/zulinlist", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取租赁详情
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 租赁详情
   */
  getRentalDetail(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwweituo/zulinceshi/zulininfo", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取钢价行情
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 钢价行情
   */
  getSteelPrices(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwweituo/gangjia/gangjiainfo", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取成交案例
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 成交案例
   */
  getTransactionCases(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwweituo/zulinceshi/chengjiao", requestParams)
      .then((res) => res.data);
  },
};

/**
 * 其他API
 */
export const otherApi = {
  /**
   * 跳转用户页面
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 跳转结果
   */
  toUser(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/ziguan/touser", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取招标公告列表
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 招标公告列表
   */
  getTenderList(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/ptindex/zhaobiaolist", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取招标公告详情
   * @param {BaseParams} params - 请求参数
   * @returns {Promise<ApiResponse>} 招标公告详情
   */
  getTenderDetail(params: BaseParams): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      ...getParams(true),
    };
    return http
      .post("ypwapi/ptindex/zhaobiaoinfo", requestParams)
      .then((res) => res.data);
  },
};

// 导出所有API模块
export default {
  homeApi,
  authApi,
  auctionApi,
  auctionSessionApi,
  companyApi,
  userApi,
  biddingApi,
  newsApi,
  uploadApi,
  rentalApi,
  otherApi,
};
