<template>
  <!-- 上传缴纳凭证弹窗 -->
  <Modal
    v-model="visible"
    title="上传缴纳凭证"
    title-icon="model-icon"
    width="500px"
  >
    <div class="upload-content">
      <div class="upload-tip">
        <p>请上传缴纳凭证图片</p>
        <p class="tip-text">只能上传jpg/png文件，且不超过10MB</p>
      </div>
      
      <!-- 已上传图片显示区域 -->
      <div v-if="uploadedImageUrl" class="image-display">
        <el-image
          ref="imageRef"
          :src="uploadedImageUrl"
          :preview-src-list="[uploadedImageUrl]"
          class="uploaded-image"
          fit="cover"
        />
        <!-- 操作按钮遮罩层 -->
        <div class="upload-overlay">
          <div class="overlay-actions">
            <!-- 重新上传区域 -->
            <div class="action-item reupload-action" @click="triggerReupload">
              <SvgIcon iconName="upload-up" className="action-icon" />
              <span class="action-text">重新上传</span>
            </div>
            <!-- 预览图片区域 -->
            <div class="action-item preview-action" @click="previewImage">
              <SvgIcon iconName="freedom-propertyDetail-eye" className="action-icon" />
              <span class="action-text">预览图片</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 上传区域 -->
      <el-upload
        v-else
        ref="uploadRef"
        class="upload-demo"
        action="#"
        :show-file-list="false"
        :before-upload="handleBeforeUpload"
        :http-request="handleCustomUpload"
        accept="image/jpeg,image/png,image/jpg,image/gif,image/bmp"
      >
        <div class="upload-area">
          <el-icon class="upload-icon"><Plus /></el-icon>
          <div class="upload-text">点击上传图片</div>
        </div>
      </el-upload>
    </div>
    
    <template #footer>
      <div class="upload-footer">
        <button class="btn-cancel" @click="handleCancel">取消</button>
        <button
          class="btn-confirm"
          :disabled="!uploadedImageUrl || uploadLoading"
          @click="handleConfirm"
        >
          {{ uploadLoading ? '提交中...' : '确认提交' }}
        </button>
      </div>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { ElMessage } from "element-plus"; // ElUpload 由 unplugin-element-plus 自动引入
import { Plus } from "@element-plus/icons-vue";
import Modal from "@/components/Modal.vue";
import SvgIcon from "@/components/SvgIcon.vue";
import { uploadToOSS, validateFile } from "@/views/Profile/components/verification/uploadUtils";

// 定义组件名称
defineOptions({
  name: "UploadModal",
});

// 定义 props
interface Props {
  modelValue: boolean; // 控制弹窗显示/隐藏
  loading?: boolean; // 提交加载状态
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
});

// 定义 emits
interface Emits {
  "update:modelValue": [value: boolean]; // 更新弹窗显示状态
  confirm: [imageUrl: string]; // 确认提交事件，传递图片地址
  cancel: []; // 取消事件
}

const emit = defineEmits<Emits>();

// 响应式数据
const uploadLoading = ref<boolean>(false); // 上传加载状态
const uploadedImageUrl = ref<string>(""); // 上传的图片地址
const uploadRef = ref(); // 上传组件引用
const imageRef = ref(); // 图片预览引用

// 计算属性：控制弹窗显示
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit("update:modelValue", value),
});

/**
 * 触发重新上传
 * @param event 点击事件
 */
const triggerReupload = (event: Event): void => {
  event.stopPropagation();
  if (uploadRef.value) {
    uploadRef.value.$el.querySelector('input[type="file"]')?.click();
  }
};

/**
 * 预览图片
 * @param event 点击事件
 */
const previewImage = (event: Event): void => {
  event.stopPropagation();
  if (uploadedImageUrl.value && imageRef.value) {
    imageRef.value.showPreview();
  }
};

/**
 * 上传前的文件校验
 * @param file 要上传的文件
 * @returns boolean 验证是否通过
 */
const handleBeforeUpload = (file: File): boolean => {
  try {
    validateFile(file, 10 * 1024 * 1024); // 10MB限制
    return true;
  } catch (error: any) {
    ElMessage.error(error.message);
    return false;
  }
};

/**
 * 自定义上传处理
 * @param options 上传选项
 */
const handleCustomUpload = async (options: any): Promise<void> => {
  const { file } = options;
  
  try {
    uploadLoading.value = true;
    const fileName = await uploadToOSS(file);
    uploadedImageUrl.value = `https://huigupaimai.oss-cn-beijing.aliyuncs.com/${fileName}`;
    ElMessage.success("图片上传成功");
  } catch (error: any) {
    console.error("图片上传失败:", error);
    ElMessage.error(error.message || "图片上传失败，请重试");
  } finally {
    uploadLoading.value = false;
  }
};

/**
 * 确认提交
 */
const handleConfirm = (): void => {
  if (!uploadedImageUrl.value) {
    ElMessage.error("请先上传凭证图片");
    return;
  }
  
  emit("confirm", uploadedImageUrl.value);
};

/**
 * 取消上传
 */
const handleCancel = (): void => {
  uploadedImageUrl.value = "";
  uploadLoading.value = false;
  emit("cancel");
  visible.value = false;
};
</script>

<style scoped lang="scss">
/* 上传凭证弹窗样式 */
.upload-content {
  padding: 20px 0;
}

.upload-tip {
  text-align: center;
  margin-bottom: 20px;
}

.upload-tip p {
  margin: 5px 0;
  color: #333;
}

.upload-tip .tip-text {
  font-size: 12px;
  color: #999;
}

// 已上传图片显示区域
.image-display {
  width: 200px;
  height: 150px;
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0 auto;
  
  .uploaded-image {
    width: 100%;
    height: 100%;
    border-radius: 6px;
  }
  
  // 重新上传遮罩层
  .upload-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6); // 半透明黑色遮罩
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0; // 默认隐藏
    transition: opacity 0.3s ease;
    border-radius: 6px;
  }
  
  /* 遮罩层操作区域容器 */
  .overlay-actions {
    display: flex;
    gap: 20px;
    align-items: center;
    justify-content: center;
    width: 100%;
  }
  
  /* 操作项样式 */
  .action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 12px 8px;
    border-radius: 6px;
  }
  
  /* 操作图标样式 */
  .action-icon {
    width: 20px;
    height: 20px;
    color: #fff;
    transition: color 0.3s ease;
  }
  
  /* 操作文字样式 */
  .action-text {
    font-size: 12px;
    color: #fff;
    text-align: center;
    line-height: 1.2;
    font-weight: 500;
  }
  
  /* 重新上传操作悬停效果 */
  .reupload-action:hover .action-icon {
    color: #67c23a;
  }
  
  .reupload-action:hover .action-text {
    color: #67c23a;
  }
  
  /* 预览操作悬停效果 */
  .preview-action:hover .action-icon {
    color: #409eff;
  }
  
  .preview-action:hover .action-text {
    color: #409eff;
  }
  
  // 悬停时显示遮罩
  &:hover .upload-overlay {
    opacity: 1;
  }
}

.upload-demo {
  display: flex;
  justify-content: center;
  
  .upload-area {
    width: 200px;
    height: 150px;
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: border-color 0.3s;
    
    &:hover {
      border-color: #ff6b35;
    }
    
    .upload-icon {
      font-size: 28px;
      color: #8c939d;
      margin-bottom: 10px;
    }
    
    .upload-text {
      color: #8c939d;
      font-size: 14px;
    }
  }
}

.upload-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.upload-footer button {
  padding: 8px 20px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.btn-cancel {
  background: #f5f5f5;
  color: #666;
}

.btn-cancel:hover {
  background: #e8e8e8;
}

.btn-confirm {
  background: #ff6b35;
  color: white;
}

.btn-confirm:hover:not(:disabled) {
  background: #e55a2b;
}

.btn-confirm:disabled {
  background: #ccc;
  cursor: not-allowed;
}
</style>