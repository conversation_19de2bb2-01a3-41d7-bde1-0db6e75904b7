<template>
  <div class="account-security">
    <!-- 用户信息顶部 -->
    <div class="user-header">
      <div class="user-avatar">
        <div class="avatar-bg">
          <SvgIcon iconName="user" className="avatar-icon" />
        </div>
        <div class="certification-info">
          <div class="phone-display">
            {{
              userPhone
                ? userPhone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2")
                : ""
            }}
          </div>
          <div class="certification-status">
            <span class="cert-item"
              >个人认证: {{ isPersonalCertified ? "已认证" : "未认证" }}</span
            >
            <span class="cert-item"
              >企业认证: {{ isEnterpriseCertified ? "已认证" : "未认证" }}</span
            >
          </div>
        </div>
      </div>
    </div>

    <!-- 安全设置列表 -->
    <div class="security-list">
      <!-- 登录密码 -->
      <div class="security-item">
        <div class="security-content">
          <div class="label">
            <span class="label-text">登录密码</span>
            <span class="tips"
              >(互联网账号存在被盗风险，建议您定期更改密码以保护账户安全)</span
            >
          </div>
          <div class="value">
            已设置登录密码，建议您定期更改密码以保护账户安全
          </div>
        </div>
        <button class="edit-btn" @click="handleEdit('password')">编辑</button>
      </div>

      <!-- 联系方式 -->
      <div class="security-item">
        <div class="security-content">
          <div class="label">
            <span class="label-text">联系方式</span>
            <span class="tips">(建议您提供有效联系方式，随时随地获得商机)</span>
          </div>
          <div class="value">{{ userPhone || "未绑定手机号" }}</div>
        </div>
        <!-- <button class="edit-btn" @click="handleEdit('phone')">编辑</button> -->
      </div>

      <!-- 注销账号 -->
      <!-- <div class="security-item no-border">
        <div class="security-content">
          <div class="label">
            <span class="label-text">注销账号</span>
            <span class="tips">(账号绑定所有信息数据，注销需谨慎)</span>
          </div>
          <div class="value">注销后将无法使用该账号登录，注销操作不可恢复</div>
        </div>
        <button class="confirm-btn" @click="handleDeleteAccount">确认</button>
      </div> -->
    </div>

    <!-- 修改登录密码弹窗 -->
    <Modal
      v-model="showPasswordModal"
      title="修改登录密码"
      width="658px"
      :confirm-loading="passwordLoading"
      confirm-button-text="确定"
      cancel-button-text="取消"
      @confirm="handlePasswordConfirm"
      @cancel="handlePasswordCancel"
    >
      <div class="password-form">
        <div class="form-item">
          <label class="form-label">当前密码</label>
          <el-input
            v-model="passwordForm.currentPassword"
            type="password"
            placeholder="输入当前的密码"
            show-password
            clearable
          />
        </div>

        <div class="form-item">
          <label class="form-label">新密码</label>
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入8位以上的新密码，需包含大小写字母、数字及特殊字符"
            show-password
            clearable
          />
        </div>

        <div class="form-item">
          <label class="form-label">确认新密码</label>
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次确认新密码"
            show-password
            clearable
          />
        </div>
      </div>
    </Modal>

    <!-- 修改联系方式弹窗 -->
    <Modal
      v-model="showPhoneModal"
      title="修改联系方式"
      width="658px"
      :confirm-loading="phoneLoading"
      confirm-button-text="确定"
      cancel-button-text="取消"
      @confirm="handlePhoneConfirm"
      @cancel="handlePhoneCancel"
    >
      <div class="phone-form">
        <div class="form-item">
          <label class="form-label">当前手机号</label>
          <div class="phone-display">
            <span class="country-code">+86</span>
            <span class="phone-number">{{ phoneForm.currentPhone }}</span>
          </div>
        </div>

        <div class="form-item">
          <label class="form-label">验证码</label>
          <div class="verify-input">
            <el-input
              v-model="phoneForm.verifyCode"
              placeholder="请输入验证码"
              clearable
            />
            <el-button
              type="primary"
              :disabled="countdown > 0"
              @click="getVerifyCode"
              class="verify-btn"
            >
              {{ countdown > 0 ? `${countdown}s` : "获取验证码" }}
            </el-button>
          </div>
        </div>

        <div class="form-item">
          <label class="form-label">新手机号</label>
          <el-input
            v-model="phoneForm.newPhone"
            placeholder="请输入新手机号"
            clearable
          />
        </div>
      </div>
    </Modal>

    <!-- 注销账号弹窗 -->
    <DeleteAccountModal
      v-model="showDeleteAccountModal"
      :loading="deleteAccountLoading"
      @confirm="handleConfirmDeleteAccount"
      @cancel="handleCancelDeleteAccount"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import SvgIcon from "@/components/SvgIcon.vue";
import Modal from "@/components/Modal.vue";
import { ElMessage } from "element-plus"; // ElInput, ElButton 由 unplugin-element-plus 自动引入
import DeleteAccountModal from "@/components/login/DeleteAccountModal.vue";
import { useUserStore } from "@/stores/user";
import { authApi, userApi } from "@/utils/api";
import http from "@/utils/http";
import { getParams } from "@/utils/auth";

// 用户状态管理
const userStore = useUserStore();

// 弹窗状态管理
const showPasswordModal = ref(false); // 修改密码弹窗
const showPhoneModal = ref(false); // 修改联系方式弹窗
const passwordLoading = ref(false); // 修改密码加载状态
const phoneLoading = ref(false); // 修改联系方式加载状态

// 认证状态
const certificationStatus = ref({
  enterqiye: 0, // 企业认证状态
  enteruser: 0, // 个人认证状态
});

// 计算属性
const userPhone = computed(() => {
  return userStore.userInfo?.mobile || "";
});

const isPersonalCertified = computed(() => {
  return certificationStatus.value.enteruser === 1;
});

const isEnterpriseCertified = computed(() => {
  return certificationStatus.value.enterqiye === 1;
});

// 修改密码表单数据
const passwordForm = ref({
  currentPassword: "", // 当前密码
  newPassword: "", // 新密码
  confirmPassword: "", // 确认新密码
});

// 修改联系方式表单数据
const phoneForm = ref({
  currentPhone: "131 2345 6789", // 当前手机号
  verifyCode: "", // 验证码
  newPhone: "", // 新手机号
});

// 验证码倒计时
const countdown = ref(0);
const countdownTimer = ref<ReturnType<typeof setInterval> | null>(null);

// 处理编辑操作
const handleEdit = (type: string) => {
  if (type === "password") {
    showPasswordModal.value = true;
    // 重置表单
    passwordForm.value = {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    };
  } else if (type === "phone") {
    showPhoneModal.value = true;
    // 重置表单
    phoneForm.value = {
      currentPhone: "131 2345 6789",
      verifyCode: "",
      newPhone: "",
    };
  }
};

// 处理确认操作
const handleConfirm = () => {
  // console.log("确认注销账号"); 已移除
};

// 检查认证状态
const getAuthStatus = async () => {
  if (!userStore.userInfo?.id) {
    return;
  }

  try {
    const response = await userApi.checkCertification({
      member_id: userStore.userInfo.id,
    });

    if (response.code === 200) {
      certificationStatus.value = {
        enterqiye: response.data.enterqiye || 0,
        enteruser: response.data.enteruser || 0,
      };
    }
  } catch (error) {
    console.error("获取认证状态失败:", error);
  }
};

// 修改密码确认
const handlePasswordConfirm = async () => {
  // 验证新密码是否一致
  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    ElMessage.error("两次输入的新密码不一致");
    return;
  }

  // 验证密码强度
  if (passwordForm.value.newPassword.length < 6) {
    ElMessage.error("密码长度至少6位");
    return;
  }

  if (!userStore.userInfo?.id) {
    ElMessage.error("用户信息不完整");
    return;
  }

  passwordLoading.value = true;

  try {
    await authApi.changePassword({
      jiu_password: passwordForm.value.currentPassword,
      password: passwordForm.value.newPassword,
      member_id: userStore.userInfo.id,
    });

    // console.log("密码修改成功", passwordForm.value); 已移除
    showPasswordModal.value = false;
    ElMessage.success("密码修改成功");
  } catch (error) {
    console.error("密码修改失败:", error);
    ElMessage.error("密码修改失败");
  } finally {
    passwordLoading.value = false;
  }
};

// 修改密码取消
const handlePasswordCancel = () => {
  showPasswordModal.value = false;
};

// 获取验证码
const getVerifyCode = async () => {
  if (countdown.value > 0) return;

  try {
    // 模拟发送验证码
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 开始倒计时
    countdown.value = 60;
    countdownTimer.value = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(countdownTimer.value!);
        countdownTimer.value = null;
      }
    }, 1000);

    // alert("验证码已发送");
    ElMessage.success("验证码已发送");
  } catch (error) {
    console.error("发送验证码失败:", error);
    // alert("发送验证码失败");
    ElMessage.error("验证码发送失败");
  }
};

// 修改联系方式确认
const handlePhoneConfirm = async () => {
  // 验证手机号格式
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(phoneForm.value.newPhone.replace(/\s/g, ""))) {
    // alert("请输入正确的手机号格式");
    ElMessage.info("请输入正确的手机号格式");
    return;
  }

  // 验证验证码
  if (!phoneForm.value.verifyCode) {
    // alert("请输入验证码");
    ElMessage.info("请输入验证码");
    return;
  }

  phoneLoading.value = true;

  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // console.log("联系方式修改成功", phoneForm.value); 已移除
    showPhoneModal.value = false;
    // alert("联系方式修改成功");
    ElMessage.success("联系方式修改成功");
  } catch (error) {
    console.error("联系方式修改失败:", error);
    // alert("联系方式修改失败");
    ElMessage.error("联系方式修改失败");
  } finally {
    phoneLoading.value = false;
  }
};

// 修改联系方式取消
const handlePhoneCancel = () => {
  showPhoneModal.value = false;
  // 清除倒计时
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
    countdownTimer.value = null;
    countdown.value = 0;
  }
};

// 注销账号相关

// 注销账号弹窗
const showDeleteAccountModal = ref(false); // 注销账号弹窗
const deleteAccountLoading = ref(false); // 注销账号加载状态

// 处理注销账号
const handleDeleteAccount = () => {
  showDeleteAccountModal.value = true;
};

// 确认注销账号
const handleConfirmDeleteAccount = async () => {
  deleteAccountLoading.value = true;

  try {
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // 执行注销账号逻辑
    // console.log("用户已注销账号"); 已移除
  } catch (error) {
    console.error("注销账号失败:", error);
  } finally {
    deleteAccountLoading.value = false;
    // 在loading状态重置后关闭弹窗
    showDeleteAccountModal.value = false;
  }
};

// 取消注销账号
const handleCancelDeleteAccount = () => {
  showDeleteAccountModal.value = false;
};

// 组件挂载时初始化数据
onMounted(() => {
  // 获取认证状态
  getAuthStatus();
});
</script>

<style lang="scss" scoped>
.account-security {
  margin: 30px 20px 20px 20px;
}

// 用户信息顶部样式
.user-header {
  margin-bottom: 32px;

  .user-avatar {
    display: flex;
    align-items: center;
    gap: 9px;
    .avatar-bg {
      background-color: #004c66;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      .avatar-icon {
        width: 24px;
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .certification-info {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .phone-display {
        background-color: #e6eef0;
        border: 1px solid #004c66;
        padding: 5px 10px;
        border-radius: 6px;
        font-size: 12px;
        color: #004c66;
      }

      .certification-status {
        display: flex;
        gap: 12px;

        .cert-item {
          background-color: #f5f5f5;
          border: 1px solid #ddd;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 11px;
          color: #666;
        }
      }
    }
  }
}

// 安全设置列表样式
.security-list {
  .security-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid;
    // 渐变
    border-image: linear-gradient(
      to right,
      rgba(221, 221, 221, 1),
      rgba(255, 255, 255, 1)
    );
    border-image-slice: 1;

    &.no-border {
      border-bottom: none;
    }

    .security-content {
      flex: 1;

      .label {
        font-size: 18px;
        color: #333;
        margin-bottom: 10px;
        .label-text {
          font-family: "PingFang Bold";
        }
        .tips {
          font-size: 12px;
          color: #999;
          margin-left: 10px;
        }
      }

      .value {
        font-size: 14px;
        color: #999;
        line-height: 1.5;
      }
    }

    .edit-btn {
      padding: 8px 32px;
      background: #fff;
      border: 1px solid #ddd;
      border-radius: 6px;
      color: #333;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #004c66;
        color: #004c66;
        background-color: rgba(0, 76, 102, 0.2);
      }
    }

    .confirm-btn {
      padding: 8px 32px;
      background: #fee6e6;
      border: 1px solid #ef0004;
      border-radius: 6px;
      color: #ef0004;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background: rgba($color: #fee6e6, $alpha: 0.2);
      }
    }
  }
}

/* 弹窗表单样式 */
.password-form,
.phone-form {
  padding: 20px;
}

.form-item {
  margin-bottom: 24px;
  .el-input {
    height: 46px;
  }
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 16px;
  color: #333;
  font-family: "PingFang Bold";
}

.phone-display {
  display: flex;
  align-items: center;
  padding: 13px 16px;
  background-color: #f2f2f2;
  border-radius: 6px;
  font-size: 14px;
  color: #ddd;
}

.country-code {
  padding: 0 12px;
  color: #999;
  border-right: 1px solid #e0e0e0;
  margin-right: 12px;
}

.phone-number {
  color: #999;
  font-size: 14px;
}

.verify-input {
  display: flex;
  gap: 12px;
  align-items: center;
}

.verify-input .el-input {
  flex: 1;
  height: 46px;
}

.verify-btn {
  white-space: nowrap;
  min-width: 100px;
  background-color: #fff;
  border: 1px solid #ddd;
  color: #333;
  font-size: 14px;
  height: 46px;
}

/* Element Plus 输入框样式覆盖 */
.password-form .el-input,
.phone-form .el-input {
  --el-input-border-radius: 6px;
  --el-input-border-color: #e0e0e0;
  --el-input-focus-border-color: #004c66;
}

.password-form .el-input__wrapper,
.phone-form .el-input__wrapper {
  padding: 12px 16px;
  box-shadow: 0 0 0 1px var(--el-input-border-color) inset;
}

.password-form .el-input__wrapper:hover,
.phone-form .el-input__wrapper:hover {
  box-shadow: 0 0 0 1px var(--el-input-hover-border-color) inset;
}

.password-form .el-input.is-focus .el-input__wrapper,
.phone-form .el-input.is-focus .el-input__wrapper {
  box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset;
}
</style>
