# DataTable 通用表格组件

一个功能完整的表格组件，支持筛选、分页、排序、批量操作等功能，专为发布板块等业务场景设计。

## 功能特性

- ✅ **按钮切换栏** - 支持多种业务状态切换
- ✅ **筛选栏** - 支持输入框、选择框、日期范围等多种筛选方式
- ✅ **水平导航栏** - 支持标签页切换，显示数量统计
- ✅ **表格展示** - 支持多种数据类型展示（文本、图片、标签、金额、日期等）
- ✅ **分页功能** - 支持页码切换和每页数量调整
- ✅ **排序功能** - 支持列排序
- ✅ **行选择** - 支持单选和多选
- ✅ **批量操作** - 支持批量编辑、删除、导出等操作
- ✅ **操作列** - 内置编辑、删除、查看等常用操作

## 基本用法

```vue
<template>
  <DataTable
    :config="tableConfig"
    :show-selection="true"
    @on-button-click="handleButtonClick"
    @on-filter-change="handleFilterChange"
    @on-tab-change="handleTabChange"
    @on-page-change="handlePageChange"
    @on-edit="handleEdit"
    @on-delete="handleDelete"
    @on-view="handleView"
  />
</template>

<script setup>
import { ref } from 'vue'
import { PublishListConfigFactory } from '@/components/DataTable/config'

const tableConfig = ref(PublishListConfigFactory.createSupplyListConfig())

// 事件处理函数
const handleButtonClick = (key) => {
  console.log('按钮点击:', key)
}

const handleFilterChange = (filters) => {
  console.log('筛选变化:', filters)
}

const handleEdit = (row) => {
  console.log('编辑:', row)
}
</script>
```

## 配置说明

### TableConfig 接口

```typescript
interface TableConfig {
  buttonBar?: ButtonBarConfig     // 按钮切换栏配置
  filterBar?: FilterBarConfig     // 筛选栏配置
  tabBar?: TabBarConfig          // 水平导航栏配置
  columns: TableColumn[]         // 表格列配置
  data?: any[]                   // 表格数据
  pagination?: PaginationConfig  // 分页配置
  loading?: boolean              // 加载状态
}
```

### 按钮栏配置

```typescript
buttonBar: {
  show: true,
  buttons: [
    {
      key: 'all',
      label: '全部任务',
      type: 'primary',
      active: true,
      icon: 'ant-design:appstore-outlined'
    }
  ]
}
```

### 筛选栏配置

```typescript
filterBar: {
  show: true,
  filters: [
    {
      key: 'keyword',
      label: '关键词',
      type: 'input',
      placeholder: '请输入关键词搜索',
      width: '200px'
    },
    {
      key: 'category',
      label: '分类',
      type: 'select',
      placeholder: '请选择分类',
      options: [
        { label: '废钢', value: 'steel' },
        { label: '废铜', value: 'copper' }
      ]
    },
    {
      key: 'dateRange',
      label: '发布时间',
      type: 'daterange',
      width: '300px'
    }
  ]
}
```

### 标签页配置

```typescript
tabBar: {
  show: true,
  activeKey: 'all',
  tabs: [
    { key: 'all', label: '全部供应', count: 156 },
    { key: 'publishing', label: '发布中', count: 45 },
    { key: 'pending', label: '待审核', count: 12 }
  ]
}
```

### 表格列配置

```typescript
columns: [
  {
    key: 'title',
    label: '标题',
    width: '300px',
    align: 'left'
  },
  {
    key: 'price',
    label: '价格',
    width: '120px',
    align: 'right',
    type: 'money'
  },
  {
    key: 'status',
    label: '状态',
    width: '100px',
    align: 'center',
    type: 'tag',
    formatter: (value) => statusMap[value]
  },
  {
    key: 'actions',
    label: '操作',
    width: '150px',
    align: 'center',
    type: 'action'
  }
]
```

## 支持的列类型

- `text` - 普通文本（默认）
- `image` - 图片展示
- `tag` - 标签展示
- `date` - 日期格式化
- `money` - 金额格式化
- `action` - 操作按钮列

## 事件说明

| 事件名 | 参数 | 说明 |
|--------|------|------|
| onButtonClick | key: string | 按钮点击事件 |
| onFilterChange | filters: Record<string, any> | 筛选条件变化 |
| onTabChange | key: string | 标签页切换 |
| onPageChange | page: number, pageSize: number | 分页变化 |
| onSortChange | column: string, order: 'asc' \| 'desc' \| null | 排序变化 |
| onRowClick | row: any, index: number | 行点击 |
| onRowDoubleClick | row: any, index: number | 行双击 |
| onSelectionChange | selectedRows: any[] | 选择变化 |
| onEdit | row: any | 编辑操作 |
| onDelete | row: any | 删除操作 |
| onView | row: any | 查看操作 |

## 预设配置

组件提供了一些预设配置，可以快速创建常用的表格：

```typescript
import { PublishListConfigFactory } from '@/components/DataTable/config'

// 供应信息列表配置
const supplyConfig = PublishListConfigFactory.createSupplyListConfig()

// 求购信息列表配置
const demandConfig = PublishListConfigFactory.createDemandListConfig()

// 简化版配置
const simpleConfig = PublishListConfigFactory.createSimpleListConfig()
```

## 样式定制

组件使用 SCSS 编写样式，支持通过 CSS 变量进行主题定制：

```scss
.data-table-container {
  --primary-color: #004C66;
  --border-color: #e5e5e5;
  --background-color: #fff;
}
```
