<template>
  <div class="upload-card">
    <!-- 已上传图片显示区域 -->
    <div v-if="currentImageUrl" class="image-display">
      <el-image
        ref="imageRef"
        :src="currentImageUrl"
        :preview-src-list="[currentImageUrl]"
        class="uploaded-image"
        fit="cover"
      />
      <!-- 操作按钮遮罩层 -->
      <div class="upload-overlay">
        <div class="overlay-actions">
          <!-- 重新上传区域 -->
          <div class="action-item reupload-action" @click="triggerFileInput">
            <SvgIcon iconName="upload-up" className="action-icon" />
            <span class="action-text">重新上传</span>
          </div>
          <!-- 预览图片区域 -->
          <div class="action-item preview-action" @click="previewImage">
            <SvgIcon iconName="freedom-propertyDetail-eye" className="action-icon" />
            <span class="action-text">预览图片</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 上传区域 -->
    <div v-else class="upload-area" @click="handleUpload">
      <div class="upload-placeholder" :style="backgroundStyle">
        <!-- 上传图标（固定为upload图标） -->
        <SvgIcon iconName="upload-up" className="upload-icon" />
        <!-- 上传文字说明 -->
        <span class="upload-text">{{ uploadText }}</span>
      </div>
    </div>
    
    <!-- 隐藏的文件输入框 -->
    <input
      ref="fileInputRef"
      type="file"
      accept="image/*"
      style="display: none"
      @change="handleFileChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
// ElementPlus 组件由 unplugin-element-plus 自动引入
import SvgIcon from '@/components/SvgIcon.vue'

// 定义组件属性接口
interface Props {
  uploadText: string // 上传文字说明
  backgroundImage?: string // 背景图片URL（可选）
  imageUrl?: string // 已上传的图片URL（可选）
}

// 定义组件事件接口
interface Emits {
  (e: 'upload', file: File): void // 上传文件事件
}

// 接收父组件传入的属性
const props = defineProps<Props>()

// 定义组件事件
const emit = defineEmits<Emits>()

// 文件输入框引用
const fileInputRef = ref<HTMLInputElement>()

// 图片预览引用
const imageRef = ref()

// 当前显示的图片URL（优先使用传入的imageUrl，其次使用本地上传的图片）
const currentImageUrl = ref<string>(props.imageUrl || '')

// 监听props.imageUrl的变化
watch(
  () => props.imageUrl,
  (newUrl) => {
    if (newUrl) {
      currentImageUrl.value = `https://huigupaimai.oss-cn-beijing.aliyuncs.com/${newUrl}`
    }
  },
  { immediate: true }
)

// 计算背景图片样式
const backgroundStyle = computed(() => {
  if (props.backgroundImage) {
    // 处理@/assets路径，转换为正确的URL
    let imageUrl = props.backgroundImage
    if (imageUrl.startsWith('@/')) {
      // 在开发环境中，Vite会自动处理这些路径
      imageUrl = imageUrl.replace('@/', '/src/')
    }
    return {
      backgroundImage: `url(${imageUrl})`
    }
  }
  return {}
})

/**
 * 处理上传点击事件
 */
const handleUpload = () => {
  fileInputRef.value?.click()
}

// 触发文件输入
const triggerFileInput = (event: Event) => {
  // 阻止事件冒泡，避免触发其他点击事件
  event.stopPropagation()
  fileInputRef.value?.click()
}

// 预览图片
const previewImage = (event: Event) => {
  // 阻止事件冒泡，避免触发其他点击事件
  event.stopPropagation()
  
  if (currentImageUrl.value && imageRef.value) {
    // 使用 Element Plus 官方提供的预览方法
    imageRef.value.showPreview()
  }
}

/**
 * 处理文件选择变化事件
 * @param event 文件选择事件
 */
const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file) {
    // 验证文件大小（限制5MB）
    if (file.size > 5 * 1024 * 1024) {
      alert('文件大小不能超过5MB')
      return
    }
    
    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp']
    if (!allowedTypes.includes(file.type)) {
      alert('只支持 gif、png、jpg、bmp 格式的图片')
      return
    }
    
    // 创建本地预览URL
    const localUrl = URL.createObjectURL(file)
    currentImageUrl.value = localUrl
    
    // 触发上传事件
    emit('upload', file)
  }
  
  // 清空输入框值，允许重复选择同一文件
  target.value = ''
}
</script>

<style scoped lang="scss">
.upload-card {
  display: inline-block;
  
  // 已上传图片显示区域
  .image-display {
    width: 101px; // 图片显示区域宽度
    height: 75px; // 图片显示区域高度
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    
    .uploaded-image {
      width: 100%;
      height: 100%;
      border-radius: 10px;
    }
    
    // 重新上传遮罩层
    .upload-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.6); // 半透明黑色遮罩
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0; // 默认隐藏
      transition: opacity 0.3s ease;
      border-radius: 10px;
    }
    
    /* 遮罩层操作区域容器 */
    .overlay-actions {
      display: flex;
      gap: 5px;
      align-items: center;
      justify-content: center;
      width: 100%;
    }
    
    /* 操作项样式 */
    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      cursor: pointer;
      transition: all 0.3s ease;
      padding: 8px 2px;
      border-radius: 6px;
    }
    
    /* 操作图标样式 */
    .action-icon {
      width: 16px;
      height: 16px;
      color: #fff;
      transition: color 0.3s ease;
    }
    
    /* 操作文字样式 */
    .action-text {
      font-size: 10px;
      color: #fff;
      text-align: center;
      line-height: 1.2;
      font-weight: 500;
    }
    
    /* 重新上传操作悬停效果 */
    .reupload-action:hover .action-icon {
      color: #67c23a;
    }
    
    .reupload-action:hover .action-text {
      color: #67c23a;
    }
    
    /* 预览操作悬停效果 */
    .preview-action:hover .action-icon {
      color: #409eff;
    }
    
    .preview-action:hover .action-text {
      color: #409eff;
    }
    
    // 悬停时显示遮罩
    &:hover .upload-overlay {
      opacity: 1;
    }
  }
  
  // 原始上传区域
  .upload-area {
    width: 101px; // 上传组件宽度
    height: 75px; // 上传组件高度
    background-color: #f2f2f2; // 背景色
    border-radius: 10px; // 边框圆角
    padding: 3px; // 内边距
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background-color: #e8e8e8; // 悬停时背景色稍微变深
    }
    
    .upload-placeholder {
      width: 100%;
      height: 100%;
      border: 2px dashed #ccc; // 虚线边框
      border-radius: 7px; // 内部区域圆角
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 4px;
      position: relative; // 相对定位，用于背景图片定位
      background-position: center; // 背景图片居中
      background-repeat: no-repeat; // 背景图片不重复
      
      .upload-icon {
        width: 20px;
        height: 20px;
        color: #999; // 图标颜色
        z-index: 2; // 确保图标在背景图片之上
        position: relative;
      }
      
      .upload-text {
        font-size: 12px;
        color: #999; // 文字颜色
        text-align: center;
        line-height: 1.2;
        z-index: 2; // 确保文字在背景图片之上
        position: relative;
      }
    }
  }
}
</style>