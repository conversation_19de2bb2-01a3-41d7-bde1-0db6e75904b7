<template>
  <!-- 退出登录确认弹窗组件 -->
  <Modal
    v-model="visible"
    title="退出登录"
    width="400px"
    :confirm-loading="loading"
    confirm-button-text="确认退出"
    cancel-button-text="取消"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="logout-confirm-content">
      
      <!-- 主要提示信息 -->
      <p class="logout-message">
        确定要退出登录吗？
      </p>
      
      <!-- 次要提示信息 -->
      <p class="logout-note">
        退出后需要重新登录才能使用相关功能。
      </p>
    </div>
  </Modal>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import Modal from '@/components/Modal.vue'
import SvgIcon from '@/components/SvgIcon.vue'

/**
 * 退出登录确认弹窗组件
 * 用于确认用户是否要退出登录
 */

// 组件属性定义
interface Props {
  /** 控制弹窗显示/隐藏 */
  modelValue: boolean
  /** 确认按钮加载状态 */
  loading?: boolean
}

// 组件事件定义
interface Emits {
  /** 更新modelValue */
  (e: 'update:modelValue', value: boolean): void
  /** 确认退出登录 */
  (e: 'confirm'): void
  /** 取消退出登录 */
  (e: 'cancel'): void
}

// 接收props
const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 定义事件
const emit = defineEmits<Emits>()

// 计算属性：控制弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

/**
 * 处理确认退出登录
 * 触发confirm事件，由父组件处理具体的退出逻辑
 */
const handleConfirm = () => {
  emit('confirm')
}

/**
 * 处理取消退出登录
 * 关闭弹窗并触发cancel事件
 */
const handleCancel = () => {
  emit('cancel')
  visible.value = false
}
</script>

<style lang="scss" scoped>
/**
 * 退出登录确认弹窗样式
 * 包含图标、消息文本和提示文本的样式定义
 */
.logout-confirm-content {
  padding: 20px 0;
  text-align: center;
  
  // 警告图标容器
  .logout-icon {
    margin-bottom: 16px;
    
    // 警告图标样式
    :deep(.warning-icon) {
      width: 48px;
      height: 48px;
      color: #f39c12; // 橙色警告色
    }
  }
  
  // 主要提示消息样式
  .logout-message {
    font-size: 18px;
    color: #333;
    font-weight: 600;
    margin-bottom: 12px;
    line-height: 1.5;
  }
  
  // 次要提示信息样式
  .logout-note {
    font-size: 14px;
    color: #666;
    margin: 0;
    line-height: 1.5;
  }
}
</style>