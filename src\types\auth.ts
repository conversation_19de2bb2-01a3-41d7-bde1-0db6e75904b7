/**
 * 用户类型枚举
 */
export enum UserType {
  PERSONAL = 'personal', // 个人用户
  ENTERPRISE = 'enterprise', // 企业用户
  ADMIN = 'admin' // 管理员
}

/**
 * 登录表单接口
 */
export interface LoginForm {
  userType: string // 用户类型
  username: string // 用户名（手机号或邮箱）
  password: string // 密码
  rememberMe: boolean // 记住密码
}

/**
 * 登录响应接口
 */
export interface LoginResponse {
  token: string // 访问令牌
  refreshToken: string // 刷新令牌
  userInfo: UserInfo // 用户信息
}

/**
 * 用户信息接口
 */
export interface UserInfo {
  id: string // 用户ID
  username: string // 用户名
  email: string // 邮箱
  phone: string // 手机号
  userType: UserType // 用户类型
  avatar?: string // 头像
  nickname?: string // 昵称
  createTime: string // 创建时间
  lastLoginTime?: string // 最后登录时间
}