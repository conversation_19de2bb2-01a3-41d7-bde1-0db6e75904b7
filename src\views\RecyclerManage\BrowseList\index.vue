<template>
  <div class="browse-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>浏览记录</h2>
      <p>共 {{ total }} 条浏览记录</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-text">加载中...</div>
    </div>

    <!-- 空状态 -->
    <div
      v-else-if="!loading && propertyList.length === 0"
      class="empty-container"
    >
      <div class="empty-text">暂无浏览记录</div>
    </div>

    <!-- 资产卡片列表 -->
    <div v-else class="property-grid">
      <PropertyCard
        v-for="item in propertyList"
        :key="item.productId"
        v-bind="item"
        @click="handleCardClick"
      />
    </div>

    <!-- 分页组件 -->
    <div v-if="total > 0" class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        layout="prev, pager, next, jumper"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import PropertyCard from "@/components/PropertyCard.vue";
import { systemApi } from "@/utils/api-new";
import type { PropertyItem } from "@/types/property";

const router = useRouter();

// 响应式数据
const loading = ref(false);
const propertyList = ref<PropertyItem[]>([]);
const currentPage = ref(1);
const pageSize = ref(12);
const total = ref(0);

/**
 * 获取浏览记录列表数据
 */
const fetchBrowseList = async () => {
  try {
    loading.value = true;

    const params = {
      pageNo: currentPage.value,
      pageSize: pageSize.value,
    };

    const response = await systemApi.getBrowseList(params);

    if (response && response.success) {
      // 转换数据格式以适配PropertyCard组件
      const list = response.result?.records?.map((item: any) => ({
        productId: item.itemId || item.id || '',
        productName: item.logContent || `商品${item.itemId}` || '未知商品',
        productImage: '', // 日志数据中没有图片信息
        currentPrice: 0, // 日志数据中没有价格信息
        priceUnit: '元',
        viewCount: 0, // 日志数据中没有浏览量信息
        enterpriseLogo: '',
        enterpriseName: '未知企业',
        enterpriseType: '企业',
        status: '5',
        statusName: '已浏览',
        productCount: 1,
        productCountUnit: '件',
        productWeight: 0,
        productWeightUnit: 'kg',
        productPower: undefined,
        productPowerUnit: 'kw',
        // 添加日志相关信息
        browseTime: item.createTime, // 浏览时间
        logType: item.logType, // 日志类型
        operateType: item.operateType, // 操作类型
      })) || [];

      propertyList.value = list;
      total.value = response.result?.total || 0;
    } else {
      ElMessage.error(response?.message || "获取浏览记录失败");
    }
  } catch (error) {
    console.error("获取浏览记录失败:", error);
    ElMessage.error("获取浏览记录失败");
  } finally {
    loading.value = false;
  }
};

/**
 * 处理页码变化
 */
const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchBrowseList();
};

/**
 * 处理卡片点击事件
 */
const handleCardClick = (data: { productId: string; productName: string }) => {
  // 跳转到资产详情页
  router.push({
    name: "propertyDetail",
    query: { id: data.productId },
  });
};

// 组件挂载时获取数据
onMounted(() => {
  fetchBrowseList();
});
</script>

<style scoped lang="scss">
.browse-list {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;

    h2 {
      font-size: 24px;
      color: #333;
      margin: 0 0 8px 0;
    }

    p {
      font-size: 14px;
      color: #666;
      margin: 0;
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;

    .loading-text {
      font-size: 16px;
      color: #666;
    }
  }

  .empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;

    .empty-text {
      font-size: 16px;
      color: #999;
    }
  }

  .property-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(295px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 30px;
  }
}
</style>