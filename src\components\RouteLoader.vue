<template>
  <div class="route-loader">
    <!-- 路由组件包装器 -->
    <router-view v-slot="{ Component, route }">
      <transition
        :name="shouldApplyTransition(route.path) ? 'fade' : ''"
        mode="out-in"
        @before-leave="onBeforeLeave"
        @after-enter="onAfterEnter"
      >
        <div
          v-if="Component"
          :key="route.path"
          class="route-content"
        >
          <component :is="Component" />
        </div>
        <!-- 加载占位符 -->
        <div
          v-else
          class="route-loading"
          key="loading"
        >
          <div class="loading-skeleton">
            <div class="skeleton-header"></div>
            <div class="skeleton-content">
              <div class="skeleton-line"></div>
              <div class="skeleton-line"></div>
              <div class="skeleton-line short"></div>
            </div>
          </div>
        </div>
      </transition>
    </router-view>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 是否正在加载
const isLoading = ref(false)

// 判断是否应用过渡动画
function shouldApplyTransition(path: string): boolean {
  // 对主要页面应用过渡动画
  const mainPages = ['/', '/about', '/bidding', '/freedom']
  return mainPages.some(page => path.startsWith(page))
}

// 离开前的处理
function onBeforeLeave() {
  isLoading.value = true
}

// 进入后的处理
function onAfterEnter() {
  isLoading.value = false
}
</script>

<style scoped lang="scss">
.route-loader {
  width: 100%;
  min-height: 400px;
  position: relative;
}

.route-content {
  width: 100%;
  min-height: inherit;
}

.route-loading {
  width: 100%;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

/* 骨架屏样式 */
.loading-skeleton {
  width: 100%;
  max-width: 800px;
  padding: 20px;
}

.skeleton-header {
  height: 60px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 20px;
}

.skeleton-content {
  .skeleton-line {
    height: 20px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
    margin-bottom: 12px;
    
    &.short {
      width: 60%;
    }
  }
}

/* 淡入淡出过渡 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}

/* 骨架屏加载动画 */
@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 确保过渡期间布局稳定 */
.fade-enter-active,
.fade-leave-active {
  position: relative;
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-skeleton {
    padding: 15px;
  }
  
  .skeleton-header {
    height: 40px;
  }
  
  .skeleton-content .skeleton-line {
    height: 16px;
    margin-bottom: 10px;
  }
}
</style>
