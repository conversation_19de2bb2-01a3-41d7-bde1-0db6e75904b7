import { ref, computed } from "vue";
import { defineStore } from "pinia";

/**
 * 自由交易模块用户信息接口
 * 与老系统的用户信息区分开
 */
export interface FreedomUserInfo {
  id: string;
  username: string;
  realname?: string;
  avatar?: string;
  phone?: string;
  email?: string;
  token: string;
  [key: string]: any;
}

/**
 * 自由交易模块用户状态管理
 * 独立于老系统的用户状态，使用不同的localStorage key
 */
export const useFreedomUserStore = defineStore("freedomUser", () => {
  // 自由交易模块用户信息
  const freedomUserInfo = ref<FreedomUserInfo | null>(null);

  // 自由交易模块登录状态
  const isFreedomLoggedIn = computed(() => {
    return freedomUserInfo.value !== null && freedomUserInfo.value.token !== "";
  });

  /**
   * 自由交易模块登录
   * @param user 用户信息
   */
  const freedomLogin = (user: FreedomUserInfo) => {
    freedomUserInfo.value = user;
    // 保存到localStorage，使用不同的key与老系统区分
    localStorage.setItem("freedomToken", user.token);
    localStorage.setItem("freedomUserInfo", JSON.stringify(user));
    localStorage.setItem("isFreedomLoggedIn", "true");
  };

  /**
   * 自由交易模块登出
   */
  const freedomLogout = () => {
    freedomUserInfo.value = null;
    // 清除localStorage中的自由交易登录信息
    localStorage.removeItem("freedomUserInfo");
    localStorage.removeItem("isFreedomLoggedIn");
    localStorage.removeItem("freedomToken");
  };

  /**
   * 初始化自由交易模块用户状态（从localStorage恢复）
   */
  const initFreedomUserState = () => {
    const savedUserInfo = localStorage.getItem("freedomUserInfo");
    const savedLoginState = localStorage.getItem("isFreedomLoggedIn");

    if (savedLoginState === "true" && savedUserInfo) {
      try {
        freedomUserInfo.value = JSON.parse(savedUserInfo);
      } catch (error) {
        console.error("解析自由交易用户信息失败:", error);
        freedomLogout();
      }
    }
  };

  /**
   * 获取自由交易模块token
   * @returns {string} token
   */
  const getFreedomToken = (): string => {
    return localStorage.getItem("freedomToken") || "";
  };

  /**
   * 检查自由交易模块是否已登录
   * @returns {boolean} 是否已登录
   */
  const checkFreedomLogin = (): boolean => {
    const token = getFreedomToken();
    const loginState = localStorage.getItem("isFreedomLoggedIn");
    return loginState === "true" && token !== "" && token !== "undefined";
  };

  return {
    freedomUserInfo,
    isFreedomLoggedIn,
    freedomLogin,
    freedomLogout,
    initFreedomUserState,
    getFreedomToken,
    checkFreedomLogin,
  };
});