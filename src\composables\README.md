# Composables 使用说明

本目录包含了项目中可复用的组合式函数（Composables），遵循 Vue 3 Composition API 的最佳实践。

## useWebSocket

WebSocket连接管理的组合式函数，专门用于拍卖详情页面的实时通信功能。

### 功能特性

- 🔌 自动管理WebSocket连接生命周期
- 💓 内置心跳机制保持连接稳定
- 🔊 支持音频提示功能
- 📨 灵活的消息处理回调机制
- 🧹 自动资源清理
- 🔄 支持动态心跳数据

### 基本用法

```typescript
import { useWebSocket } from '@/composables/useWebSocket'

const {
  // 响应式数据
  websock,           // WebSocket连接实例
  isOnline,          // 连接状态
  audioRef,          // 音频元素引用
  
  // 方法
  initWebSocket,     // 初始化WebSocket连接
  websocketsend,     // 发送WebSocket消息
  closeWebSocket,    // 关闭WebSocket连接
} = useWebSocket()

// 消息处理回调函数
const handleMessage = (data: any) => {
  console.log('收到消息:', data)
  // 处理接收到的消息
}

// 心跳数据获取函数
 const getHeartbeatData = () => ({
   fayanId: fayanList.value.length === 0 ? 0 : fayanList.value[0].id,
   chujiaiId: chujiailist.value.length === 0 ? 0 : chujiailist.value[0].id,
 })

// 初始化WebSocket连接
initWebSocket(
  targetId.value,      // 标的ID
  pmhId.value,         // 拍卖会ID
  userId.value,        // 用户ID
  handleMessage,       // 消息处理回调
  getHeartbeatData     // 心跳数据获取函数（可选）
)
```

### 在模板中使用

```vue
<template>
  <div>
    <!-- 连接状态指示器 -->
    <div class="connection-status" :class="{ online: isOnline }">
      {{ isOnline ? '已连接' : '未连接' }}
    </div>
    
    <!-- 音频播放元素 -->
    <audio ref="audioRef" preload="auto">
      <source src="/audio/notification.mp3" type="audio/mpeg">
      <source src="/audio/notification.wav" type="audio/wav">
      您的浏览器不支持音频播放。
    </audio>
  </div>
</template>
```

### API 参考

#### 响应式数据

| 属性 | 类型 | 说明 |
|------|------|------|
| `websock` | `Ref<WebSocket \| null>` | WebSocket连接实例 |
| `isOnline` | `Ref<boolean>` | WebSocket连接状态 |
| `audioRef` | `Ref<HTMLAudioElement \| null>` | 音频元素引用 |

#### 方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|---------|
| `initWebSocket` | `targetId, pmhId, userId, callback, getHeartbeatData?` | `void` | 初始化WebSocket连接 |
| `websocketsend` | `data: any` | `void` | 发送WebSocket消息 |
| `closeWebSocket` | - | `void` | 关闭WebSocket连接并清理资源 |

#### 参数说明

- `targetId`: 标的ID
- `pmhId`: 拍卖会ID
- `userId`: 用户ID
- `callback`: 消息处理回调函数
- `getHeartbeatData`: 获取心跳数据的函数（可选）

### 使用场景

1. **拍卖详情页**: 实时接收出价信息、发言记录、状态变化
2. **实时通知**: 接收系统通知和提醒
3. **数据同步**: 保持页面数据与服务器同步

### 注意事项

- 组件卸载时会自动清理WebSocket连接和定时器
- 心跳机制每秒发送一次，保持连接稳定
- 音频播放需要用户交互后才能正常工作（浏览器限制）
- WebSocket连接失败时会在控制台输出错误信息
- 建议在拍卖进行中才建立WebSocket连接，避免不必要的资源消耗

## useAuctions

拍卖数据管理的组合式函数，提供获取即将开始和已完成拍品的功能。

### 功能特性

- 🔄 自动管理加载状态
- 📊 提供即将开始和已完成的拍品数据
- 🚀 支持并行数据获取
- 🎯 可配置返回数量限制
- 🔄 提供数据刷新功能

### 基本用法

```typescript
import { useAuctions } from '@/composables/useAuctions'

const {
  // 响应式数据
  auctionItems,      // 即将开始的拍品
  accomplishItems,   // 已完成的拍品
  loading,           // 加载状态
  
  // 方法
  initAuctions,      // 初始化所有数据
  refreshAuctions,   // 刷新所有数据
  getUpcomingAuctions,    // 获取即将开始的拍品
  getAccomplishAuctions   // 获取已完成的拍品
} = useAuctions()

// 在组件挂载时初始化数据
onMounted(async () => {
  await initAuctions()
})
```

### 在模板中使用

```vue
<template>
  <div>
    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <div class="loading-text">加载中...</div>
    </div>
    
    <!-- 即将开始的拍品 -->
    <div v-else-if="auctionItems.length > 0" class="auction-cards">
      <AuctionCard
        v-for="item in auctionItems"
        :key="item.id"
        v-bind="item"
        @click="handleCardClick"
      />
    </div>
    
    <!-- 空状态 -->
    <div v-else class="placeholder">
      <div class="placeholder-text">暂无拍品信息</div>
    </div>
  </div>
</template>
```

### API 参考

#### 响应式数据

| 属性 | 类型 | 说明 |
|------|------|------|
| `auctionItems` | `Ref<AuctionItem[]>` | 即将开始的拍品列表 |
| `accomplishItems` | `Ref<AuctionItem[]>` | 已完成的拍品列表 |
| `loading` | `Ref<boolean>` | 数据加载状态 |

#### 方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `initAuctions` | `limit?: number` | `Promise<void>` | 初始化所有拍卖数据 |
| `refreshAuctions` | `limit?: number` | `Promise<void>` | 刷新所有拍卖数据 |
| `getUpcomingAuctions` | `limit?: number` | `Promise<void>` | 获取即将开始的拍品 |
| `getAccomplishAuctions` | `limit?: number` | `Promise<void>` | 获取已完成的拍品 |

#### 参数说明

- `limit`: 限制返回的数据数量，默认为 4

### 使用场景

1. **首页展示**: 展示推荐拍品和成交案例
2. **拍卖列表页**: 展示不同状态的拍品
3. **数据刷新**: 用户手动刷新或定时刷新数据

### 注意事项

- 该 hooks 会自动处理错误情况，错误信息会在控制台输出
- 加载状态会在数据获取过程中自动管理
- 建议在组件的 `onMounted` 生命周期中调用 `initAuctions`
- 如需自定义数据获取逻辑，可以单独调用 `getUpcomingAuctions` 或 `getAccomplishAuctions`

### 样式建议

为了更好的用户体验，建议为加载状态和空状态添加相应的样式：

```scss
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  border-radius: 10px;
  background-color: #f9f9f9;
}

.loading-text {
  font-size: 16px;
  color: #666666;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  border-radius: 10px;
  background-color: #f5f5f5;
}

.placeholder-text {
  font-size: 16px;
  color: #999999;
}
```