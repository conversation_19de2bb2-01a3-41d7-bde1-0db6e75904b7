<template>
  <div class="enterprise-detail">
    <!-- 企业详情容器 -->
    <div class="enterprise-container">
      <!-- 上部分：标题和观看数 -->
      <div class="header-section">
        <div class="breadcrumb">
          <template v-if="route.query.crumbsTitle !== '自由交易'">
            <span class="free-title" @click="handleGoBack('自由交易')"
              >自由交易</span
            >
            <span class="separator">></span>
          </template>
          <span
            class="free-title"
            @click="handleGoBack('other')"
            >{{ route.query.productName }}</span
          >
          <span class="separator">></span>
          <span class="current">{{ enterpriseData.name }}</span>
        </div>
      </div>

      <!-- 上半部：公司资料简介 -->
      <div class="company-profile">
        <!-- 左侧：公司信息 -->
        <div class="company-info">
          <!-- 公司logo -->
          <div class="company-logo">
            <img :src="enterpriseData.logo" :alt="enterpriseData.name" />
          </div>
          <!-- 公司详细信息 -->
          <div class="company-details">
            <!-- 公司名称和标签 -->
            <div class="company-header">
              <span class="company-name">{{ enterpriseData.name }}</span>
              <div class="company-type">
                <CompanyTag :enterpriseType="enterpriseData.type" />
              </div>
            </div>
            <!-- 账号和粉丝量 -->
            <div class="company-stats">
              <div class="stat-item">
                <span class="stat-number">{{
                  enterpriseData.accountCount
                }}</span>
                <span class="stat-label">账号</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{ enterpriseData.fansCount }}</span>
                <span class="stat-label">万粉丝</span>
              </div>
            </div>
          </div>
        </div>
        <!-- 右侧：关注按钮 -->
        <div class="follow-section">
          <el-button
            type="primary"
            :class="{ followed: isFollowed }"
            @click="toggleFollow"
          >
            {{ isFollowed ? "已关注" : "+ 关注" }}
          </el-button>
        </div>
      </div>

      <!-- 下半部：导航和内容区域 -->
      <div class="content-section">
        <!-- 导航栏 -->
        <div class="nav-tabs">
          <div
            v-for="tab in tabs"
            :key="tab.key"
            :class="['nav-tab', { active: activeTab === tab.key }]"
            @click="switchTab(tab.key)"
          >
            {{ tab.label }}
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="tab-content">
          <!-- 供应信息 -->
          <div v-if="activeTab === 'supply'" class="supply-content">
            <div class="property-cards">
              <PropertyCard
                v-for="item in supplyItems"
                :key="item.productId"
                v-bind="item"
                @click="handleCardClick"
              />
            </div>
          </div>

          <!-- 求购信息 -->
          <div v-if="activeTab === 'demand'" class="demand-content">
            <div class="property-cards">
              <PropertyCard
                v-for="item in demandItems"
                :key="item.productId"
                v-bind="item"
                @click="handleCardClick"
              />
            </div>
          </div>

          <!-- 成交案例 -->
          <div v-if="activeTab === 'cases'" class="cases-content">
            <div class="empty-state">
              <p>暂无成交案例</p>
            </div>
          </div>

          <!-- 主营业务 -->
          <div v-if="activeTab === 'business'" class="business-content">
            <div class="empty-state">
              <p>暂无主营业务信息</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import PropertyCard from "@/components/PropertyCard.vue";
import CompanyTag from "@/components/CompanyTag.vue";
import type { PropertyItem } from "@/types/property";
import LogoImg from "@/assets/images/company.png";

// 路由实例
const route = useRoute();
const router = useRouter();

// 企业ID
const enterpriseId = ref<string>(route.params.id as string);

// 关注状态
const isFollowed = ref<boolean>(false);

// 当前激活的标签页
const activeTab = ref<string>("supply");

// 导航标签配置
const tabs = ref([
  { key: "supply", label: "供应信息" },
  { key: "demand", label: "求购信息" },
  { key: "cases", label: "成交案例" },
  { key: "business", label: "主营业务" },
]);

// 企业数据
const enterpriseData = reactive({
  id: enterpriseId.value,
  name: "天瑞水泥集团有限公司",
  type: "国企",
  logo: LogoImg,
  accountCount: 5,
  fansCount: 12.5,
  description: "专业从事水泥生产和销售的大型企业集团",
});

// 模拟供应信息数据
const supplyItems = ref<PropertyItem[]>([
  {
    productId: "2",
    productName: "混凝土搅拌车",
    productImage:
      "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/********/**************.png",
    currentPrice: 1800.0,
    priceUnit: "元/吨",
    statusName: "待发布",
    productCount: 1,
    productCountUnit: "辆",
    productWeight: 10,
    productWeightUnit: "吨",
    viewCount: 2800,
    enterpriseLogo: LogoImg,
    enterpriseName: "天瑞水泥集团有限公司",
    enterpriseType: "国企",
    status: "upcoming",
  },
  {
    productId: "2",
    productName: "混凝土搅拌车",
    productImage:
      "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/********/**************.png",
    currentPrice: 1800.0,
    priceUnit: "元/吨",
    statusName: "待发布",

    productCount: 1,
    productCountUnit: "辆",
    productWeight: 10,
    productWeightUnit: "吨",
    viewCount: 2800,
    enterpriseLogo: LogoImg,
    enterpriseName: "天瑞水泥集团有限公司",
    enterpriseType: "国企",
    status: "upcoming",
  },
  {
    productId: "2",
    productName: "混凝土搅拌车",
    productImage:
      "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/********/**************.png",
    currentPrice: 1800.0,
    statusName: "待发布",

    priceUnit: "元/吨",
    productCount: 1,
    productCountUnit: "辆",
    productWeight: 10,
    productWeightUnit: "吨",
    viewCount: 2800,
    enterpriseLogo: LogoImg,
    enterpriseName: "天瑞水泥集团有限公司",
    enterpriseType: "国企",
    status: "upcoming",
  },
  {
    productId: "2",
    productName: "混凝土搅拌车",
    productImage:
      "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/********/**************.png",
    currentPrice: 1800.0,
    statusName: "待发布",

    priceUnit: "元/吨",
    productCount: 1,
    productCountUnit: "辆",
    productWeight: 10,
    productWeightUnit: "吨",
    viewCount: 2800,
    enterpriseLogo: LogoImg,
    enterpriseName: "天瑞水泥集团有限公司",
    enterpriseType: "国企",
    status: "upcoming",
  },
  {
    productId: "2",
    productName: "混凝土搅拌车",
    productImage:
      "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/********/**************.png",
    currentPrice: 1800.0,
    priceUnit: "元/吨",
    statusName: "待发布",

    productCount: 1,
    productCountUnit: "辆",
    productWeight: 10,
    productWeightUnit: "吨",
    viewCount: 2800,
    enterpriseLogo: LogoImg,
    enterpriseName: "天瑞水泥集团有限公司",
    enterpriseType: "国企",
    status: "upcoming",
  },
]);

// 模拟求购信息数据
const demandItems = ref<PropertyItem[]>([
  {
    productId: "3",
    productName: "二手装载机求购",
    productImage:
      "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/********/**************.png",
    currentPrice: 1500.0,
    priceUnit: "元/吨",
    statusName: "待发布",

    productCount: 1,
    productCountUnit: "辆",
    productWeight: 10,
    productWeightUnit: "吨",
    viewCount: 1200,
    enterpriseLogo: LogoImg,
    enterpriseName: "天瑞水泥集团有限公司",
    enterpriseType: "国企",
    status: "ongoing",
  },
]);

// 点击面包屑回到freedomHome
const handleGoBack = (title: string) => {
  if (title === "自由交易") {
    router.push({
      name: "freedomHome",
    });
  } else {
    router.back();
  }
};

/**
 * 切换关注状态
 */
const toggleFollow = () => {
  isFollowed.value = !isFollowed.value;
  ElMessage.success(isFollowed.value ? "关注成功" : "取消关注成功");
};

/**
 * 切换标签页
 * @param tabKey 标签页key
 */
const switchTab = (tabKey: string) => {
  activeTab.value = tabKey;
};

/**
 * 处理卡片点击事件
 * @param productId 产品ID
 */
const handleCardClick = (value: any) => {
  // console.log(`点击了商品ID: ${value.productId},${value.productName}`);
  // 跳转到资产详情页面
  router.push({
    name: "propertyDetail",
    query: { id: value.productId, crumbsTitle: enterpriseData.name },
  });
};

/**
 * 加载企业详情数据
 */
const loadEnterpriseDetail = async () => {
  try {
    // TODO: 调用API获取企业详情数据
    // console.log("加载企业详情:", enterpriseId.value);
    // 这里可以根据enterpriseId加载对应的企业数据
  } catch (error) {
    console.error("加载企业详情失败:", error);
    ElMessage.error("加载企业详情失败");
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadEnterpriseDetail();
});
</script>

<style lang="scss" scoped>
.enterprise-detail {
  margin: 0 auto;
  max-width: 1280px;
  margin-top: 20px;
  margin-bottom: 30px;

  .enterprise-container {
    background-color: #fff;
    border-radius: 10px;
    padding: 15px 20px 20px 20px;

    .header-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 20px;
      border-bottom: 1px solid #e5e5e5;

      .breadcrumb {
        font-size: 14px;
        color: #666;

        .free-title {
          cursor: pointer;
        }

        .separator {
          margin: 0 8px;
        }

        .current {
          color: #004c66;
          font-weight: 500;
          cursor: pointer;
        }
      }

      .view-count {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #666;

        .eye-icon {
          width: 16px;
          height: 16px;
          margin-right: 5px;
        }
      }
    }

    // 公司资料简介区域
    .company-profile {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 25px;

      .company-info {
        display: flex;
        align-items: center;
        flex: 1;

        .company-logo {
          width: 72px;
          height: 72px;
          border-radius: 50%;
          overflow: hidden;
          margin-right: 10px;
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;

          img {
            width: 41px;
            height: 48px;
            object-fit: cover;
          }
        }

        .company-details {
          flex: 1;

          .company-header {
            display: flex;
            align-items: center;
            margin-bottom: 5px;

            .company-name {
              font-size: 18px;
              font-family: "PingFang Bold";
              color: #333;
              margin-right: 5px;
            }

            .company-type {
              flex-shrink: 0;
            }
          }

          .company-stats {
            display: flex;
            gap: 24px;

            .stat-item {
              display: flex;
              align-items: baseline;
              gap: 4px;

              .stat-number {
                font-size: 22px;
                font-family: "DIN Regular";
                color: #333;
              }

              .stat-label {
                font-size: 12px;
                color: #999;
              }
            }
          }
        }
      }

      .follow-section {
        flex-shrink: 0;
        padding-top: 2px;

        .el-button {
          padding: 8px 24px;
          font-size: 16px;
          border-radius: 6px;
          background-color: #004c66;
          border-color: #004c66;

          &:hover {
            background-color: #003a4d;
            border-color: #003a4d;
          }

          &.followed {
            background-color: #f0f0f0;
            color: #666;
            border-color: #d9d9d9;

            &:hover {
              background-color: #e6e6e6;
              border-color: #d9d9d9;
            }
          }
        }
      }
    }

    // 内容区域
    .content-section {
      .nav-tabs {
        display: flex;
        border-bottom: 1px solid;
        border-image: linear-gradient(
            to right,
            rgb(221, 221, 221),
            rgb(255, 255, 255)
          )
          1;
        margin-bottom: 20px;

        .nav-tab {
          padding: 15px 0;
          margin-right: 20px;
          font-size: 16px;
          color: #666;
          cursor: pointer;
          border-bottom: 2px solid transparent;
          transition: all 0.3s ease;
          position: relative;
          bottom: -1px;

          &:hover {
            color: #004c66;
          }

          &.active {
            color: #004c66;
            border-bottom-color: #004c66;
          }
        }
      }

      .tab-content {
        .property-cards {
          display: flex;
          gap: 20px;
          flex-wrap: wrap;
        }

        .empty-state {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 200px;
          color: #999;
          font-size: 16px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .enterprise-detail {
    .enterprise-container {
      .company-profile {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;

        .company-info {
          .company-logo {
            width: 60px;
            height: 60px;
          }

          .company-details {
            .company-header {
              .company-name {
                font-size: 20px;
              }
            }

            .company-stats {
              gap: 16px;

              .stat-item {
                .stat-number {
                  font-size: 16px;
                }
              }
            }
          }
        }
      }

      .content-section {
        .nav-tabs {
          .nav-tab {
            padding: 10px 16px;
            font-size: 14px;
          }
        }

        .tab-content {
          .property-cards {
            gap: 16px;
          }
        }
      }
    }
  }
}
</style>
