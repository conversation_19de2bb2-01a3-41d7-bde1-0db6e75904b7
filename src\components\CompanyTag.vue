<template>
  <div
    class="enterprise-type"
    :style="{
      'background-color':
        enterpriseType === '国企'
          ? '#5fa4c0'
          : enterpriseType === '央企'
          ? '#418EAB'
          : enterpriseType === '民企'
          ? '#4BAA9C'
          : '#5C6BC0',
    }"
  >
    <p>
      {{ enterpriseType }}
    </p>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive } from "vue";
const props = defineProps({
  enterpriseType: {
    type: String,
    default: "民企",
  },
});
const { enterpriseType } = props;
</script>
<style lang="scss" scoped>
.enterprise-type {
  position: relative;
  font-size: 16px;
  font-family: "YouSheBiaoTiHei";
  color: #fff;
  width: 36px;
  height: 17px;
  line-height: 17px;
  text-align: center;
  border-radius: 2px;
  transform: skew(-10deg);
  p {
    transform: skew(10deg);
  }
}
</style>
