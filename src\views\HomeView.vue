<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import AuctionCard from "../components/AuctionCard.vue";
import AuctionListItem from "../components/AuctionListItem.vue";
import type { ListItem } from "../types/auction";
import { useAuctions } from "../composables/useAuctions";
import { homeApi, auctionSessionApi, otherApi } from "../utils/api";

const router = useRouter();

// 使用拍卖数据hooks
const { auctionItems, accomplishItems, loading, initAuctions } = useAuctions();

// 拍卖会公告数据 - 第一个公告区域
const auctionAnnouncements = ref<ListItem[]>([]);
// 销售公告数据 - 第二个公告区域  
const salesAnnouncements = ref<ListItem[]>([]);
// 公告数据加载状态
const announcementsLoading = ref(false);

// 模拟列表项数据 - 保留作为备用数据，可以在后期删除
/* 模拟数据开始 - 可折叠删除 */
const listItems = ref<ListItem[]>([
  {
    productId: "101",
    productName: "标的1: 小松PC260LC-11MO 挖掘机",
    productImage:
      "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250321/17425257163318.png",
    description:
      "应客户的要求，河南麦工拍卖有限公司定于2025年06月10日 15:00:00时在线竞价系统对河南省郑州市某企业一批挖掘机物资进行公开竞价处置。",
    companyName: "河南郑州市某企业",
    timeValue: "2025/10/10",
    likeCount: 455,
    commentCount: 455,
    viewCount: 4555,
  },
  {
    productId: "102",
    productName: "标的2: 卡特彼勒320D2 GC 挖掘机",
    productImage:
      "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250613/17497843321079.png",
    description:
      "应客户的要求，河南麦工拍卖有限公司定于2025年06月15日 15:00:00时在线竞价系统对河南省郑州市某企业一批挖掘机物资进行公开竞价处置。",
    companyName: "河南郑州市某企业",
    timeValue: "2025/10/15",
    likeCount: 325,
    commentCount: 210,
    viewCount: 3800,
  },
  {
    productId: "103",
    productName: "标的2: 卡特彼勒320D2 GC 挖掘机",
    productImage:
      "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250603/17489456557790.png",
    description:
      "应客户的要求，河南麦工拍卖有限公司定于2025年06月15日 15:00:00时在线竞价系统对河南省郑州市某企业一批挖掘机物资进行公开竞价处置。",
    companyName: "河南郑州市某企业",
    timeValue: "2025/10/15",
    likeCount: 325,
    commentCount: 210,
    viewCount: 3800,
  },
  {
    productId: "104",
    productName: "标的2: 卡特彼勒320D2 GC 挖掘机",
    productImage:
      "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250611/17496321666592.png",
    description:
      "应客户的要求，河南麦工拍卖有限公司定于2025年06月15日 15:00:00时在线竞价系统对河南省郑州市某企业一批挖掘机物资进行公开竞价处置。",
    companyName: "河南郑州市某企业",
    timeValue: "2025/10/15",
    likeCount: 325,
    commentCount: 210,
    viewCount: 3800,
  },
]);
/* 模拟数据结束 */

/**
 * 获取拍卖会公告数据
 * 调用拍卖会公告接口，获取前4条数据用于第一个公告区域显示
 */
const fetchAuctionAnnouncements = async () => {
  try {
    const response = await auctionSessionApi.getAuctionSessionAnnouncementList({
      page: 1
    });
    if (response.code === 1 && response.data && response.data.data && Array.isArray(response.data.data)) {
      // 取前4条数据并转换为ListItem格式
      auctionAnnouncements.value = response.data.data.slice(0, 4).map((item: any) => ({
        productId: item.id?.toString() || String(Math.random()),
        productName: item.pmh_name || '拍卖会公告',
        productImage: item.pmh_pic 
          ? `${import.meta.env.VITE_API_BASE_URL || "https://huigupaimai.oss-cn-beijing.aliyuncs.com"}/${item.pmh_pic}`
          : '',
        description: item.pmh_gonggao || item.pmh_name || '',
        companyName: item.parentname || '拍卖会公告',
        timeValue: item.start_time_name || item.addtime || '',
        likeCount: 0,
        commentCount: 0,
        viewCount: 0,
      }));
    }
  } catch (error) {
    console.error('获取拍卖会公告失败:', error);
    // 发生错误时使用模拟数据作为备用
    auctionAnnouncements.value = listItems.value.slice(0, 4);
  }
};



/**
 * 获取销售公告数据
 * 调用销售公告接口，获取前4条数据用于第二个公告区域显示
 */
const fetchSalesAnnouncements = async () => {
  try {
    const response = await otherApi.getTenderList({
      cateid: "30", // 销售公告分类ID
      limit: 4,
      page: 1,
    });
    if (response.code === 1 && response.data && response.data.data && Array.isArray(response.data.data)) {
      // 取前4条数据并转换为ListItem格式
      salesAnnouncements.value = response.data.data.map((item: any, index: number) => ({
        productId: item.id?.toString() || String(Math.random()),
        productName: item.title || '销售公告',
        productImage: 'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/sale_1754962281689.png', // 使用销售公告默认图片
        description: item.title || '',
        companyName: item.cate_name || '销售公告',
        timeValue: item.addtime || '',
        likeCount: 0,
        commentCount: 0,
        viewCount: 0,
      }));
    }
  } catch (error) {
    console.error('获取销售公告失败:', error);
    // 发生错误时使用模拟数据作为备用
    salesAnnouncements.value = listItems.value.slice(0, 4);
  }
};

/**
 * 初始化公告数据
 * 并行获取拍卖会公告和销售公告数据
 */
const initAnnouncements = async () => {
  announcementsLoading.value = true;
  try {
    // 并行获取两种公告数据
    await Promise.all([
      fetchAuctionAnnouncements(),
      fetchSalesAnnouncements()
    ]);
  } catch (error) {
    console.error('初始化公告数据失败:', error);
  } finally {
    announcementsLoading.value = false;
  }
};

// 组件挂载完成生命周期
onMounted(async () => {
  // 初始化拍卖数据
  await initAuctions();
  // 初始化公告数据
  await initAnnouncements();
});

// 处理卡片点击事件
const handleCardClick = (value: { productId: string; pmhId: string }) => {
  // console.log(`点击了商品ID: ${value.productId}, 拍卖会ID: ${value.pmhId}`);
  // 跳转到拍品详情页
  router.push({
    name: "auctionDetail",
    query: { id: value.productId, pmhId: value.pmhId, crumbsTitle: "推荐专区" },
  });
};

// 处理列表项点击事件
const handleListItemClick = (productId: string,type: string) => {
  // 跳转到公告详情页
  router.push({
    name: "announcementInfo-detail",
    query: { id: productId, type,crumbsTitle: '推荐专区' },
  });
};
</script>

<template>
  <div class="home">
    <div class="low-container">
      <div v-if="loading" class="loading">
        <div class="loading-text">加载中...</div>
      </div>
      <div v-else-if="auctionItems.length > 0" class="auction-cards">
        <AuctionCard
          v-for="item in auctionItems"
          :key="item.id"
          v-bind="item"
          @click="handleCardClick"
        />
      </div>
      <!-- 数据为空占位符 -->
      <div v-else class="placeholder">
        <div class="placeholder-text">暂无拍品信息</div>
      </div>
    </div>

    <!-- 第一个公告区域：拍卖会公告 -->
    <div class="high-container">
      <div v-if="announcementsLoading" class="loading">
        <div class="loading-text">加载公告中...</div>
      </div>
      <div v-else-if="auctionAnnouncements.length > 0" class="auction-list">
        <template v-for="(item, index) in auctionAnnouncements" :key="item.productId">
          <AuctionListItem v-bind="item" @click="handleListItemClick(item.productId,'auction')" />

          <div v-if="index < auctionAnnouncements.length - 1" class="item-divider"></div>
        </template>
      </div>
      <!-- 数据为空占位符 -->
      <div v-else class="placeholder">
        <div class="placeholder-text">暂无拍卖会公告</div>
      </div>
    </div>

    <div class="low-container">
      <div v-if="loading" class="loading">
        <div class="loading-text">加载中...</div>
      </div>
      <div v-else-if="accomplishItems.length > 0" class="auction-cards">
        <AuctionCard
          v-for="item in accomplishItems"
          :key="item.id"
          v-bind="item"
          @click="handleCardClick"
        />
      </div>
      <div v-else class="placeholder">
        <div class="placeholder-text">暂无拍品信息</div>
      </div>
    </div>
    <!-- 第二个公告区域：销售公告 -->
    <div class="high-container">
      <div v-if="announcementsLoading" class="loading">
        <div class="loading-text">加载公告中...</div>
      </div>
      <div v-else-if="salesAnnouncements.length > 0" class="auction-list">
        <template v-for="(item, index) in salesAnnouncements" :key="item.productId">
          <AuctionListItem v-bind="item" @click="handleListItemClick(item.productId,'sales')" />
          <div v-if="index < salesAnnouncements.length - 1" class="item-divider"></div>
        </template>
      </div>
      <!-- 数据为空占位符 -->
      <div v-else class="placeholder">
        <div class="placeholder-text">暂无销售公告</div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.home {
  // padding: 0 320px;
  margin: 0 auto;
  margin-bottom: 20px;
  max-width: 1280px;

  .auction-cards {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
  }

  .placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    border-radius: 10px;
    background-color: #f5f5f5;
    margin-top: 20px;
  }

  .placeholder-text {
    font-size: 16px;
    color: #999999;
  }

  .loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    border-radius: 10px;
    background-color: #f9f9f9;
  }

  .loading-text {
    font-size: 16px;
    color: #666666;
    animation: pulse 1.5s ease-in-out infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  .auction-list {
    display: flex;
    flex-direction: column;

    .item-divider {
      height: 1px;
      background-color: #dddddd;
      margin: 20px 0;
    }
  }

  .low-container,
  .high-container {
    width: 100%;
    margin-top: 20px;
    border-radius: 10px;
    background-color: #fff;
    padding: 20px;
  }

  .low-container {
    min-height: 392px;
  }

  .high-container {
    min-height: 392px;
  }
}
</style>
