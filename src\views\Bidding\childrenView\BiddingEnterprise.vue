<template>
  <div class="enterprise-cooperation">
    <!-- 我们的优势板块 -->
    <section class="advantages-section">
      <div class="container">
        <!-- 标题区域 -->
        <div
          class="section-header"
          :class="{ 'animate-fade-in-up': isHeaderVisible }"
        >
          <p class="section-title">我们的优势</p>
          <p class="section-subtitle">数字化管理处置闲废物资全链路方案服务商</p>
        </div>

        <!-- 优势卡片列表 -->
        <div class="advantages-grid">
          <AdvantageCard
            v-for="(advantage, index) in advantages"
            :key="index"
            :number="index + 1"
            :title="advantage.title"
            :subtitle="advantage.subtitle"
            :description="advantage.description"
            :img-url="advantage.imgUrl"
            class="advantage-item"
            :class="{ 'animate-slide-in': visibleCards[index] }"
            :data-index="index"
          />
        </div>

        <!-- 携手合作按钮 -->
        <div
          class="cooperation-button-wrapper"
          :class="{ 'animate-bounce-in': isButtonVisible }"
        >
          <button class="cooperation-button" @click="showCooperation">
            <SvgIcon
              iconName="enterpriseCooperation-cooperation"
              className="button-icon"
            />
            携手合作
          </button>
        </div>
      </div>
    </section>

    <!-- 我们的合作伙伴板块 -->
    <section class="partners-section">
      <div
        class="section-header"
        :class="{ 'animate-fade-in-up': isPartnersVisible }"
      >
        <p class="section-title">我们的合作伙伴</p>
        <p class="section-subtitle">遍地全国的合作伙伴是我们实力的展现</p>
      </div>

      <!-- 合作伙伴滚动展示 -->
      <PartnerScroll />
    </section>

    <!-- 携手合作模态框 -->
    <Modal
      v-model="customModalVisible"
      title="携手合作"
      title-icon="model-icon"
      width="64.6vw"
      :confirm-loading="confirmLoading"
      :show-footer="false"
      confirm-button-text="提交资料"
      @confirm="handleCustomConfirm"
      @cancel="handleCustomCancel"
      class="cooperation-modal"
    >
      <div class="cooperation-form">
        <!-- 表单标题 -->
        <div class="form-header">
          <p class="form-title">留下您的联系方式 专属顾问会尽快联系您</p>
        </div>

        <!-- 表单内容 -->
        <div class="form-content">
          <el-form
            ref="cooperationFormRef"
            :model="cooperationForm"
            :rules="formRules"
            label-position="right"
            label-width="140px"
            class="cooperation-form-el"
          >
            <!-- 第一、二行：联系人和手机号 -->
            <el-form-item label="联系人" prop="contactName" class="form-item">
              <el-input
                v-model="cooperationForm.contactName"
                placeholder="请填写您的姓名（必填项）"
                class="form-input"
              />
            </el-form-item>
            <el-form-item label="手机号" prop="phoneNumber" class="form-item">
              <el-input
                v-model="cooperationForm.phoneNumber"
                placeholder="请填写您的手机号（必填项）"
                class="form-input"
              />
            </el-form-item>

            <!-- 第三行：公司名称 -->
            <el-form-item
              label="公司名称"
              prop="companyName"
              class="form-item full-width"
            >
              <el-input
                v-model="cooperationForm.companyName"
                placeholder="请填写您的公司名称"
                class="form-input"
              />
            </el-form-item>

            <!-- 第四行：处置物资名称 -->
            <el-form-item
              label="处置物资名称"
              prop="assetName"
              class="form-item full-width"
            >
              <el-input
                v-model="cooperationForm.assetName"
                placeholder="请填写您处置物资名称，如：钢铁、铜铝、报废汽车"
                class="form-input"
              />
            </el-form-item>

            <!-- 第五行：单次处置物资价值 -->
            <el-form-item
              label="单次处置物资价值"
              prop="assetValue"
              class="form-item full-width"
            >
              <el-input
                v-model="cooperationForm.assetValue"
                placeholder="请填写您的单次处置物资价值估值"
                class="form-input"
              />
            </el-form-item>

            <!-- 第六行：处置周期 -->
            <el-form-item
              label="处置周期"
              prop="disposalCycle"
              class="form-item full-width"
            >
              <div class="disposal-cycle-options" style="width: 100%">
                <el-select
                  v-model="cooperationForm.disposalCycle"
                  placeholder="请选择您的处置周期"
                  class="form-select"
                  popper-class="custom-popper"
                >
                  <el-option label="一次性" value="一次性" />
                  <el-option label="长期试探" value="长期试探" />
                  <el-option label="其他" value="其他" />
                </el-select>
              </div>
            </el-form-item>
          </el-form>

          <!-- 提交按钮 -->
          <div class="form-submit">
            <el-button
              type="primary"
              size="large"
              :loading="confirmLoading"
              @click="handleCustomConfirm"
              plain
              class="submit-button"
            >
              提交资料
            </el-button>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from "vue";
import SvgIcon from "@/components/SvgIcon.vue";
import AdvantageCard from "./components/AdvantageCard.vue";
import PartnerScroll from "./components/PartnerScroll.vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";

// 优势数据接口
interface Advantage {
  title: string;
  subtitle: string;
  description: string;
  imgUrl: string;
}

// 合作表单数据接口
interface CooperationForm {
  contactName: string;
  phoneNumber: string;
  companyName: string;
  assetName: string;
  assetValue: string;
  disposalCycle: string;
}

// 优势数据
const advantages = ref<Advantage[]>([
  {
    title: "企业信息推广",
    subtitle: "全网覆盖 立体传播",
    description:
      "充分发挥网络平台优势，为企业提供全方位多层次的信息发布服务，让企业信息得到最大化传播，提升企业知名度和影响力。",
    imgUrl: "https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/img1_1754959891411.png",
  },
  {
    title: "资产招商服务",
    subtitle: "精准匹配 高效对接",
    description:
      "基于大数据分析和智能匹配算法，为企业提供精准的资产招商服务，实现供需双方的高效对接，降低交易成本，提高成交效率。",
    imgUrl: "https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/img2_1754959911419.png",
  },
  {
    title: "司法竞价/拍卖服务",
    subtitle: "合规透明 专业高效",
    description:
      "严格按照司法程序和相关法律法规，提供专业的竞价拍卖服务，确保交易过程公开透明，维护各方合法权益。",
    imgUrl: "https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/img3_1754959925802.png",
  },
  {
    title: "平台对接",
    subtitle: "多元整合 协同发展",
    description:
      "整合多方资源，建立完善的平台对接机制，实现信息共享、资源互补，为企业提供一站式综合服务解决方案。",
    imgUrl: "https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/img4_1754959939308.png",
  },
  {
    title: "灰谷智能（数字DM）服务",
    subtitle: "智能驱动 数字化运营",
    description:
      "运用人工智能和大数据技术，为企业提供智能化的数字营销解决方案，助力企业实现数字化转型升级。",
    imgUrl: "https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/img5_1754959955679.png",
  },
  {
    title: "定制化服务",
    subtitle: "量身定制 精准服务",
    description:
      "根据企业具体需求和行业特点，提供个性化的定制服务方案，确保服务内容与企业发展战略高度契合。",
    imgUrl: "https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/img6_1754959967912.png",
  },
]);

// 响应式数据
const customModalVisible = ref(false);
const confirmLoading = ref(false);

// 动画相关状态
const isHeaderVisible = ref(false);
const visibleCards = ref<boolean[]>([]);
const isPartnersVisible = ref(false);
const isButtonVisible = ref(false);

// 表单引用
const cooperationFormRef = ref<FormInstance>();

// 合作表单数据
const cooperationForm = reactive<CooperationForm>({
  contactName: "",
  phoneNumber: "",
  companyName: "",
  assetName: "",
  assetValue: "",
  disposalCycle: "",
});

// 表单验证规则
const formRules: FormRules<CooperationForm> = {
  contactName: [
    { required: true, message: "请填写联系人姓名", trigger: "blur" },
    {
      min: 2,
      max: 20,
      message: "联系人姓名长度在 2 到 20 个字符",
      trigger: "blur",
    },
  ],
  phoneNumber: [
    { required: true, message: "请填写手机号", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请填写正确的手机号格式",
      trigger: "blur",
    },
  ],
  companyName: [
    {
      min: 2,
      max: 50,
      message: "公司名称长度在 2 到 50 个字符",
      trigger: "blur",
    },
  ],
  assetName: [
    {
      min: 2,
      max: 100,
      message: "处置物资名称长度在 2 到 100 个字符",
      trigger: "blur",
    },
  ],
  assetValue: [
    {
      min: 1,
      max: 50,
      message: "处置物资价值长度在 1 到 50 个字符",
      trigger: "blur",
    },
  ],
  disposalCycle: [
    { required: false, message: "请选择处置周期", trigger: "change" },
  ],
};

// 携手合作按钮点击事件
const showCooperation = () => {
  customModalVisible.value = true;
};

// 处理自定义模态框确认
const handleCustomConfirm = async () => {
  if (!cooperationFormRef.value) return;

  try {
    // 验证表单
    await cooperationFormRef.value.validate();

    confirmLoading.value = true;

    // 模拟异步提交操作
    setTimeout(() => {
      ElMessage.success("提交成功！我们的专属顾问会尽快联系您");
      confirmLoading.value = false;
      customModalVisible.value = false;

      // 重置表单
      resetForm();
    }, 2000);
  } catch (error) {
    ElMessage.error("请检查表单信息是否填写正确");
  }
};

// 重置表单
const resetForm = () => {
  if (cooperationFormRef.value) {
    cooperationFormRef.value.resetFields();
  }

  // 重置表单数据
  Object.assign(cooperationForm, {
    contactName: "",
    phoneNumber: "",
    companyName: "",
    assetName: "",
    assetValue: "",
    disposalCycle: "",
  });
};

// 处理自定义模态框取消
const handleCustomCancel = () => {
  // 重置表单
  resetForm();
  ElMessage.info("已取消操作");
};

// 滚动动画相关函数
const observeElements = () => {
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const target = entry.target as HTMLElement;

          // 标题区域动画
          if (target.classList.contains("section-header")) {
            if (target.closest(".advantages-section")) {
              isHeaderVisible.value = true;
            } else if (target.closest(".partners-section")) {
              isPartnersVisible.value = true;
            }
          }

          // 优势卡片动画
          if (target.classList.contains("advantage-item")) {
            const index = parseInt(target.dataset.index || "0");
            setTimeout(() => {
              visibleCards.value[index] = true;
            }, index); // 错开动画时间
          }

          // 按钮动画
          if (target.classList.contains("cooperation-button-wrapper")) {
            setTimeout(() => {
              isButtonVisible.value = true;
            }, 600);
          }
        }
      });
    },
    {
      threshold: 0, // 元素一进入视口就触发动画
      rootMargin: "0px 0px 0px 0px", // 不收缩视口，元素顶部一出现就触发
    }
  );

  // 观察所有需要动画的元素
  nextTick(() => {
    const elementsToObserve = [
      ".section-header",
      ".advantage-item",
      ".cooperation-button-wrapper",
    ];

    elementsToObserve.forEach((selector) => {
      const elements = document.querySelectorAll(selector);
      elements.forEach((el) => observer.observe(el));
    });
  });
};

// 初始化动画
const initAnimations = () => {
  // 初始化卡片可见性数组
  visibleCards.value = new Array(advantages.value.length).fill(false);

  // 页面加载动画
  setTimeout(() => {
    isHeaderVisible.value = true;
  }, 300);
};

// 生命周期钩子
onMounted(() => {
  initAnimations();
  observeElements();
});
</script>

<style scoped lang="scss">
.enterprise-cooperation {
  min-height: 100vh;
  background-color: #fff;
  overflow-x: hidden; // 防止动画造成的水平滚动条
}

// 动画关键帧定义
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(20px);
  }
  50% {
    opacity: 1;
    transform: scale(1.05) translateY(-5px);
  }
  70% {
    transform: scale(0.95) translateY(2px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

// 动画类定义
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-slide-in {
  animation: fadeInUp 0.6s ease-out forwards;

  // 奇数卡片从左侧滑入
  &:nth-child(odd) {
    animation: slideInLeft 0.6s ease-out forwards;
  }

  // 偶数卡片从右侧滑入
  &:nth-child(even) {
    animation: slideInRight 0.6s ease-out forwards;
  }
}

.animate-bounce-in {
  animation: bounceIn 0.8s ease-out forwards;
}

// 初始状态（动画前）
.section-header,
.advantage-item,
.cooperation-button-wrapper {
  opacity: 0;
  transform: translateY(30px);
}

// 动画完成后的状态
.animate-fade-in-up,
.animate-slide-in,
.animate-bounce-in {
  opacity: 1;
  transform: translateY(0);
}

// 我们的优势板块
.advantages-section {
  padding: 70px 0;

  .container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 20px;
  }
}

// 我们的合作伙伴板块
.partners-section {
  background-color: #f2f2f2;
  padding: 80px 0 0 0;

  .section-header {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 20px;
    margin-bottom: 30px;
  }
}

// 通用标题样式
.section-header {
  text-align: center;
  margin-bottom: 30px;
}

.section-title {
  font-size: 50px;
  font-family: "PingFang Bold";
  color: #004c66;
  margin: 0 0 11px 0;
}

.section-subtitle {
  font-size: 22px;
  color: #666666;
  margin: 0;
  &::before {
    content: "";
    display: inline-block;
    width: 20vw;
    height: 1px;
    background: linear-gradient(
      90deg,
      rgba(221, 221, 221, 0) 0%,
      rgba(221, 221, 221, 1) 100%
    );
    margin-right: 21px;
    vertical-align: middle;
  }
  &::after {
    content: "";
    display: inline-block;
    width: 20vw;
    height: 1px;
    background: linear-gradient(
      90deg,
      rgba(221, 221, 221, 1) 0%,
      rgba(221, 221, 221, 0) 100%
    );
    margin-left: 21px;
    vertical-align: middle;
  }
}

// 优势卡片网格
.advantages-grid {
  display: grid;
  gap: 20px;
}

.advantage-item {
  width: 100%;
}

// 携手合作按钮
.cooperation-button-wrapper {
  display: flex;
  justify-content: center;
}

.cooperation-button {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 16px 32px;
  background: transparent;
  border: 1px solid #004c66;
  border-radius: 10px;
  color: #004c66;
  font-size: 20px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  margin-top: 40px;
  position: relative;
  overflow: hidden;
  font-family: 'PingFang Medium';

  // 添加按钮背景渐变效果
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(0, 76, 102, 0.1),
      transparent
    );
    transition: left 0.6s ease;
  }

  .button-icon {
    width: 24px;
    height: 24px;
    transition: transform 0.3s ease;
  }

  &:hover {
    background-color: #004c66;
    color: #ffffff;
    transform: scale(1.02);
    // box-shadow: 0 12px 35px rgba(0, 76, 102, 0.4);

    &::before {
      left: 100%;
    }

    .button-icon {
      transform: rotate(360deg) scale(1.1);
    }
  }

  &:active {
    transform: translateY(-1px) scale(0.98);
    transition: all 0.1s ease;
  }

  // 点击波纹效果
  &:focus {
    outline: none;
    animation: pulse 0.6s ease-out;
  }
}

// 响应式设计
@media (max-width: 1440px) {
  .advantages-section,
  .partners-section {
    padding: 76px 0;
  }

  .partners-section {
    padding: 60px 0 0 0;
  }

  .section-title {
    font-size: 42px;
  }

  .section-subtitle {
    font-size: 16px;
  }

  .advantages-grid {
    gap: 25px;
    margin-bottom: 60px;
  }
}

@media (max-width: 1200px) {
  .advantages-section,
  .partners-section {
    padding: 50px 0;
  }

  .partners-section {
    padding: 50px 0 0 0;
  }

  .section-header {
    margin-bottom: 40px;
  }

  .partners-section .section-header {
    margin-bottom: 40px;
  }

  .section-title {
    font-size: 36px;
  }

  .section-subtitle {
    font-size: 15px;
  }

  .advantages-grid {
    gap: 20px;
    margin-bottom: 50px;
  }

  .cooperation-button {
    padding: 14px 28px;
    font-size: 16px;

    .button-icon {
      width: 20px;
      height: 20px;
    }
  }
}

@media (max-width: 768px) {
  .advantages-section,
  .partners-section {
    padding: 40px 0;
  }

  .partners-section {
    padding: 40px 0 0 0;
  }

  .container,
  .partners-section .section-header {
    padding: 0 15px;
  }

  .section-title {
    font-size: 28px;
  }

  .section-subtitle {
    font-size: 14px;
  }

  .advantages-grid {
    gap: 15px;
    margin-bottom: 40px;
  }

  .cooperation-button {
    padding: 12px 24px;
    font-size: 15px;
    gap: 8px;

    .button-icon {
      width: 18px;
      height: 18px;
    }
  }
}

// 模态框动画样式
.cooperation-modal {
  :deep(.el-dialog) {
    animation: modalSlideIn 0.4s ease-out;
  }

  :deep(.el-overlay) {
    animation: fadeIn 0.3s ease-out;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 合作表单样式
.cooperation-form {
  padding: 28px;
  background-color: #fff;

  .form-header {
    text-align: center;
    margin-bottom: 15px;

    .form-title {
      font-size: 25px;
      font-family: "PingFang Bold";
      color: #333333;
      margin: 0;
      line-height: 1.4;
    }
  }

  .form-content {
    max-width: 929px;
    margin: 0 auto;

    .cooperation-form-el {
      .form-row {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;

        .form-item {
          flex: 1;
          margin-bottom: 0;

          &.full-width {
            width: 100%;
            flex: 1;
          }

          :deep(.el-form-item__label) {
            font-size: 16px;
            font-weight: 500;
            color: #333333;
            line-height: 1.5;
            padding-right: 12px;
          }

          :deep(.el-form-item__content) {
            .form-input {
              .el-input__wrapper {
                border-radius: 8px;
                border: 1px solid #dcdfe6;
                box-shadow: none;
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                overflow: hidden;
                height: 40px;

                // 添加输入框光效
                &::before {
                  content: "";
                  position: absolute;
                  top: 0;
                  left: -100%;
                  width: 100%;
                  height: 100%;
                  background: linear-gradient(
                    90deg,
                    transparent,
                    rgba(0, 76, 102, 0.05),
                    transparent
                  );
                  transition: left 0.6s ease;
                  z-index: 1;
                }

                &:hover {
                  border-color: #c0c4cc;
                  transform: translateY(-1px);
                  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

                  &::before {
                    left: 100%;
                  }
                }

                &.is-focus {
                  border-color: #004c66;
                  box-shadow: 0 0 0 3px rgba(0, 76, 102, 0.15),
                    0 4px 12px rgba(0, 76, 102, 0.1);
                  transform: translateY(-2px);
                }

                .el-input__inner {
                  font-size: 14px;
                  color: #333333;
                  height: 44px;
                  line-height: 44px;
                  position: relative;
                  z-index: 2;
                  transition: all 0.3s ease;

                  &::placeholder {
                    color: #c0c4cc;
                    transition: color 0.3s ease;
                  }

                  &:focus::placeholder {
                    color: #a8a8a8;
                  }
                }
              }
            }

            .form-select {
              width: 100%;

              :deep(.el-select) {
                width: 100%;
              }

              :deep(.el-select__wrapper) {
                width: 100%;
                border-radius: 8px;
                border: 1px solid #dcdfe6;
                box-shadow: none;
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                height: 44px;
                position: relative;
                overflow: hidden;

                // 添加选择框光效
                &::before {
                  content: "";
                  position: absolute;
                  top: 0;
                  left: -100%;
                  width: 100%;
                  height: 100%;
                  background: linear-gradient(
                    90deg,
                    transparent,
                    rgba(0, 76, 102, 0.05),
                    transparent
                  );
                  transition: left 0.6s ease;
                  z-index: 1;
                }

                &:hover {
                  border-color: #c0c4cc;
                  transform: translateY(-1px);
                  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

                  &::before {
                    left: 100%;
                  }
                }

                &.is-focus {
                  border-color: #004c66;
                  box-shadow: 0 0 0 3px rgba(0, 76, 102, 0.15),
                    0 4px 12px rgba(0, 76, 102, 0.1);
                  transform: translateY(-2px);
                }

                .el-select__selected-item {
                  font-size: 14px;
                  color: #333333;
                  line-height: 42px;
                  position: relative;
                  z-index: 2;
                }

                .el-select__placeholder {
                  color: #c0c4cc;
                  font-size: 14px;
                  position: relative;
                  z-index: 2;
                  transition: color 0.3s ease;
                }
              }
            }
          }

          :deep(.el-form-item__error) {
            font-size: 12px;
            color: #f56c6c;
            margin-top: 4px;
          }
        }
      }
    }

    .form-submit {
      text-align: center;
      margin-top: 80px;

      .submit-button {
        padding: 28px 55px;
        border: none;
        border-radius: 8px;
        font-size: 20px;
        color: #ffffff;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        background: transparent;
        border: 1px solid #004c66;
        border-radius: 10px;
        color: #004c66;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
          background: linear-gradient(135deg, #003a52 0%, #005a75 100%);
          // box-shadow: 0 12px 35px rgba(0, 76, 102, 0.4);
          color: #fff;

          &::before {
            left: 100%;
          }
        }

        &:active {
          transform: translateY(-1px) scale(0.98);
          transition: all 0.1s ease;
        }

        &:focus {
          outline: none;
          animation: pulse 0.6s ease-out;
        }

        &.is-loading {
          background: linear-gradient(135deg, #004c66 0%, #006b8a 100%);
          opacity: 0.8;
          cursor: not-allowed;
          transform: none;

          &:hover {
            transform: none;
            box-shadow: none;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .cooperation-form {
    padding: 30px;

    .form-header {
      .form-title {
        font-size: 24px;
      }
    }

    .form-content {
      .cooperation-form-el {
        .form-row {
          gap: 15px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .cooperation-form {
    padding: 20px;

    .form-header {
      margin-bottom: 30px;

      .form-title {
        font-size: 20px;
      }
    }

    .form-content {
      .cooperation-form-el {
        .form-row {
          flex-direction: column;
          gap: 0;
          margin-bottom: 15px;

          .form-item {
            margin-bottom: 15px;

            :deep(.el-form-item__label) {
              font-size: 14px;
            }
          }
        }
      }

      .form-submit {
        margin-top: 30px;

        .submit-button {
          width: 100%;
          max-width: 300px;
        }
      }
    }
  }
}
</style>
