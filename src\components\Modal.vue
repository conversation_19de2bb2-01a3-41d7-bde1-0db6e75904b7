<template>
  <!-- 基于Element Plus的对话框组件进行二次封装的模态框 -->
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    :width="width"
    :top="top"
    :modal="modal"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    :show-close="false"
    :before-close="handleClose"
    :destroy-on-close="destroyOnClose"
    :append-to-body="appendToBody"
    :lock-scroll="lockScroll"
    :custom-class="customClass"
    class="custom-modal"
  >
    <!-- 自定义头部 -->
    <template #header>
      <div class="modal-header">
        <div class="modal-title">
          <!-- 标题图标 -->
          <SvgIcon
            v-if="titleIcon"
            :iconName="titleIcon"
            className="title-icon"
          />
        </div>
        <span class="title-text">{{ title }}</span>
        <!-- 关闭按钮 -->
        <div class="modal-close" @click="handleClose">
          <SvgIcon iconName="model-close" className="close-icon" />
        </div>
      </div>
    </template>

    <!-- 模态框内容 -->
    <div class="modal-content">
      <slot></slot>
    </div>

    <!-- 自定义底部 -->
    <template #footer v-if="showFooter">
      <div class="modal-footer">
        <slot name="footer">
          <!-- 默认底部按钮 -->
          <el-button
            v-if="showCancelButton"
            @click="handleCancel"
            :size="buttonSize"
            class="cancel-btn"
          >
            {{ cancelButtonText }}
          </el-button>
          <el-button
            v-if="showConfirmButton"
            type="primary"
            @click="handleConfirm"
            :loading="confirmLoading"
            :size="buttonSize"
          >
            {{ confirmButtonText }}
          </el-button>
        </slot>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
// ElementPlus 组件由 unplugin-element-plus 自动引入
import SvgIcon from "./SvgIcon.vue";

// 定义组件属性接口
interface Props {
  // 控制模态框显示隐藏
  modelValue: boolean;
  // 模态框标题
  title?: string;
  // 标题图标
  titleIcon?: string;
  // 模态框宽度
  width?: string | number;
  // 距离顶部的距离
  top?: string;
  // 是否显示遮罩层
  modal?: boolean;
  // 是否可以通过点击遮罩层关闭
  closeOnClickModal?: boolean;
  // 是否可以通过按下ESC关闭
  closeOnPressEscape?: boolean;
  // 关闭时销毁子元素
  destroyOnClose?: boolean;
  // 是否插入至body元素上
  appendToBody?: boolean;
  // 是否锁定滚动
  lockScroll?: boolean;
  // 自定义类名
  customClass?: string;
  // 是否显示底部
  showFooter?: boolean;
  // 是否显示取消按钮
  showCancelButton?: boolean;
  // 是否显示确认按钮
  showConfirmButton?: boolean;
  // 取消按钮文本
  cancelButtonText?: string;
  // 确认按钮文本
  confirmButtonText?: string;
  // 确认按钮加载状态
  confirmLoading?: boolean;
  // 按钮尺寸
  buttonSize?: "large" | "default" | "small";
}

// 定义事件接口
interface Emits {
  // 更新modelValue
  (e: "update:modelValue", value: boolean): void;
  // 确认事件
  (e: "confirm"): void;
  // 取消事件
  (e: "cancel"): void;
  // 关闭事件
  (e: "close"): void;
  // 打开事件
  (e: "open"): void;
  // 打开动画结束事件
  (e: "opened"): void;
  // 关闭动画结束事件
  (e: "closed"): void;
}

// 定义属性默认值
const props = withDefaults(defineProps<Props>(), {
  title: "提示",
  width: "500px",
  top: "15vh",
  modal: true,
  closeOnClickModal: true,
  closeOnPressEscape: true,
  destroyOnClose: false,
  appendToBody: true,
  lockScroll: true,
  customClass: "",
  showFooter: true,
  showCancelButton: true,
  showConfirmButton: true,
  cancelButtonText: "取消",
  confirmButtonText: "确定",
  confirmLoading: false,
  buttonSize: "default",
});

// 定义事件
const emit = defineEmits<Emits>();

// 内部控制显示隐藏的响应式变量
const dialogVisible = ref(props.modelValue);

// 监听外部传入的modelValue变化
watch(
  () => props.modelValue,
  (newVal) => {
    dialogVisible.value = newVal;
    if (newVal) {
      emit("open");
    }
  },
  { immediate: true }
);

// 监听内部dialogVisible变化，同步到外部
watch(dialogVisible, (newVal) => {
  emit("update:modelValue", newVal);
  if (!newVal) {
    emit("closed");
  } else {
    emit("opened");
  }
});

// 处理关闭事件
const handleClose = () => {
  dialogVisible.value = false;
  emit("close");
};

// 处理确认事件
const handleConfirm = () => {
  emit("confirm");
};

// 处理取消事件
const handleCancel = () => {
  dialogVisible.value = false;
  emit("cancel");
};
</script>

<style lang="scss">
// 模态框样式 - 完全去除Element Plus对话框的默认内边距
.custom-modal {
  --el-dialog-padding-primary: 0;

  border-radius: 10px;
  overflow: hidden;
  padding: 0 !important; // 去除对话框容器的内边距
}
</style>

<style lang="scss" scoped>
// 模态框头部样式
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  padding: 0 24px;
  // 背景色上下渐变：从rgba(0,76,102,0.9)渐变到rgba(0,76,102,0.6)
  background: linear-gradient(
    to bottom,
    rgba(0, 76, 102, 0.9),
    rgba(0, 76, 102, 0.6)
  );
  border-bottom: 1px solid #f0f0f0;

  .modal-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: #fff; // 白色文字以适配深色背景

    .title-icon {
      margin-right: 8px;
      font-size: 18px;
      width: 20px;
      height: 21px;
      color: #fff; // 图标也设置为白色
    }
  }

  .title-text {
    font-size: 18px;
    color: #fff;
  }

  .modal-close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;

    .close-icon {
      font-size: 14px;
      color: #fff; // 关闭图标设置为白色

    &:hover {
      // background-color: rgba(255, 255, 255, 0.1); // 悬停效果适配深色背景
      color: #EF0004;
    }
    }
  }
}

// 模态框内容样式
.modal-content {
  background-color: #fff;
}

// 模态框底部样式
.modal-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 20px;
  background-color: #fff;
  .el-button {
    width: 112px;
    height: 37px;
    border-radius: 6px;
  }
  .cancel-btn {
    background-color: rgba(0, 76, 102, 0.2);
    color: #004c66;
    &:hover {
      background-color: rgba(0, 76, 102, 0.4);
    }
  }
}

:deep(.el-input__wrapper) {
  height: 40px;
}
:deep(.el-form-item__label){
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  font-family: 'PingFang Medium';
}

:deep(.el-select__wrapper) {
  height: 40px;
}
</style>
