<template>
  <div class="local-scrollbar-container" ref="containerRef">
    <!-- 滚动内容区域 -->
    <div 
      class="local-scrollbar-content" 
      ref="contentRef"
      @scroll="handleScroll"
    >
      <slot></slot>
    </div>
    
    <!-- 自定义滚动条 -->
    <div class="local-scrollbar" v-show="showScrollbar">
      <!-- 滚动条轨道 -->
      <div 
        class="scrollbar-track" 
        ref="trackRef"
        @click="handleTrackClick"
      >
        <!-- 滚动条滑块 -->
        <div 
          class="scrollbar-thumb" 
          ref="thumbRef"
          :style="thumbStyle"
          @mousedown="handleThumbMouseDown"
        >
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'

// 组件引用
const containerRef = ref<HTMLElement>() // 容器引用
const contentRef = ref<HTMLElement>() // 内容区域引用
const trackRef = ref<HTMLElement>() // 滚动条轨道引用
const thumbRef = ref<HTMLElement>() // 滚动条滑块引用

// 响应式数据
const scrollTop = ref(0) // 当前滚动位置
const scrollHeight = ref(0) // 内容总高度
const clientHeight = ref(0) // 可视区域高度
const isDragging = ref(false) // 是否正在拖拽
const dragStartY = ref(0) // 拖拽开始时的Y坐标
const dragStartScrollTop = ref(0) // 拖拽开始时的滚动位置

// MutationObserver 实例
let mutationObserver: MutationObserver | null = null

/**
 * 计算滚动百分比
 */
const scrollPercentage = computed(() => {
  if (scrollHeight.value <= clientHeight.value) return 0
  const percentage = Math.round((scrollTop.value / (scrollHeight.value - clientHeight.value)) * 100)
  return Math.min(100, Math.max(0, percentage))
})

/**
 * 计算是否显示滚动条
 */
const showScrollbar = computed(() => {
  return scrollHeight.value > clientHeight.value
})

/**
 * 计算滑块样式
 */
const thumbStyle = computed(() => {
  if (!showScrollbar.value) return { height: '0px', top: '0px' }
  
  // 计算滑块高度（最小20px，最大轨道高度的80%）
  const trackHeight = trackRef.value?.clientHeight || 0
  const thumbHeight = Math.max(20, Math.min(trackHeight * 0.8, (clientHeight.value / scrollHeight.value) * trackHeight))
  
  // 计算滑块位置
  const maxScrollTop = scrollHeight.value - clientHeight.value
  const maxThumbTop = trackHeight - thumbHeight
  const thumbTop = maxScrollTop > 0 ? (scrollTop.value / maxScrollTop) * maxThumbTop : 0
  
  return {
    height: `${thumbHeight}px`,
    top: `${thumbTop}px`,
    opacity: isDragging.value ? '0.8' : '0.6'
  }
})

/**
 * 更新滚动信息
 */
const updateScrollInfo = () => {
  if (!contentRef.value) return
  
  scrollTop.value = contentRef.value.scrollTop
  scrollHeight.value = contentRef.value.scrollHeight
  clientHeight.value = contentRef.value.clientHeight
}

/**
 * 刷新滚动条（供外部调用）
 */
const refresh = () => {
  nextTick(() => {
    updateScrollInfo()
  })
}

// 暴露方法给父组件
defineExpose({
  refresh,
  updateScrollInfo
})

/**
 * 处理滚动事件
 */
const handleScroll = () => {
  if (!isDragging.value) {
    updateScrollInfo()
  }
}

/**
 * 处理轨道点击事件
 */
const handleTrackClick = (event: MouseEvent) => {
  if (!trackRef.value || !thumbRef.value || !contentRef.value) return
  
  const trackRect = trackRef.value.getBoundingClientRect()
  const clickY = event.clientY - trackRect.top
  const thumbHeight = thumbRef.value.clientHeight
  
  // 计算目标滚动位置
  const trackHeight = trackRef.value.clientHeight
  const maxScrollTop = scrollHeight.value - clientHeight.value
  const targetScrollTop = ((clickY - thumbHeight / 2) / (trackHeight - thumbHeight)) * maxScrollTop
  
  // 平滑滚动到目标位置
  contentRef.value.scrollTo({
    top: Math.max(0, Math.min(maxScrollTop, targetScrollTop)),
    behavior: 'smooth'
  })
}

/**
 * 处理滑块鼠标按下事件
 */
const handleThumbMouseDown = (event: MouseEvent) => {
  event.preventDefault()
  event.stopPropagation()
  
  isDragging.value = true
  dragStartY.value = event.clientY
  dragStartScrollTop.value = scrollTop.value
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  document.body.style.userSelect = 'none'
}

/**
 * 处理鼠标移动事件（拖拽）
 */
const handleMouseMove = (event: MouseEvent) => {
  if (!isDragging.value || !trackRef.value || !contentRef.value) return
  
  const deltaY = event.clientY - dragStartY.value
  const trackHeight = trackRef.value.clientHeight
  const thumbHeight = thumbRef.value?.clientHeight || 0
  const maxScrollTop = scrollHeight.value - clientHeight.value
  const maxThumbTop = trackHeight - thumbHeight
  
  if (maxThumbTop > 0) {
    const scrollDelta = (deltaY / maxThumbTop) * maxScrollTop
    const newScrollTop = Math.max(0, Math.min(maxScrollTop, dragStartScrollTop.value + scrollDelta))

    // 直接设置滚动位置
    contentRef.value.scrollTop = newScrollTop
    
    // 立即更新滚动信息
    updateScrollInfo()
  }
}

/**
 * 处理鼠标释放事件
 */
const handleMouseUp = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
  document.body.style.userSelect = ''
}

/**
 * 处理窗口大小变化
 */
const handleResize = () => {
  updateScrollInfo()
}

/**
 * 组件挂载时的初始化
 */
onMounted(async () => {
  await nextTick()
  updateScrollInfo()
  
  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize, { passive: true })
  
  // 创建 MutationObserver 监听内容变化
  if (contentRef.value) {
    mutationObserver = new MutationObserver(() => {
      // 延迟更新，确保DOM已经渲染完成
      setTimeout(() => {
        updateScrollInfo()
      }, 10)
    })
    
    // 开始观察内容变化
    mutationObserver.observe(contentRef.value, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class']
    })
  }
})

/**
 * 组件卸载时的清理
 */
onUnmounted(() => {
  // 移除事件监听器
  window.removeEventListener('resize', handleResize)
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
  
  // 断开 MutationObserver
  if (mutationObserver) {
    mutationObserver.disconnect()
    mutationObserver = null
  }
})
</script>

<style lang="scss" scoped>
.local-scrollbar-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.local-scrollbar-content {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  
  // 隐藏原生滚动条
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.local-scrollbar {
  position: absolute;
  top: 0;
  right: -12px; /* 向右移动12px，避免遮挡文字 */
  width: 8px;
  height: 100%;
  z-index: 10;
  pointer-events: auto;
  
  .scrollbar-track {
    position: relative;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0);
    cursor: pointer;
    transition: background 0.2s ease;
    
    &:hover {
      .scrollbar-thumb {
        opacity: 0.8 !important;
        width: 6px;
      }
    }
  }
  
  .scrollbar-thumb {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    background: #ccc;
    border-radius: 4px;
    cursor: grab;
    transition: all 0.2s ease;
    opacity: 0.6;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    
    &:hover {
      opacity: 0.8;
    }
    
    &:active {
      cursor: grabbing;
      opacity: 1;
      transform: translateX(-50%) scale(1.1);
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .local-scrollbar {
    width: 6px;
    
    .scrollbar-thumb {
      width: 3px;
    }
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .local-scrollbar {
    .scrollbar-track {
      background: rgba(0, 0, 0, 0.2);
    }
    
    .scrollbar-thumb {
      background: #000;
      opacity: 0.8;
    }
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .local-scrollbar {
    .scrollbar-track,
    .scrollbar-thumb {
      transition: none;
    }
  }
}
</style>