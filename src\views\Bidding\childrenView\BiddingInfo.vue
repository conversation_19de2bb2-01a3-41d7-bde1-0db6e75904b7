<template>
  <div class="info">
    <!-- 筛选组件 -->
    <Filter
      @filter-change="handleFilterChange"
      :total="auctionTotal"
      :page="currentFilters.page"
    />

    <!-- 添加分割线 -->
    <div class="divider"></div>

    <!-- 数据列表区域 -->
    <div class="data-list">
      <!-- 这里可以添加数据列表组件 -->
      <div v-if="auctionItems.length !== 0">
        <div class="auction-cards">
          <AuctionCard
            v-for="item in auctionItems"
            :key="item.id"
            v-bind="item"
            @click="handleCardClick"
          />
        </div>
        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            :current-page.sync="currentFilters.page"
            :total="auctionTotal"
            :page-size="16"
            layout="total, prev, pager, next, jumper"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
      <!-- 数据为空 -->
      <div class="empty" v-else>
        <div class="empty-icon">
          <SvgIcon iconName="search" className="empty-search-icon" />
        </div>
        <div class="empty-text">没有找到相关拍品</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import Filter from "./components/Filter.vue";
import AuctionCard from "@/components/AuctionCard.vue";
import SvgIcon from "@/components/SvgIcon.vue";
import type { AuctionItem } from "@/types/auction";
import { auctionApi } from "@/utils/api";

const router = useRouter();
const route = useRoute();

// 当前筛选条件
const currentFilters = ref({
  province: "",
  city: "",
  keyword: "",
  page: 1,
  status: '1,5,6',
  type: 0,
});

// 模拟拍卖商品数据
const auctionItems = ref<AuctionItem[]>([]);
const auctionTotal = ref(0);

// 获取拍品列表
const initAuctions = async () => {
  try {
    const response = await auctionApi.getAuctionList(currentFilters.value);
    if (response.code == 1) {
      // 创建Promise数组，用于并行获取详细数据
      const promises = response.data.data.map(async (item: any) => {
        const obj: AuctionItem = {
          id: item.id,
          pmhId: item.pmh_id,
          bdName: item.bd_title,
          startTime: item.start_time_name,
          endTime: item.end_time_name,
          bdPic:
            "https://huigupaimai.oss-cn-beijing.aliyuncs.com/" + item.bd_url,
          bdQipaijia: item.bd_qipaijia,
          qpjDanwie: item.qpj_danwie,
          bdWeiguan: item.bd_weiguan,
          timeLabel: "截止报名",
          scheduleLabel: "预计开始",
          status: item.bd_status
        };
        return obj;
      });

      // 等待所有异步操作完成，获取实际的数据数组
      const list = await Promise.all(promises);

      // 更新auctionItems响应式数据
      auctionItems.value = list;
      auctionTotal.value = response.data.total;
    }
  } catch (error) {
    console.error("获取数据失败:", error);
  }
};

// 监听路由查询参数变化，处理搜索关键词
watch(
  () => route.query.keyword,
  (newKeyword) => {
    if (newKeyword && typeof newKeyword === 'string') {
      // 更新搜索关键词到筛选条件
      currentFilters.value.keyword = newKeyword;
      // 重置页码到第一页
      currentFilters.value.page = 1;
      // 触发搜索
      initAuctions();
    }
  },
  { immediate: true }
);

// 组件挂载完成
onMounted(() => {
  // 检查是否有搜索关键词
  const keyword = route.query.keyword;
  if (keyword && typeof keyword === 'string') {
    currentFilters.value.keyword = keyword;
  }
  
  // 初始化数据
  initAuctions();
});

// 处理卡片点击事件
const handleCardClick = (value: { productId: string; pmhId: string }) => {
  // 跳转到拍品详情页
  router.push({
    name: "auctionDetail",
    query: { id: value.productId, pmhId: value.pmhId, crumbsTitle: '标的信息' },
  });
};

// 处理筛选条件变化
const handleFilterChange = (filters: any) => {
  currentFilters.value = filters;
  // console.log("筛选条件变化: ", filters);
  initAuctions();

  // 这里可以调用API获取筛选后的数据
  // fetchFilteredData(filters)
};

// 获取筛选后的数据
const fetchFilteredData = async (filters: any) => {
  try {
    // 调用API获取数据
    // console.log("获取筛选数据:", filters);
  } catch (error) {
    console.error("获取数据失败:", error);
  }
};

// 分页相关
const handleCurrentChange = (val: number) => {
  currentFilters.value.page = val;
  initAuctions();
};
</script>

<style lang="scss" scoped>
.info {
  margin: 0 auto;
  max-width: 1280px;
  padding: 8px 20px 20px 20px;
  background: #fff;
  margin-top: 20px;
  margin-bottom: 20px;
  border-radius: 10px;
  .divider {
    margin: 20px 0;
    height: 1px;
    background: #ddd;
  }
}

.data-list {
  margin-top: 20px;
  min-height: 500px;
  .auction-cards {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    margin-bottom: 20px;
  }
  .pagination {
    display: flex;
    justify-content: right;
  }
}

.placeholder {
  background: #f5f5f5;
  padding: 40px;
  text-align: center;
  border-radius: 8px;
  color: #666;

  p {
    margin: 10px 0;
  }
}

// 数据为空
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #999;

  .empty-icon {
    font-size: 64px;
    margin-bottom: 16px;
    color: #ddd;
    
    // SvgIcon 组件样式
    .empty-search-icon {
      width: 64px;
      height: 64px;
      color: #ddd;
    }
  }

  .empty-text {
    font-size: 16px;
  }
}
</style>
