<template>
  <div class="my-favorites">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>我的收藏</h2>
      <p>共 {{ total }} 条收藏记录</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-text">加载中...</div>
    </div>

    <!-- 空状态 -->
    <div
      v-else-if="!loading && auctionList.length === 0"
      class="empty-container"
    >
      <div class="empty-text">暂无收藏记录</div>
    </div>

    <!-- 拍卖卡片列表 -->
    <div v-else class="auction-grid">
      <BiddingCard
        v-for="item in auctionList"
        :key="item.id"
        v-bind="item"
        button-type="cancel"
        @card-click="handleCardClick"
        @button-click="handleCancelFavorite"
      />
    </div>

    <!-- 分页组件 -->
    <div v-if="total > 0" class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        layout="prev, pager, next, jumper"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import BiddingCard from "@/components/BiddingCard.vue";
import { userApi,auctionApi } from "@/utils/api";
import { useUserStore } from "@/stores/user";

const router = useRouter();

// 用户状态管理
const userStore = useUserStore();

// 响应式数据
const loading = ref(false);
const auctionList = ref<any[]>([]);
const currentPage = ref(1);
const pageSize = ref(12);
const total = ref(0);

/**
 * 获取收藏列表数据
 */
const fetchFavorites = async () => {
  try {
    loading.value = true;

    // 从缓存中获取用户信息
    const userInfo = userStore.userInfo;
    if (!userInfo?.id) {
      ElMessage.error("请先登录");
      return;
    }

    const params = {
      member_id: userInfo.id,
      page: currentPage.value,
      type: 1,
    };

    const response = await userApi.getMyFavoriteAuctions(params);

    if (response.code === 1) {
      // 创建Promise数组，用于并行获取详细数据
      const promises = response.data.data.map(async (item: any) => {
        const obj: any = {
          id: item.id,
          pmhId: item.pmh_id || 0,
          bdName: item.bd_title,
          startTime: item.start_time_name.split("年")[1],
          endTime: item.start_time_name.split("年")[1],
          bdPic:
            "https://huigupaimai.oss-cn-beijing.aliyuncs.com/" + item.bd_url,
          bdQipaijia: item.bd_qipaijia,
          qpjDanwie: item.qpj_danwie,
          bdWeiguan: item.bd_weiguan,
          timeLabel: "截止报名",
          scheduleLabel: "预计开始",
          status: item.bd_status,
        };
        return obj;
      });

      // 等待所有异步操作完成，获取实际的数据数组
      const list = await Promise.all(promises);

      // 更新auctionItems响应式数据
      auctionList.value = list;
      total.value = response.data.total || 0;
    } else {
      ElMessage.error(response.msg || "获取收藏列表失败");
    }
  } catch (error) {
    console.error("获取收藏列表失败:", error);
    ElMessage.error("获取收藏列表失败");
  } finally {
    loading.value = false;
  }
};

/**
 * 处理页码变化
 */
const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchFavorites();
};

/**
 * 处理卡片点击事件
 */
const handleCardClick = (data: { productId: string, pmhId: string }) => {
  // 跳转到拍卖详情页
  router.push({
    name: "auctionDetail",
    query: { id: data.productId, pmhId: data.pmhId },
  });
};

/**
 * 处理取消收藏
 */
const handleCancelFavorite = async (data: {
  productId: string;
  type: string;
}) => {
  try {
    const result = await ElMessageBox.confirm(
      "确定要取消收藏这个标的吗？",
      "取消收藏",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    if (result === "confirm") {
      // TODO: 调用取消收藏的API
      const params = {
        member_id: userStore.userInfo?.id || 0,
        bd_id: Number(data.productId),
        isquxiao: 0
      };
      const response = await auctionApi.favoriteTarget(params);
      if (response.code === 1) {
        ElMessage.success("取消收藏成功");
        // 重新获取列表数据
        fetchFavorites();
      } else {
        ElMessage.error(response.msg || "取消收藏失败");
      }
    }
  } catch (error) {
    // 用户取消操作
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchFavorites();
});
</script>

<style scoped lang="scss">
.my-favorites {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;

    h2 {
      font-size: 24px;
      color: #333;
      margin: 0 0 8px 0;
    }

    p {
      font-size: 14px;
      color: #666;
      margin: 0;
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;

    .loading-text {
      font-size: 16px;
      color: #666;
    }
  }

  .empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;

    .empty-text {
      font-size: 16px;
      color: #999;
    }
  }

  .auction-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(295px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 30px;
  }
}
</style>
