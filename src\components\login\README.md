# 登录相关组件使用说明

本目录包含登录相关的弹窗组件，包括退出登录确认和注销账号确认功能。

## 组件列表

### 1. LogoutConfirmModal - 退出登录确认弹窗

用于确认用户是否要退出登录的弹窗组件。

#### 使用方法

```vue
<template>
  <div>
    <button @click="showLogout = true">退出登录</button>
    
    <LogoutConfirmModal
      v-model="showLogout"
      :loading="logoutLoading"
      @confirm="handleLogout"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { LogoutConfirmModal } from '@/components/login'

const showLogout = ref(false)
const logoutLoading = ref(false)

const handleLogout = async () => {
  logoutLoading.value = true
  try {
    // 执行退出登录逻辑
    await logoutApi()
    // 清除本地存储
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    // 跳转到登录页
    router.push('/login')
  } catch (error) {
    console.error('退出登录失败:', error)
  } finally {
    logoutLoading.value = false
    showLogout.value = false
  }
}

const handleCancel = () => {
  showLogout.value = false
}
</script>
```

#### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | boolean | false | 控制弹窗显示/隐藏 |
| loading | boolean | false | 确认按钮加载状态 |

#### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| update:modelValue | 更新modelValue | (value: boolean) |
| confirm | 确认退出登录 | - |
| cancel | 取消退出登录 | - |

### 2. DeleteAccountModal - 注销账号确认弹窗

用于显示注销账号详细说明和确认操作的弹窗组件。

#### 使用方法

```vue
<template>
  <div>
    <button @click="showDelete = true">注销账号</button>
    
    <DeleteAccountModal
      v-model="showDelete"
      :loading="deleteLoading"
      @confirm="handleDeleteAccount"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DeleteAccountModal } from '@/components/login'

const showDelete = ref(false)
const deleteLoading = ref(false)

const handleDeleteAccount = async () => {
  deleteLoading.value = true
  try {
    // 执行注销账号逻辑
    await deleteAccountApi()
    // 清除本地存储
    localStorage.clear()
    // 跳转到登录页
    router.push('/login')
  } catch (error) {
    console.error('注销账号失败:', error)
  } finally {
    deleteLoading.value = false
    showDelete.value = false
  }
}

const handleCancel = () => {
  showDelete.value = false
}
</script>
```

#### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | boolean | false | 控制弹窗显示/隐藏 |
| loading | boolean | false | 确认按钮加载状态 |

#### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| update:modelValue | 更新modelValue | (value: boolean) |
| confirm | 确认注销账号 | - |
| cancel | 取消注销账号 | - |

## 设计特点

### LogoutConfirmModal
- 简洁的确认界面
- 警告图标提示
- 清晰的操作说明
- 加载状态反馈

### DeleteAccountModal
- 详细的注销说明
- 分段式信息展示
- 法律条款说明
- 客服联系方式
- 友好的感谢语

## 样式特点

- 响应式设计，适配不同屏幕尺寸
- 统一的色彩搭配和字体规范
- 清晰的信息层次结构
- 符合用户体验的交互设计

## 注意事项

1. 使用前请确保已正确导入组件
2. 确认和取消的逻辑需要在父组件中实现
3. 加载状态需要在异步操作时正确设置
4. 建议在实际项目中添加错误处理和用户提示