<template>
  <div class="personal-info">
    <!-- 用户信息顶部 -->
    <div class="user-header">
      <div class="user-avatar">
        <div class="avatar-bg">
          <SvgIcon iconName="user" className="avatar-icon" />
        </div>
        <span class="phone-display">已实名认证 131 ****6789</span>
      </div>
    </div>

    <!-- 用户资料列表 -->
    <div class="info-list">
      <!-- 昵称 -->
      <div class="info-item">
        <div class="info-content">
          <div class="label">昵称</div>
          <div class="value">{{ userInfo.nickname }}</div>
        </div>
        <button class="edit-btn" @click="handleEdit('nickname')">编辑</button>
      </div>

      <!-- 用户名 -->
      <div class="info-item">
        <div class="info-content">
          <div class="label">用户名</div>
          <div class="value">{{ userInfo.username }}</div>
        </div>
        <button class="edit-btn" @click="handleEdit('username')">编辑</button>
      </div>

      <!-- 用户ID -->
      <div class="info-item">
        <div class="info-content">
          <div class="label">用户ID</div>
          <div class="value">{{ userInfo.userId }}</div>
        </div>
      </div>

      <!-- 联系电话 -->
      <div class="info-item">
        <div class="info-content">
          <div class="label">联系电话</div>
          <div class="value">{{ userInfo.phone }}</div>
        </div>
        <button class="edit-btn" @click="handleEdit('phone')">编辑</button>
      </div>

      <!-- 用户身份 -->
      <div class="info-item">
        <div class="info-content">
          <div class="label">用户身份</div>
          <div class="value">{{ userInfo.role }}</div>
        </div>
        <button class="edit-btn" @click="handleEdit('role')">切换</button>
      </div>

      <!-- 地址 -->
      <div class="info-item">
        <div class="info-content">
          <div class="label">地址</div>
          <div class="value">{{ userInfo.address }}</div>
        </div>
        <button class="edit-btn" @click="handleEdit('address')">编辑</button>
      </div>

      <!-- 个人介绍 -->
      <div class="info-item">
        <div class="info-content">
          <div class="label">个人介绍</div>
          <div class="value">{{ userInfo.introduction }}</div>
        </div>
        <button class="edit-btn" @click="handleEdit('introduction')">编辑</button>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-actions">
      <button class="logout-btn" @click="handleLogout">退出登录</button>
      <button class="delete-account-btn" @click="handleDeleteAccount">注销账号</button>
    </div>

    <!-- 编辑弹窗 -->
    <Modal
      v-model="showEditModal"
      :title="currentEditType === 'role' ? '切换用户身份' : `编辑${fieldLabels[currentEditType]}`"
      width="500px"
      :confirm-loading="confirmLoading"
      @confirm="handleModalConfirm"
      @cancel="handleModalCancel"
    >
      <!-- 用户身份切换确认 -->
      <div v-if="currentEditType === 'role'" class="role-switch-content">
        <p class="switch-message">
          您当前的身份是：<strong>{{ userInfo.role }}</strong>
        </p>
        <p class="switch-question">
          是否要切换为：<strong>{{ userInfo.role === '竞买人' ? '处置企业' : '竞买人' }}</strong>？
        </p>
        <p class="switch-note">
          注意：切换身份后，您的权限和功能将发生变化。
        </p>
      </div>
      
      <!-- 其他字段编辑 -->
      <div v-else class="edit-form">
        <div class="form-item">
          <el-input
            v-model="editValue"
            :placeholder="`请输入${fieldLabels[currentEditType]}`"
            :type="currentEditType === 'introduction' ? 'textarea' : 'text'"
            :rows="currentEditType === 'introduction' ? 4 : undefined"
            show-word-limit
            clearable
          />
        </div>
      </div>
    </Modal>

    <!-- 退出登录确认弹窗 -->
    <LogoutConfirmModal
      v-model="showLogoutModal"
      :loading="logoutLoading"
      @confirm="handleConfirmLogout"
      @cancel="handleCancelLogout"
    />

    <!-- 注销账号确认弹窗 -->
    <DeleteAccountModal
      v-model="showDeleteAccountModal"
      :loading="deleteAccountLoading"
      @confirm="handleConfirmDeleteAccount"
      @cancel="handleCancelDeleteAccount"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import Modal from "@/components/Modal.vue";
import SvgIcon from "@/components/SvgIcon.vue";
import LogoutConfirmModal from "@/components/login/LogoutConfirmModal.vue";
import DeleteAccountModal from "@/components/login/DeleteAccountModal.vue";
// ElInput 由 unplugin-element-plus 自动引入

// 定义用户信息接口
interface UserInfo {
  nickname: string;
  username: string;
  userId: string;
  phone: string;
  role: string;
  address: string;
  introduction: string;
}

// 用户信息数据
const userInfo = ref<UserInfo>({
  nickname: "张三",
  username: "张三",
  userId: "hg000000001",
  phone: "131 2345 6789",
  role: "竞买人",
  address: "河南省林州市临淇镇政府街林钢公司家属院20号1楼110室",
  introduction: "暂未填写个人介绍"
});

// 弹窗相关状态
const showEditModal = ref(false); // 控制编辑弹窗显示
const showLogoutModal = ref(false); // 控制退出登录弹窗显示
const currentEditType = ref(""); // 当前编辑的字段类型
const editValue = ref(""); // 编辑框的值
const confirmLoading = ref(false); // 确认按钮加载状态
const logoutLoading = ref(false); // 退出登录加载状态
const showDeleteAccountModal = ref(false); // 控制注销账号弹窗显示
const deleteAccountLoading = ref(false); // 注销账号加载状态

// 字段标签映射
const fieldLabels: Record<string, string> = {
  nickname: "昵称",
  username: "用户名",
  phone: "联系电话",
  role: "用户身份",
  address: "地址",
  introduction: "个人介绍"
};

// 处理编辑操作
const handleEdit = (type: string) => {
  currentEditType.value = type;
  
  // 如果是用户身份切换，不需要设置编辑值
  if (type !== 'role') {
    // 根据编辑类型设置当前值
    editValue.value = userInfo.value[type as keyof UserInfo];
  }
  
  showEditModal.value = true;
};

// 处理弹窗确认
const handleModalConfirm = async () => {
  confirmLoading.value = true;
  
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 如果是用户身份切换
    if (currentEditType.value === 'role') {
      // 切换用户身份逻辑
      const newRole = userInfo.value.role === '竞买人' ? '处置企业' : '竞买人';
      userInfo.value.role = newRole;
      // console.log(`用户身份已切换为: ${newRole}`);
    } else {
      // 更新其他用户信息
      if (currentEditType.value && editValue.value.trim()) {
        userInfo.value[currentEditType.value as keyof UserInfo] = editValue.value.trim();
      }
      // console.log(`更新${fieldLabels[currentEditType.value]}: `, editValue.value);
    }
    
    // 关闭弹窗
    showEditModal.value = false;
  } catch (error) {
    console.error("更新失败:", error);
  } finally {
    confirmLoading.value = false;
  }
};

// 处理弹窗取消
const handleModalCancel = () => {
  showEditModal.value = false;
  editValue.value = "";
  currentEditType.value = "";
};

// 处理退出登录
const handleLogout = () => {
  showLogoutModal.value = true;
};

// 确认退出登录
const handleConfirmLogout = async () => {
  logoutLoading.value = true;
  
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 清除用户数据和token
    localStorage.removeItem('token');
    localStorage.removeItem('userInfo');
    
    // 跳转到登录页面
    // console.log('用户已退出登录'); 已移除
    // 这里可以使用路由跳转到登录页
    // router.push('/login');
    
    showLogoutModal.value = false;
  } catch (error) {
    console.error('退出登录失败:', error);
  } finally {
    logoutLoading.value = false;
  }
};

// 取消退出登录
const handleCancelLogout = () => {
  showLogoutModal.value = false;
};

// 处理注销账号
const handleDeleteAccount = () => {
  showDeleteAccountModal.value = true;
};

// 确认注销账号
const handleConfirmDeleteAccount = async () => {
  deleteAccountLoading.value = true;
  
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 执行注销账号逻辑
    // console.log('用户已注销账号'); 已移除
    
    // 关闭弹窗
    showDeleteAccountModal.value = false;
  } catch (error) {
    console.error('注销账号失败:', error);
  } finally {
    deleteAccountLoading.value = false;
  }
};

// 取消注销账号
const handleCancelDeleteAccount = () => {
  showDeleteAccountModal.value = false;
};
</script>

<style lang="scss" scoped>
.personal-info{
  margin: 30px 20px 20px 20px;
}

// 用户信息顶部样式
.user-header {
  margin-bottom: 10px;

  .user-avatar {
    display: flex;
    align-items: center;
    gap: 9px;
    .avatar-bg {
      background-color: #004c66;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      .avatar-icon {
        width: 24px;
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .phone-display {
      background-color: #E6EEF0;
      border: 1px solid #004C66;
      padding: 5px 10px;
      border-radius: 6px;
      font-size: 12px;
      color: #004C66;
    }
  }
}

// 信息列表样式
.info-list {
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid;
    // 渐变
    border-image: linear-gradient(to right, rgba(221, 221, 221, 1), rgba(255, 255, 255, 1));
    border-image-slice: 1;

    &.no-border {
      border-bottom: none;
    }

    .info-content {
      flex: 1;

      .label {
        font-size: 18px;
        color: #333;
        margin-bottom: 10px;
        font-family: 'PingFang Bold';
      }

      .value {
        font-size: 14px;
        color: #999;
        line-height: 1.5;
      }
    }

    .edit-btn {
      padding: 8px 32px;
      background: #fff;
      border: 1px solid #ddd;
      border-radius: 6px;
      color: #333;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #004c66;
        color: #004c66;
        background-color: rgba(0, 76, 102, 0.2);
      }
    }
  }
}

// 底部按钮样式
.bottom-actions {
  margin-top: 40px;
  display: flex;
  gap: 16px;

  button {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s;
  }

  .logout-btn {
    background: #fff;
    border: 1px solid #ddd;
    color: #333;

    &:hover {
      border-color: #004c66;
      color: #004c66;
      background-color: rgba($color: #004c66, $alpha: 0.2);
    }
  }

  .delete-account-btn {
    background: #FEE6E6;
    border: 1px solid #EF0004;
    color: #EF0004;

    &:hover {
      background: rgba($color: #FEE6E6, $alpha: 0.2);
      border-color: #EF0004;
    }
  }
}

// 编辑弹窗样式
.edit-form {
  padding: 20px;
  padding-bottom: 0;

  .form-item {
    margin-bottom: 20px;

    .form-label {
      display: block;
      font-size: 14px;
      color: #333;
      margin-bottom: 8px;
      font-weight: 500;
    }

    :deep(.el-input) {
      .el-input__wrapper {
        border-radius: 6px;
        height: 46px;
        border: 1px solid #ddd;
        transition: all 0.3s;

        &:hover {
          border-color: #004c66;
        }

        &.is-focus {
          border-color: #004c66;
          box-shadow: 0 0 0 2px rgba(0, 76, 102, 0.1);
        }
      }

      .el-input__inner {
        font-size: 14px;
        color: #333;
        padding: 13px 15px;

        &::placeholder {
          color: #a8abb2;
        }
      }
    }

    :deep(.el-textarea) {
      .el-textarea__inner {
        border-radius: 6px;
        border: 1px solid #dcdfe6;
        font-size: 14px;
        color: #333;
        padding: 12px 15px;
        resize: vertical;
        min-height: 100px;
        transition: all 0.3s;

        &:hover {
          border-color: #004c66;
        }

        &:focus {
          border-color: #004c66;
          box-shadow: 0 0 0 2px rgba(0, 76, 102, 0.1);
          outline: none;
        }

        &::placeholder {
          color: #a8abb2;
        }
      }
    }

    // 字数统计样式
    :deep(.el-input__count) {
      color: #909399;
      font-size: 12px;
    }
  }
}

// 用户身份切换弹窗样式
.role-switch-content {
  padding: 20px 0;
  text-align: center;
  
  .switch-message {
    font-size: 16px;
    color: #333;
    margin-bottom: 16px;
    line-height: 1.5;
    
    strong {
      color: #004c66;
      font-weight: 600;
    }
  }
  
  .switch-question {
    font-size: 16px;
    color: #333;
    margin-bottom: 20px;
    line-height: 1.5;
    
    strong {
      color: #e74c3c;
      font-weight: 600;
    }
  }
  
  .switch-note {
    font-size: 14px;
    color: #666;
    background-color: #f8f9fa;
    padding: 12px 16px;
    border-radius: 6px;
    border-left: 4px solid #ffc107;
    margin: 0;
    line-height: 1.5;
    text-align: left;
  }
}



// 弹窗内容区域样式优化
:deep(.custom-modal) {
  .modal-content {
    padding: 0;
  }

  .modal-footer {
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
    
    .el-button {
      padding: 10px 24px;
      border-radius: 6px;
      font-size: 14px;
      
      &.cancel-btn {
        border-color: #dcdfe6;
        color: #606266;
        
        &:hover {
          border-color: #004c66;
          color: #004c66;
        }
      }
      
      &.el-button--primary {
        background-color: #004c66;
        border-color: #004c66;
        
        &:hover {
          background-color: #003d52;
          border-color: #003d52;
        }
      }
    }
  }
}
</style>
