<template>
  <!-- 自由交易密码登录表单 -->
  <el-form
    ref="passwordFormRef"
    :model="passwordForm"
    :rules="passwordRules"
    :validate-on-rule-change="false"
    class="login-form"
  >
    <!-- 用户名输入框 -->
    <el-form-item prop="username">
      <el-input
        v-model="passwordForm.username"
        placeholder="请输入用户名"
        size="large"
        class="login-input"
      />
    </el-form-item>

    <!-- 密码输入框 -->
    <el-form-item prop="password" class="password">
      <el-input
        v-model="passwordForm.password"
        type="password"
        placeholder="请输入登录密码"
        size="large"
        class="login-input"
        show-password
      />
    </el-form-item>

    <!-- 图形验证码输入框 -->
    <el-form-item prop="captcha" class="captcha">
      <div class="captcha-container">
        <el-input
          v-model="passwordForm.captcha"
          placeholder="请输入验证码"
          size="large"
          class="captcha-input"
          maxlength="4"
        />
        <div class="captcha-image-wrapper" @click="refreshCaptcha">
          <img 
            :src="captchaUrl" 
            alt="验证码" 
            class="captcha-image"
            @error="handleCaptchaError"
          />
          <div class="captcha-refresh-tip">点击刷新</div>
        </div>
      </div>
    </el-form-item>

    <!-- 记住密码 -->
    <el-form-item class="form-options">
      <el-checkbox v-model="passwordForm.rememberMe" class="remember-checkbox">
        记住密码
      </el-checkbox>
    </el-form-item>

    <!-- 登录按钮 -->
    <el-form-item>
      <el-button
        type="primary"
        size="large"
        class="login-button"
        :loading="loginLoading"
        @click="handleLogin"
      >
        登录
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { newApi } from "@/utils/api-new";
import { useFreedomUserStore } from "@/stores/freedomUser";
import { saveEncryptedPassword, getDecryptedPassword, removeEncryptedPassword } from "@/utils/crypto";

// 定义组件事件
const emit = defineEmits<{
  loginSuccess: [];
}>();

// 自由交易用户状态管理
const freedomUserStore = useFreedomUserStore();

// 登录加载状态
const loginLoading = ref(false);

// 表单引用
const passwordFormRef = ref<FormInstance>();

// 密码登录表单数据
const passwordForm = reactive({
  username: "", // 用户名
  password: "", // 密码
  captcha: "", // 验证码
  rememberMe: false, // 记住密码
});

// 验证码相关数据
const captchaUrl = ref(""); // 验证码图片URL
const checkKey = ref(""); // 验证码唯一标识

// 密码登录验证规则
const passwordRules: FormRules = {
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    { min: 2, message: "用户名长度不能少于2位", trigger: "blur" },
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, message: "密码长度不能少于6位", trigger: "blur" },
  ],
  captcha: [
    { required: true, message: "请输入验证码", trigger: "blur" },
    { len: 4, message: "验证码长度为4位", trigger: "blur" },
  ],
};

/**
 * 处理自由交易登录
 */
const handleLogin = async () => {
  if (!passwordFormRef.value) return;

  try {
    // 表单验证
    await passwordFormRef.value.validate();

    loginLoading.value = true;

    // 调用自由交易登录API
    const response = await newApi.login({
      username: passwordForm.username,
      password: passwordForm.password,
      captcha: passwordForm.captcha,
      checkKey: checkKey.value,
      loginSource: 1, // 固定为1
    });

    
    
    if (response.code === 200) {
      // console.log('登录响应: ', response);
      // 登录成功
      const userInfo = response.result;

      // 保存自由交易用户状态
      freedomUserStore.freedomLogin(userInfo);

      // 如果选择记住密码，加密保存到本地存储
      if (passwordForm.rememberMe) {
        localStorage.setItem("freedomRememberedUsername", passwordForm.username);
        saveEncryptedPassword("freedomRememberedPassword", passwordForm.password);
      } else {
        localStorage.removeItem("freedomRememberedUsername");
        removeEncryptedPassword("freedomRememberedPassword");
      }

      ElMessage.success("登录成功");
      emit("loginSuccess");
    } else {
      // 登录失败时刷新验证码
      refreshCaptcha();
      ElMessage.error(response.message || "登录失败，请检查用户名和密码");
    }
  } catch (error: any) {
    console.error("自由交易登录失败:", error);
    
    // 登录失败时刷新验证码
    refreshCaptcha();
    
    // 根据HTTP状态码显示不同的错误信息
    if (error.response) {
      const status = error.response.status;
      switch (status) {
        case 401:
          ElMessage.error("用户名、密码或验证码错误");
          break;
        case 403:
          ElMessage.error("账户被禁用，请联系管理员");
          break;
        case 500:
          ElMessage.error("服务器内部错误，请稍后重试");
          break;
        default:
          ElMessage.error("登录失败，请检查网络连接");
      }
    } else {
      ElMessage.error("登录失败，请检查用户名、密码和验证码");
    }
  } finally {
    loginLoading.value = false;
  }
};

/**
 * 获取验证码图片
 * 在前端生成checkKey，然后调用API获取验证码图片
 */
const getCaptcha = async () => {
  try {
    // 在前端生成验证码唯一标识
    const timestamp = new Date().getTime()
    const randomStr = Math.random().toString(36).slice(-4)
    const generatedCheckKey = timestamp + randomStr
    
    // 调用API获取验证码（异步请求）
    const response = await newApi.getCaptcha(generatedCheckKey)
    console.log('验证码API响应:', response)
    
    // 检查API响应是否成功
     if (response.code === 200 || response.success) {
       // API返回的result字段包含base64格式的验证码图片数据
       const base64Image = response.result || response.data
       
       // 设置验证码图片（base64格式）和checkKey
       captchaUrl.value = base64Image
       checkKey.value = generatedCheckKey
       
       // 调试日志
       console.log('验证码base64数据长度:', base64Image?.length)
       console.log('验证码checkKey:', generatedCheckKey)
     } else {
       throw new Error(response.msg || '获取验证码失败')
     }
    
  } catch (error) {
    console.error('获取验证码失败:', error)
    ElMessage.error('获取验证码失败，请稍后重试')
  }
}

/**
 * 刷新验证码
 */
const refreshCaptcha = () => {
  passwordForm.captcha = ''; // 清空验证码输入
  getCaptcha();
};

/**
 * 处理验证码加载错误
 */
const handleCaptchaError = () => {
  console.error('验证码图片加载失败');
};

// 组件挂载时检查是否有记住的用户名和密码，并获取验证码
const rememberedUsername = localStorage.getItem("freedomRememberedUsername");
const rememberedPassword = getDecryptedPassword("freedomRememberedPassword");

if (rememberedUsername && rememberedPassword) {
  passwordForm.username = rememberedUsername;
  passwordForm.password = rememberedPassword;
  passwordForm.rememberMe = true;
}

// 初始化时获取验证码
getCaptcha();
</script>

<style lang="scss" scoped>
.login-form {
  .el-form-item {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .password {
    margin-bottom: 24px;
  }

  .captcha {
    margin-bottom: 8px;
  }

  // 验证码容器样式
  .captcha-container {
    display: flex;
    gap: 12px;
    align-items: center;

    .captcha-input {
      flex: 1;
      width: auto;
      height: 46px;
      
      :deep(.el-input__wrapper) {
        height: 46px;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        box-shadow: none;
        background-color: #fff;
        
        &:hover {
          border-color: #004c66;
        }
        
        &.is-focus {
          border-color: #004c66;
          box-shadow: 0 0 0 2px rgba(0, 76, 102, 0.1);
        }
      }

      :deep(.el-input__inner) {
        font-size: 16px;
        color: #333;
        
        &::placeholder {
          color: #999;
          font-size: 16px;
        }
      }
    }

    // 验证码图片包装器
    .captcha-image-wrapper {
      position: relative;
      width: 120px;
      height: 46px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: #004c66;
        
        .captcha-refresh-tip {
          opacity: 1;
        }
      }

      // 验证码图片
      .captcha-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
      }

      // 刷新提示
      .captcha-refresh-tip {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 76, 102, 0.8);
        color: #fff;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
      }
    }
  }

  .login-input {
    width: 100%;
    height: 46px;
    
    :deep(.el-input__wrapper) {
      height: 46px;
      border-radius: 6px;
      border: 1px solid #e0e0e0;
      box-shadow: none;
      background-color: #fff;
      
      &:hover {
        border-color: #004c66;
      }
      
      &.is-focus {
        border-color: #004c66;
        box-shadow: 0 0 0 2px rgba(0, 76, 102, 0.1);
      }
    }

    :deep(.el-input__inner) {
      font-size: 16px;
      color: #333;
      
      &::placeholder {
        color: #999;
        font-size: 16px;
      }
    }
  }

  .form-options {
    margin-bottom: 38px;

    :deep(.el-form-item__content) {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .remember-checkbox {
        :deep(.el-checkbox__label) {
          color: #666;
          font-size: 14px;
        }

        :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
          background-color: #004c66 !important;
          border-color: #004c66;
        }
      }
    }
  }

  .login-button {
    width: 100%;
    height: 48px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    
    &:hover {
      background: rgba(0, 76, 102, 0.9) !important;
    }
  }
}
</style>