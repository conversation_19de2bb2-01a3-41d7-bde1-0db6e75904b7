import { fileURLToPath, URL } from "node:url";
import { resolve } from "node:path";

import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import vueDevTools from "vite-plugin-vue-devtools";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";
import vitePrerender from "vite-plugin-prerender";
import ElementPlus from "unplugin-element-plus/vite";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";

import { visualizer } from "rollup-plugin-visualizer";
// 使用动态导入以避免ES模块中的require错误
/* const vitePrerender = await (async () => {
  try {
    return (await import('vite-plugin-prerender')).default
  } catch (e) {
    console.error('Failed to load vite-plugin-prerender:', e)
    return null
  }
})() */

// https://vite.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '');
  
  return {
  // 设置发布路径 - History模式需要使用绝对路径
  base: '/',
  // 设置构建输出目录
  build: {
    outDir: 'hgw_ui',
    // 静态资源处理 - 不使用子目录，所有文件都放在assets根目录
    assetsDir: 'assets',
    // 小于4kb的图片转为base64
    assetsInlineLimit: 4096,
    // 设置chunk大小警告限制为300kb（更严格）
    chunkSizeWarningLimit: 300,
    // 启用CSS代码分割
    cssCodeSplit: true,
    // 启用压缩
    minify: 'terser',
    // Terser压缩选项
    terserOptions: {
      compress: {
        // 移除console.log
        drop_console: true,
        // 移除debugger
        drop_debugger: true,
        // 移除无用代码
        dead_code: true,
        // 压缩条件表达式
        conditionals: true,
        // 压缩布尔值
        booleans: true,
        // 移除未使用的变量
        unused: true,
        // 内联函数
        inline: true
      },
      mangle: {
        // 混淆变量名
        toplevel: true
      }
    },
    rollupOptions: {
      // 外部化依赖（可选，用于CDN加载大型库）
      external: [],
      output: {
        // 简化资源打包 - 所有文件都放在assets根目录
        assetFileNames: 'assets/[name]-[hash][extname]',
        // JS文件也放在assets根目录
        chunkFileNames: 'assets/[name]-[hash].js',
        entryFileNames: 'assets/[name]-[hash].js',
        // 极简的代码分割策略 - 避免所有循环依赖
        manualChunks: (id) => {
          // Vue核心库
          if (id.includes('node_modules/vue/') || id.includes('node_modules/@vue/')) {
            return 'vue-vendor'
          }
          if (id.includes('node_modules/vue-router/')) {
            return 'vue-router'
          }
          if (id.includes('node_modules/pinia/')) {
            return 'pinia'
          }

          // ElementPlus 简化分割
          if (id.includes('node_modules/element-plus/')) {
            return 'element-plus'
          }

          // ElementPlus图标库
          if (id.includes('@element-plus/icons-vue')) {
            return 'element-icons'
          }

          // 网络请求相关
          if (id.includes('node_modules/axios/')) {
            return 'http-vendor'
          }

          // 工具库细分
          if (id.includes('js-md5') || id.includes('crypto-js')) {
            return 'crypto-vendor'
          }
          if (id.includes('animate.css')) {
            return 'animation-vendor'
          }
          if (id.includes('@vueuse/')) {
            return 'vueuse-vendor'
          }

          // Lodash 细分（如果使用）
          if (id.includes('node_modules/lodash')) {
            return 'vendor-lodash'
          }





          // 所有组件都打包到主chunk，避免循环依赖
          if (id.includes('/src/components/')) {
            return undefined // 不分割，打包到主chunk
          }

          // 所有页面组件都打包到主chunk，避免循环依赖
          if (id.includes('/src/views/')) {
            return undefined // 不分割，打包到主chunk
          }

          // 工具类细分
          if (id.includes('/src/utils/')) {
            if (id.includes('http')) {
              return 'utils-http'
            }
            if (id.includes('performance') || id.includes('preload')) {
              return 'utils-performance'
            }
            return 'utils-common'
          }

          // 存储和类型
          if (id.includes('/src/stores/')) {
            return 'app-stores'
          }
          if (id.includes('/src/types/')) {
            return 'app-types'
          }

          // 资源文件
          if (id.includes('/src/assets/')) {
            return 'app-assets'
          }

          // 其他第三方库统一打包，避免空文件
          if (id.includes('node_modules')) {
            return 'vendor-others'
          }
        }
      }
    }
  },
  plugins: [
    vue(),
    vueJsx(),
    // vueDevTools(),

    // 自动引入 Vue API
    AutoImport({
      resolvers: [ElementPlusResolver()],
      imports: ['vue', 'vue-router'],
      dts: true,
    }),

    // 自动引入 Vue 组件
    Components({
      resolvers: [ElementPlusResolver()],
      dts: true,
    }),

    // ElementPlus按需引入插件（自动引入样式）
    ElementPlus({
      useSource: true,
    }),
    createSvgIconsPlugin({
      // 指定需要缓存的图标文件夹
      iconDirs: [resolve(process.cwd(), "src/assets/icons")],
      // 指定symbolId格式
      symbolId: "icon-[dir]-[name]",
    }),
    // 只有当vitePrerender成功加载时才使用该插件
    vitePrerender &&
      vitePrerender({
        // 必需：指定Vite构建输出的应用程序路径
        staticDir: resolve(__dirname, "hgw_ui"),
        // 预渲染的路由列表
        routes: ["/", "/about"],
        // 可选：渲染完成后的处理函数
        postProcess: (context) => {
          // 可以在这里修改生成的HTML
          return context;
        },
      }),
    // Bundle analyzer (只在分析模式下启用)
    ...(process.env.ANALYZE ? [visualizer({
      filename: 'bundle-analysis.html',
      open: true,
      gzipSize: true,
      brotliSize: true,
    })] : []),
  ],
  css: {
    postcss: {
      /* plugins: [
        require("postcss-px-to-viewport")({
          unitToConvert: "px",
          viewportWidth: 1920, // 设计稿宽度
          unitPrecision: 5,
          propList: ["*"],
          viewportUnit: "vw",
          fontViewportUnit: "vw",
          selectorBlackList: [".ignore-vw", ".el-icon"], // 忽略转换的选择器
          minPixelValue: 1,
          mediaQuery: true, // 允许在媒体查询中转换px
          replace: true,
          // exclude: [/node_modules/], // 忽略node_modules文件夹下的文件
          landscape: false, // 是否处理横屏情况
        }),
      ], */
    },
  },
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
  },
  server: {
    host: env.VITE_HOST || '0.0.0.0',
    port: parseInt(env.VITE_PORT) || 8080,
    // 开发环境代理配置
    proxy: {
      // 旧API代理配置（原有接口）
      '/api': {
        target: env.VITE_OLD_API_BASE_URL || 'http://************:80',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
        // 配置代理请求头
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('旧API代理错误:', err);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('旧API代理请求:', req.method, req.url);
          });
        }
      },
      // 新API代理配置（新功能接口）
      '/new-api': {
        target: env.VITE_NEW_API_BASE_URL || 'http://************:18080/jeecgboot',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/new-api/, ''),
        // 配置代理请求头
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('新API代理错误:', err);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('新API代理请求:', req.method, req.url);
          });
        }
      }
    }
  },
  // 定义全局常量，在代码中可以使用
  define: {
    __OLD_API_BASE_URL__: JSON.stringify(env.VITE_OLD_API_BASE_URL),
    __NEW_API_BASE_URL__: JSON.stringify(env.VITE_NEW_API_BASE_URL),
    __IS_DEVELOPMENT__: mode === 'development'
  }
  };
});
