/* 优化字体加载性能 - 添加 font-display: swap */
@font-face {
  /* 自定义的字体名车，调用该字体时使用 */
  font-family: "FZZongYi-M05S";
  /* 引用字体包。.ttf后缀不区分大小写，用.TTF也可以 */
  src: url("FZZongYi-M05S.ttf");
  font-display: swap; /* 优化字体加载，避免白屏 */
}
@font-face {
  font-family: "PingFang Regular";
  src: url("PingFang Regular.ttf");
  font-display: swap;
}
@font-face {
  font-family: "PingFang Medium";
  src: url("PingFang Medium.ttf");
  font-display: swap;
}
@font-face {
  font-family: "PingFang Bold";
  src: url("PingFang Bold.ttf");
  font-display: swap;
}
@font-face {
  font-family: "DIN Regular";
  src: url("DIN-Regular.otf");
  font-display: swap;
}
@font-face {
  font-family: "DIN Bold";
  src: url("DIN-Bold.otf");
  font-display: swap;
}
@font-face {
  font-family: "DIN BlackItalic";
  src: url("DIN-BlackItalic.otf");
  font-display: swap;
}
@font-face {
  font-family: "YouSheBiaoTiHei";
  src: url("YouSheBiaoTiHei.ttf");
  font-display: swap;
}

/* 建议：考虑使用系统字体栈作为备选方案 */
/*
.system-font {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}
*/
