/**
 * @deprecated 此文件已重构，请使用新的模块化API
 * 新的API位于：
 * - http.ts: HTTP请求配置和拦截器
 * - auth.ts: 认证相关工具函数
 * - api.ts: 业务API接口
 * 
 * 为了保持向后兼容性，此文件仍然导出原有的API方法
 * 但建议逐步迁移到新的模块化API
 */

import api from './api'
import { getParams } from './auth'

// 为了向后兼容，重新导出所有API方法
// 注意：这些方法现在使用新的模块化API实现
// 向后兼容的API方法实现
// 使用新的模块化API重新实现原有方法
// 获取首页数据
function HomeBanner() {
	return api.homeApi.getBanner()
}

// home页面的竞价会条数
function Homepmhnum(id) {
	return api.homeApi.getPmhNum()
}

// home页面近期竞价会
function HomeJinqipaimaihui(data) {
	return api.homeApi.getRecentAuctions(data)
}
// home成交案例
function HomeDaycjbiaodio() {
	return api.homeApi.getTransactionCases()
}

// home今日拍卖标的
function daybiaodi() {
	return api.homeApi.getTodayAuctions()
}

// 首页竞价会公告
function paimaihui() {
	return api.homeApi.getAuctionAnnouncements()
}

// 首页新闻列表展示
function newsindex(data) {
	return api.homeApi.getNewsList(data)
}

// 合作企业
function hzqiyelist(data) {
	return api.homeApi.getPartnerCompanies(data)
}

// 注册
function register(data) {
	return api.authApi.register(data)
}

// 发送验证码
function LoginPress(data) {
	return api.authApi.sendSms(data)
}

// 验证码 登录
function Login(data) {
	return api.authApi.login(data)
}

// 验证码登录
function dx_login(data) {
	return api.authApi.smsLogin(data)
}

// 标的列表
function biaodilist(data) {
	return api.auctionApi.getAuctionList(data)
}

// 标的参数
function biaodicanshu(data) {
	return api.auctionApi.getAuctionFilters()
}

// 竞价会列表
function paimaihuilist(data) {
	return api.auctionSessionApi.getAuctionSessionList(data)
}

// 拍卖公司列表
function pmqiyeList(data) {
	return api.companyApi.getCompanyList(data)
}

// 企业详情
function pmqiyeDetail(data) {
	return api.companyApi.getCompanyDetail(data)
}

// 我参与的标列表
function mycybiaodilist(data) {
	return api.userApi.getMyParticipatedAuctions(data)
}

// 我收藏的企业
function myscqiyelist(data) {
	return api.userApi.getMyFavoriteCompanies(data)
}

// 我收藏的标列表
function myscbiaodilist(data) {
	return api.userApi.getMyFavoriteAuctions(data)
}

// 我竞得的标列表
function myjdbiaodilist(data) {
	return api.userApi.getMyWonAuctions(data)
}

// 我报名的标列表
function mybmbiaodilist(data) {
	return api.userApi.getMyRegisteredAuctions(data)
}

// 保存头像
function saveheader(data) {
	return api.userApi.saveAvatar(data)
}

// 获取oss签名信息
function aliosspolicy(data) {
	return api.uploadApi.getOssPolicy()
}

// 标的收藏，取消收藏
function scbiaodi(data) {
	return api.auctionApi.favoriteAuction(data)
}

// 企业收藏，取消收藏
function scqiye(data) {
	return api.companyApi.favoriteCompany(data)
}

// 企业,个人认证
function rzupload(data) {
	return api.userApi.uploadCertification(data)
}

// 标的详情
function biaodiinfo(data) {
	return api.auctionApi.getAuctionDetail(data)
}

// 竞价会详情
function xq_bdlist(data) {
	return api.auctionApi.getAuctionSessionList(data)
}

// 未审的标的详情
function weishen_biaodiinfo(data) {
	return api.auctionApi.getUnauditedAuctionDetail(data)
}

// 未审的竞价会详情
function weishen_xq_bdlist(data) {
	return api.auctionApi.getUnauditedAuctionSessionList(data)
}

// 出价记录
function chujiaList(data) {
	return api.biddingApi.getBiddingHistory(data)
}

// 发言记录
function fayanList(data) {
	return api.biddingApi.getCommentHistory(data)
}

// 报名标的
function baoming_bd(data) {
	return api.auctionApi.registerAuction(data)
}

// 看货
function kanhuo_bd(data) {
	return api.auctionApi.viewAuction(data)
}

// 确认上传缴纳证明
function zhengming_bd(data) {
	return api.auctionApi.uploadPaymentProof(data)
}

// 出价
function chujia_bd(data) {
	return api.auctionApi.bidAuction(data)
}

// 新闻列表
function newslist(data) {
	return api.newsApi.getNewsList(data)
}

// 新闻详情
function newsinfo(data) {
	return api.newsApi.getNewsDetail(data)
}

// 企业竞价会列表
function pmhlist(data) {
	return api.companyApi.getCompanyAuctionSessions(data)
}

// 企业标的列表
function bdlist(data) {
	return api.companyApi.getCompanyAuctions(data)
}

// 企业拍卖公告列表
function pmhgglist(data) {
	return api.companyApi.getCompanyAnnouncements(data)
}

// 报名信息
function bmverify(data) {
	return api.userApi.getRegistrationInfo(data)
}

// 竞价会信息
function paimaihuibd(data) {
	return api.auctionSessionApi.getAuctionSessionInfo(data)
}

// 标的收藏，取消
function biaodiscqiye(data) {
	return api.auctionApi.favoriteAuction(data)
}

// 标的是否收藏
function biaodizybdsc(data) {
	return api.auctionApi.checkAuctionFavorite(data)
}

// 企业入驻
function qiyeruzhu(data) {
	return api.companyApi.companyRegister(data)
}

// 忘记密码
function zhaohuipwd(data) {
	return api.authApi.resetPassword(data)
}

// 修改密码
function xiugaimima(data) {
	return api.authApi.changePassword(data)
}
// 竞价会公告
function pmhinfo(data) {
	return api.auctionSessionApi.getAuctionSessionAnnouncement(data)
}

// 竞价会公告列表
function pmhlistInfo(data) {
	return api.auctionSessionApi.getAuctionSessionAnnouncementList(data)
}

// 企业是否收藏
function zyqyscIsClick(data) {
	return api.companyApi.checkCompanyFavorite(data)
}

// 认证信息
function getRenzheng(data) {
	return api.userApi.getCertificationInfo(data)
}

// 是否认证
function isRenzheng(data) {
	return api.userApi.checkCertification(data)
}

// 减价出价
function jianjiabd(data) {
	return api.biddingApi.reducePrice(data)
}

// 减价出价的竞价列表
function jianjialist(data) {
	return api.biddingApi.getReducePriceList(data)
}

// 一次性出价
function yicixingChujia(data) {
	return api.biddingApi.oneTimeBid(data)
}

// 一次性出价记录
function yicixingChujialist(data) {
	return api.biddingApi.getOneTimeBidList(data)
}

// 混拍竞价排名
function hunJialist(data) {
	return api.biddingApi.getMixedBiddingList(data)
}

// 竞价会详情
function paimaihuiinfo(data) {
	return api.auctionSessionApi.getAuctionSessionDetail(data)
}

// 物资租赁分类
function zulinfenlei(data) {
	return api.rentalApi.getRentalCategories(data)
}

// 物资租赁分类（新版）
function zulinfenleinew(data) {
	return api.rentalApi.getNewRentalCategories(data)
}

// 物资租赁列表
function zulinlist(data) {
	return api.rentalApi.getRentalList(data)
}

// 物资租赁详情
function zulininfo(data) {
	return api.rentalApi.getRentalDetail(data)
}
// 钢价行情
function gangjiainfo(data) {
	return api.rentalApi.getSteelPrices(data)
}

// 物资设备租售成交案例
function anlilist(data) {
	return api.rentalApi.getTransactionCases(data)
}

// 跳转物资设备租售两个页面请求
function touser(data) {
	return api.otherApi.toUser(data)
}

// 公告列表（销售和招标）
function zhaobiaolist(data) {
	return api.otherApi.getTenderList(data)
}

// 公告详情（销售和招标）
function zhaobiaoinfo(data) {
	return api.otherApi.getTenderDetail(data)
}
export default {
	zhaobiaolist,
	zhaobiaoinfo,
	HomeBanner,
	Homepmhnum,
	HomeJinqipaimaihui,
	HomeDaycjbiaodio,
	daybiaodi,
	paimaihui,
	newsindex,
	hzqiyelist,
	register,
	LoginPress,
	Login,
	dx_login,
	biaodilist,
	paimaihuilist,
	pmqiyeList,
	pmqiyeDetail,
	mycybiaodilist,
	myscqiyelist,
	myscbiaodilist,
	myjdbiaodilist,
	mybmbiaodilist,
	aliosspolicy,
	biaodicanshu,
	saveheader,
	scbiaodi,
	scqiye,
	rzupload,
	biaodiinfo,
	xq_bdlist,
	weishen_biaodiinfo,
	weishen_xq_bdlist,
	chujiaList,
	fayanList,
	baoming_bd,
	kanhuo_bd,
	zhengming_bd,
	chujia_bd,
	newslist,
	newsinfo,
	pmhlist,
	bdlist,
	pmhgglist,
	bmverify,
	paimaihuibd,
	biaodiscqiye,
	biaodizybdsc,
	qiyeruzhu,
	zhaohuipwd,
	xiugaimima,
	pmhinfo,
	pmhlistInfo,
	zyqyscIsClick,
	getRenzheng,
	isRenzheng,
	jianjiabd,
	jianjialist,
	yicixingChujia,
	yicixingChujialist,
	hunJialist,
	paimaihuiinfo,
	zulinfenlei,
	zulininfo,
	zulinlist,
	zulinfenleinew,
	gangjiainfo,
	anlilist,
	touser
}
