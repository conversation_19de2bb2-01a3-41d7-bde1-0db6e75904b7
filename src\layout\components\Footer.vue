<template>
  <footer class="footer">
    <!-- 顶部内容区域 -->
    <div class="footer-container">
      <div class="footer-top">
        <!-- 左侧Logo和导航链接 -->
        <div class="footer-left">
          <div class="footer-logo">
            <img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/logo_1754960593997.png" class="logo-icon" />
            <!-- <SvgIcon iconName="title" className="title-icon" /> -->
          </div>
          <!-- 导航链接 -->
          <div class="footer-nav">
            <div class="nav-item" @click="navigateTo('/')">
              <span>网站首页</span>
            </div>
            <div class="nav-item" @click="navigateTo('/about')">
              <span>关于我们</span>
            </div>
            <div class="nav-item" @click="navigateTo('/register')">
              <span>注册流程</span>
            </div>
            <div class="nav-item" @click="navigateTo('/guide')">
              <span>操作指南</span>
            </div>
            <div class="nav-item" @click="navigateTo('/certificate')">
              <span>资质证明</span>
            </div>
            <div class="nav-item" @click="navigateTo('/service')">
              <span>服务器协议</span>
            </div>
            <div class="nav-item" @click="navigateTo('/faq')">
              <span>常见问题</span>
            </div>
            <div class="nav-item" @click="navigateTo('/privacy')">
              <span>免责声明</span>
            </div>
          </div>
        </div>

        <!-- 右侧联系信息 -->
        <div class="contact-info">
          <div class="contact-item">
            <SvgIcon iconName="phone" className="contact-icon" />
            <span>************</span>
          </div>
          <div class="contact-item">
            <SvgIcon iconName="email" className="contact-icon" />
            <span><EMAIL></span>
          </div>
          <div class="contact-item">
            <SvgIcon iconName="address" className="contact-icon" />
            <span>河南自贸试验区郑州片区(郑东) 商务外环路20号11楼1109室</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部版权信息 -->
    <div class="footer-bottom">
      <div class="bottom-container">
        <div class="copyright">
          COPYRIGHT©2024 河南灰谷科技有限公司 版权所有
        </div>
        <div class="icp">豫ICP备20240802号-3</div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import SvgIcon from "@/components/SvgIcon.vue";

const router = useRouter();

const navigateTo = (path: string) => {
  router.push(path);
};
</script>

<style lang="scss" scoped>
.footer {
  width: 100%;
  height: 208px;
  background-color: #337085;
  color: #ffffff;
}

.footer-container {
  max-width: 1280px;
  height: 171px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  // 媒体查询 125%
  @media (max-width: 1600px) {
    margin: 0 10px;
  }
  // 媒体查询 150%
  @media (max-width: 1500px) {
    margin: 0 30px;
  }
}

.footer-top {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.footer-left {
  display: flex;
  flex-direction: column;
  gap: 26.88px;
  max-width: 60%;
}

.footer-logo {
  display: flex;
  align-items: end;
  gap: 10px;
}

.logo-icon {
  width: 302px;
  height: 45px;
}

.title-icon {
  width: 161.51px;
  height: 22.25px;
}

.footer-nav {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.nav-item {
  cursor: pointer;
  font-size: 16px;
  transition: color 0.3s;
  position: relative;
  padding-right: 10px;
  color: #fff;
  span {
    font-family: "PingFang Regular";
  }
}

.nav-item:not(:last-child)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 14px;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.5);
}

.nav-item:hover {
  color: #a8d8ff;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 7px;
  max-width: 35%;
}

.contact-item {
  display: flex;
  align-items: start;
  gap: 5px;
  font-size: 14px;
  color: #fff;
  span {
    max-width: 192px;
    line-height: 1.35;
    font-family: "PingFang Regular";
  }
}

.contact-icon {
  width: 16px;
  height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
}

.footer-bottom {
  background-color: #004c66;
  height: 37px;
  line-height: 37px;
}

.bottom-container {
  max-width: 1280px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}
</style>
