<template>
  <div class="custom-scrollbar" v-show="showScrollbar">
    <!-- 滚动条轨道 -->
    <div 
      class="scrollbar-track" 
      ref="trackRef"
      @click="handleTrackClick"
    >
      <!-- 滚动条滑块 -->
      <div 
        class="scrollbar-thumb" 
        ref="thumbRef"
        :style="thumbStyle"
        @mousedown="handleThumbMouseDown"
      >
        <!-- 滚动位置指示器 -->
        <!-- <div class="scroll-indicator">
          <span class="scroll-percentage">{{ scrollPercentage }}%</span>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

// 响应式数据
const trackRef = ref<HTMLElement>() // 滚动条轨道引用
const thumbRef = ref<HTMLElement>() // 滚动条滑块引用
const scrollTop = ref(0) // 当前滚动位置
const scrollHeight = ref(0) // 文档总高度
const clientHeight = ref(0) // 可视区域高度
const isDragging = ref(false) // 是否正在拖拽
const dragStartY = ref(0) // 拖拽开始时的Y坐标
const dragStartScrollTop = ref(0) // 拖拽开始时的滚动位置
const animationFrameId = ref<number | null>(null) // 动画帧ID，用于优化性能
const scrollTimer = ref<number | null>(null) // 滚动节流定时器
const lastDragScrollTop = ref(0) // 最后一次拖拽的滚动位置，用于防止回弹

/**
 * 计算滚动百分比
 */
const scrollPercentage = computed(() => {
  if (scrollHeight.value <= clientHeight.value) return 0
  const percentage = Math.round((scrollTop.value / (scrollHeight.value - clientHeight.value)) * 100)
  return Math.min(100, Math.max(0, percentage))
})

/**
 * 计算是否显示滚动条
 */
const showScrollbar = computed(() => {
  return scrollHeight.value > clientHeight.value
})

/**
 * 计算滑块样式（修复版本，确保准确计算）
 */
const thumbStyle = computed(() => {
  if (!showScrollbar.value || scrollHeight.value <= 0 || clientHeight.value <= 0) {
    return { height: '0px', top: '0px', opacity: '0' }
  }

  // 获取轨道高度，如果还没有渲染完成则使用视口高度
  const trackHeight = trackRef.value?.clientHeight || window.innerHeight

  // 计算滑块高度比例，确保不会出现除零错误
  const heightRatio = Math.min(1, clientHeight.value / scrollHeight.value)
  const thumbHeight = Math.max(30, Math.min(trackHeight * 0.8, heightRatio * trackHeight))

  // 计算滑块位置，确保不会出现NaN
  const maxScrollTop = Math.max(0, scrollHeight.value - clientHeight.value)
  const maxThumbTop = Math.max(0, trackHeight - thumbHeight)
  const scrollRatio = maxScrollTop > 0 ? Math.min(1, Math.max(0, scrollTop.value / maxScrollTop)) : 0
  const thumbTop = scrollRatio * maxThumbTop

  return {
    height: `${Math.round(thumbHeight)}px`,
    top: `${Math.round(thumbTop)}px`,
    opacity: isDragging.value ? '0.8' : '0.6',
    // 拖拽时禁用过渡动画，提升响应性
    transition: isDragging.value ? 'none' : 'all 0.2s ease'
  }
})

/**
 * 更新滚动信息（直接更新，避免延迟）
 */
const updateScrollInfo = () => {
  scrollTop.value = window.pageYOffset || document.documentElement.scrollTop
  scrollHeight.value = document.documentElement.scrollHeight
  clientHeight.value = window.innerHeight
}

/**
 * 处理滚动事件（移除节流，提高响应性）
 */
const handleScroll = () => {
  if (!isDragging.value) {
    const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop
    // 只有当滚动位置真正改变时才更新
    if (Math.abs(currentScrollTop - scrollTop.value) > 0.5) {
      updateScrollInfo()
    }
  }
}

/**
 * 处理窗口大小变化
 */
const handleResize = () => {
  // 延迟更新，确保DOM重新布局完成
  setTimeout(() => {
    updateScrollInfo()
  }, 100)
}

/**
 * 处理轨道点击事件
 */
const handleTrackClick = (event: MouseEvent) => {
  if (!trackRef.value || !thumbRef.value) return
  
  const trackRect = trackRef.value.getBoundingClientRect()
  const clickY = event.clientY - trackRect.top
  const thumbHeight = thumbRef.value.clientHeight
  
  // 计算目标滚动位置
  const trackHeight = trackRef.value.clientHeight
  const maxScrollTop = scrollHeight.value - clientHeight.value
  const targetScrollTop = ((clickY - thumbHeight / 2) / (trackHeight - thumbHeight)) * maxScrollTop
  
  // 平滑滚动到目标位置
  window.scrollTo({
    top: Math.max(0, Math.min(maxScrollTop, targetScrollTop)),
    behavior: 'smooth'
  })
}

/**
 * 处理滑块鼠标按下事件
 */
const handleThumbMouseDown = (event: MouseEvent) => {
  event.preventDefault()
  event.stopPropagation()

  isDragging.value = true
  dragStartY.value = event.clientY
  dragStartScrollTop.value = scrollTop.value
  lastDragScrollTop.value = scrollTop.value // 记录拖拽开始位置

  // 在开始拖拽时移除滚动监听器
  window.removeEventListener('scroll', handleScroll)

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  document.body.style.userSelect = 'none'
}

/**
 * 处理鼠标移动事件（拖拽）- 修复回弹问题
 */
const handleMouseMove = (event: MouseEvent) => {
  if (!isDragging.value || !trackRef.value || !thumbRef.value) return

  event.preventDefault()

  const deltaY = event.clientY - dragStartY.value
  const trackHeight = trackRef.value.clientHeight
  const thumbHeight = thumbRef.value.clientHeight
  const maxScrollTop = scrollHeight.value - clientHeight.value
  const maxThumbTop = trackHeight - thumbHeight

  if (maxThumbTop > 0 && maxScrollTop > 0) {
    const scrollDelta = (deltaY / maxThumbTop) * maxScrollTop
    const newScrollTop = Math.max(0, Math.min(maxScrollTop, dragStartScrollTop.value + scrollDelta))

    // 只有当位置真正改变时才更新
    if (Math.abs(newScrollTop - lastDragScrollTop.value) > 0.5) {
      // 直接设置页面滚动位置
      window.scrollTo(0, newScrollTop)

      // 立即更新scrollTop值和记录位置
      scrollTop.value = newScrollTop
      lastDragScrollTop.value = newScrollTop
    }
  }
}

/**
 * 处理鼠标释放事件
 */
const handleMouseUp = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
  document.body.style.userSelect = ''

  // 确保最终位置正确设置
  const finalScrollTop = lastDragScrollTop.value
  window.scrollTo(0, finalScrollTop)
  scrollTop.value = finalScrollTop

  // 拖拽结束后延迟重新添加滚动监听器，避免回弹
  setTimeout(() => {
    window.addEventListener('scroll', handleScroll, { passive: true })
  }, 100)
}

/**
 * 组件挂载时的初始化
 */
onMounted(async () => {
  await nextTick()

  // 等待DOM完全渲染后再初始化
  setTimeout(() => {
    updateScrollInfo()
  }, 100)

  // 添加事件监听器 - 页面滚动发生在window上，不是main-content元素上
  window.addEventListener('scroll', handleScroll, { passive: true })
  window.addEventListener('resize', handleResize, { passive: true })

  // 监听DOM变化，确保滚动信息及时更新
  const observer = new MutationObserver(() => {
    setTimeout(updateScrollInfo, 50)
  })

  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['style', 'class']
  })

  // 保存observer引用以便清理
  ;(window as any).__scrollbarObserver = observer
})

/**
 * 组件卸载时的清理
 */
onUnmounted(() => {
  // 清理动画帧
  if (animationFrameId.value) {
    cancelAnimationFrame(animationFrameId.value)
    animationFrameId.value = null
  }

  // 清理滚动定时器
  if (scrollTimer.value) {
    clearTimeout(scrollTimer.value)
    scrollTimer.value = null
  }

  // 清理MutationObserver
  const observer = (window as any).__scrollbarObserver
  if (observer) {
    observer.disconnect()
    delete (window as any).__scrollbarObserver
  }

  // 移除滚动监听器
  window.removeEventListener('scroll', handleScroll)
  window.removeEventListener('resize', handleResize)
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
})
</script>

<style lang="scss" scoped>
.custom-scrollbar {
  position: fixed;
  top: 0;
  right: 0;
  width: 12px;
  height: 100vh;
  z-index: 9999;
  pointer-events: auto;
  
  .scrollbar-track {
    position: relative;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0);
    cursor: pointer;
    transition: background 0.2s ease;
    
    &:hover {
      
      .scrollbar-thumb {
        opacity: 0.8 !important;
        width: 10px;
        
        .scroll-indicator {
          opacity: 1;
          transform: translateX(-100%) scale(1);
        }
      }
    }
  }
  
  .scrollbar-thumb {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 8px;
    background: #ccc;
    border-radius: 6px;
    cursor: grab;
    // 移除默认过渡动画，通过JavaScript动态控制
    // transition: all 0.2s ease;
    opacity: 0.6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    &:hover {
      opacity: 0.8;
    }
    
    &:active {
      cursor: grabbing;
      opacity: 1;
      transform: translateX(-50%) scale(1.1);
    }
    
    .scroll-indicator {
      position: absolute;
      right: 100%;
      top: 50%;
      transform: translateX(-10px) translateY(-50%) scale(0.8);
      background: rgba(0, 76, 102, 0.9);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      white-space: nowrap;
      opacity: 0;
      transition: all 0.2s ease;
      pointer-events: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      
      &::after {
        content: '';
        position: absolute;
        left: 100%;
        top: 50%;
        transform: translateY(-50%);
        border: 4px solid transparent;
        border-left-color: rgba(0, 76, 102, 0.9);
      }
      
      .scroll-percentage {
        font-family: 'Consolas', 'Monaco', monospace;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .custom-scrollbar {
    width: 8px;
    
    .scrollbar-thumb {
      width: 6px;
      
      .scroll-indicator {
        font-size: 10px;
        padding: 2px 6px;
      }
    }
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .custom-scrollbar {
    .scrollbar-track {
      background: rgba(0, 0, 0, 0.2);
    }
    
    .scrollbar-thumb {
      background: #000;
      opacity: 0.8;
    }
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .custom-scrollbar {
    .scrollbar-track,
    .scrollbar-thumb,
    .scroll-indicator {
      transition: none;
    }
  }
}
</style>