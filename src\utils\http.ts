import axios from 'axios'
import type { AxiosResponse, AxiosError } from 'axios'
import { ElMessage } from "element-plus";

/**
 * HTTP请求配置和拦截器
 * 用于统一处理请求和响应的拦截逻辑
 */

// 创建axios实例
const http = axios.create({
  // 开发环境使用代理，生产环境使用实际API地址
  baseURL: import.meta.env.DEV ? '/api' : (import.meta.env.VITE_OLD_API_BASE_URL || 'http://39.101.72.34:80'),
  timeout: 10000, // 请求超时时间
  withCredentials: false // 是否携带cookie
})

/**
 * 检测是否为IE9浏览器
 * @returns {boolean} 是否为IE9
 */
function isIE9(): boolean {
  if (
    navigator.appName === "Microsoft Internet Explorer" &&
    parseInt(
      navigator.appVersion
        .split(";")
        [1].replace(/[ ]/g, "")
        .replace("MSIE", "")
    ) <= 9
  ) {
    return true
  }
  return false
}

/**
 * 响应拦截器
 * 用于统一处理响应数据和错误
 */
http.interceptors.response.use(
  (response: AxiosResponse) => {
    // 判断是否为IE9浏览器，进行特殊处理
    if (isIE9()) {
      // 特殊处理IE9的响应数据
      if (response.status === 200 && response.request) {
        if (
          response.request.responseType === "json" &&
          response.request.responseText
        ) {
          response.data = JSON.parse(response.request.responseText)
          // console.log("IE9 response processed:", response)
        }
      }
    }
    return response
  },
  (error: AxiosError) => {
    if (error.response) {
      // 处理HTTP错误状态码
      console.error("HTTP Error:", error)
      
      // 根据状态码显示不同的错误信息
      switch (error.response.status) {
        case 401:
          ElMessage.error('未授权，请重新登录')
          // 可以在这里处理登录跳转逻辑
          break
        case 403:
          ElMessage.error('拒绝访问')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error('网络错误，请稍后重试')
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应
      ElMessage.error('网络连接超时，请检查网络')
    } else {
      // 其他错误
      ElMessage.error('请求配置错误')
    }
    return Promise.reject(error)
  }
)

/**
 * 请求拦截器（可选）
 * 用于在请求发送前进行统一处理
 */
http.interceptors.request.use(
  (config) => {
    // 可以在这里添加token等认证信息
    // const token = localStorage.getItem('token')
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`
    // }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 导出配置好的axios实例
export default http

// 导出类型定义供其他文件使用（已移除）