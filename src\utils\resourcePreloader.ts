/**
 * 资源预加载工具
 * 用于预加载关键的CSS和JS文件，优化首屏加载性能
 */

// 预加载资源类型枚举
enum PreloadType {
  SCRIPT = 'script',
  STYLE = 'stylesheet',
  FONT = 'font',
  IMAGE = 'image'
}

// 预加载资源配置接口
interface PreloadResource {
  href: string;           // 资源URL
  as: PreloadType;        // 资源类型
  crossorigin?: string;   // 跨域设置
  type?: string;          // MIME类型
}

/**
 * 创建预加载链接元素
 * @param resource 预加载资源配置
 * @returns HTMLLinkElement
 */
function createPreloadLink(resource: PreloadResource): HTMLLinkElement {
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = resource.href;
  link.as = resource.as;
  
  // 设置跨域属性
  if (resource.crossorigin) {
    link.crossOrigin = resource.crossorigin;
  }
  
  // 设置MIME类型
  if (resource.type) {
    link.type = resource.type;
  }
  
  return link;
}

/**
 * 预加载单个资源
 * @param resource 预加载资源配置
 * @returns Promise<void>
 */
export function preloadResource(resource: PreloadResource): Promise<void> {
  return new Promise((resolve, reject) => {
    const link = createPreloadLink(resource);
    
    // 监听加载完成事件
    link.onload = () => {
      // console.log(`✅ 资源预加载成功: ${resource.href}`); 已移除
      resolve();
    };
    
    // 监听加载失败事件
    link.onerror = () => {
      // console.warn(`❌ 资源预加载失败: ${resource.href}`); 已移除
      reject(new Error(`Failed to preload resource: ${resource.href}`));
    };
    
    // 添加到文档头部
    document.head.appendChild(link);
  });
}

/**
 * 批量预加载资源
 * @param resources 预加载资源配置数组
 * @returns Promise<void[]>
 */
export function preloadResources(resources: PreloadResource[]): Promise<void[]> {
  const preloadPromises = resources.map(resource => 
    preloadResource(resource).catch(error => {
      // 单个资源加载失败不影响其他资源
      // console.warn('资源预加载失败:', error); 已移除
      return Promise.resolve();
    })
  );
  
  return Promise.all(preloadPromises);
}

/**
 * 预加载关键CSS文件
 * @param cssUrls CSS文件URL数组
 * @returns Promise<void[]>
 */
export function preloadCriticalCSS(cssUrls: string[]): Promise<void[]> {
  const cssResources: PreloadResource[] = cssUrls.map(url => ({
    href: url,
    as: PreloadType.STYLE
  }));
  
  return preloadResources(cssResources);
}

/**
 * 预加载关键JS文件
 * @param jsUrls JS文件URL数组
 * @returns Promise<void[]>
 */
export function preloadCriticalJS(jsUrls: string[]): Promise<void[]> {
  const jsResources: PreloadResource[] = jsUrls.map(url => ({
    href: url,
    as: PreloadType.SCRIPT
  }));
  
  return preloadResources(jsResources);
}

/**
 * 预加载字体文件
 * @param fontUrls 字体文件URL数组
 * @returns Promise<void[]>
 */
export function preloadFonts(fontUrls: string[]): Promise<void[]> {
  const fontResources: PreloadResource[] = fontUrls.map(url => ({
    href: url,
    as: PreloadType.FONT,
    crossorigin: 'anonymous'
  }));
  
  return preloadResources(fontResources);
}

/**
 * DNS预解析
 * @param domains 域名数组
 */
export function preconnectDomains(domains: string[]): void {
  domains.forEach(domain => {
    const link = document.createElement('link');
    link.rel = 'preconnect';
    link.href = domain;
    document.head.appendChild(link);
  });
}

/**
 * 资源预加载管理器
 */
export class ResourcePreloader {
  private preloadedResources: Set<string> = new Set();
  
  /**
   * 检查资源是否已预加载
   * @param url 资源URL
   * @returns boolean
   */
  isPreloaded(url: string): boolean {
    return this.preloadedResources.has(url);
  }
  
  /**
   * 标记资源为已预加载
   * @param url 资源URL
   */
  markAsPreloaded(url: string): void {
    this.preloadedResources.add(url);
  }
  
  /**
   * 智能预加载资源（避免重复预加载）
   * @param resources 预加载资源配置数组
   * @returns Promise<void[]>
   */
  async smartPreload(resources: PreloadResource[]): Promise<void[]> {
    // 过滤已预加载的资源
    const newResources = resources.filter(resource => 
      !this.isPreloaded(resource.href)
    );
    
    if (newResources.length === 0) {
      // console.log('所有资源已预加载，跳过重复预加载'); 已移除
      return Promise.resolve([]);
    }
    
    // 预加载新资源
    const result = await preloadResources(newResources);
    
    // 标记为已预加载
    newResources.forEach(resource => {
      this.markAsPreloaded(resource.href);
    });
    
    return result;
  }
}

// 导出默认实例
export const resourcePreloader = new ResourcePreloader();

/**
 * 初始化关键资源预加载
 * 在应用启动时调用，预加载首屏必需的资源
 */
export function initCriticalResourcePreload(): void {
  // 预连接到API域名
  preconnectDomains([
    'http://39.101.72.34:80',
    'http://39.101.72.34:18080'
  ]);
  
  // console.log('🚀 关键资源预加载初始化完成'); 已移除
}