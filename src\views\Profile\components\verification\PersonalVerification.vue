<template>
  <div class="personal-verification">
    <!-- 个人认证表单 -->
    <div class="form-section">
      <!-- 姓名 -->
      <div class="form-item">
        <label class="form-label">姓名</label>
        <div class="form-value">
          <input
            v-model="personalForm.cnname"
            type="text"
            class="form-input"
            placeholder="请输入姓名"
          />
        </div>
      </div>

      <!-- 身份证号 -->
      <div class="form-item">
        <label class="form-label">身份证号</label>
        <div class="form-value">
          <input
            v-model="personalForm.cardnum"
            type="text"
            class="form-input"
            placeholder="请输入身份证号"
          />
        </div>
      </div>

      <!-- 开户行 -->
      <div class="form-item">
        <label class="form-label">开户行</label>
        <div class="form-value">
          <input
            v-model="personalForm.gerenyinhang"
            type="text"
            class="form-input"
            placeholder="请输入开户行"
          />
        </div>
      </div>

      <!-- 银行账号 -->
      <div class="form-item">
        <label class="form-label">银行账号</label>
        <div class="form-value">
          <input
            v-model="personalForm.gerenkahao"
            type="text"
            class="form-input"
            placeholder="请输入银行账号"
          />
        </div>
      </div>

      <!-- 回收种类 -->
      <div class="form-item">
        <label class="form-label">回收种类</label>
        <div class="form-value">
          <el-checkbox-group
            v-model="personalForm.biaoqian"
            class="checkbox-group"
          >
            <el-checkbox
              v-for="option in recycleOptions"
              :key="option.value"
              :value="option.value"
              class="checkbox-item"
            >
              {{ option.label }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>

      <!-- 所属地区 -->
      <div class="form-item">
        <label class="form-label">所属地区</label>
        <div class="form-value">
          <el-cascader
            v-model="personalForm.selectedArea"
            :options="areaOptions"
            :props="{ expandTrigger: 'hover' }"
            placeholder="请选择省市区"
            class="area-cascader"
            @change="handleAreaChange"
          />
        </div>
      </div>

      <!-- 上传证件 -->
      <div class="form-item">
        <label class="form-label">上传证件</label>
        <div class="form-value">
          <div class="upload-section">
            <p class="upload-description">
              请上传身份证件，要求为彩色，最多可上传2个附件，单文件最大5MB，类型支持gif、png、jpg、bmp。
            </p>
            <div class="upload-group">
              <UploadCard
                uploadText="身份证正面"
                backgroundImage="@/assets/icons/upload/idcard-front.svg"
                :imageUrl="`${personalForm.cardpicz}`"
                @upload="handleIdCardFrontUpload"
              />
              <UploadCard
                uploadText="身份证反面"
                backgroundImage="@/assets/icons/upload/idcard-reverse.svg"
                :imageUrl="personalForm.cardpicf"
                @upload="handleIdCardBackUpload"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, computed, onMounted, defineExpose } from "vue";
import { ElMessage } from "element-plus"; // ElCheckboxGroup, ElCheckbox, ElCascader 由 unplugin-element-plus 自动引入
import UploadCard from "@/components/UploadCard.vue";
import { userApi } from "@/utils/api";
import { uploadToOSS, validateFile } from "./uploadUtils";
import { getAreaCodesByNames, type AreaOption } from "./areaUtils";
import type { CascaderValue } from "element-plus";
import { useUserStore } from "@/stores/user";

// 定义组件的 props
interface Props {
  areaOptions: AreaOption[];
  recycleOptions: Array<{ value: string; label: string }>;
}

// 定义组件的 emits
interface Emits {
  (e: 'save', data: any): void;
  (e: 'submit', data: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 用户状态管理
const userStore = useUserStore();

// 个人认证表单数据
const personalForm = reactive({
  cnname: "", // 姓名
  cardnum: "", // 身份证号
  gerenyinhang: "", // 开户行
  gerenkahao: "", // 银行账号
  biaoqian: [] as string[], // 回收种类（多选）
  province: "", // 省
  city: "", // 市
  district: "", // 区
  selectedArea: [] as string[], // 级联选择器的值
  cardpicz: "", // 身份证正面URL
  cardpicf: "", // 身份证反面URL
  idCardFront: null as File | null, // 身份证正面文件
  idCardBack: null as File | null, // 身份证反面文件
  member_id: userStore.userInfo ? userStore.userInfo.id : 0, // 会员ID
});



/**
 * 处理地区选择变化
 * @param value 级联选择器的值
 */
const handleAreaChange = (value: CascaderValue) => {
  // 确保value是数组且长度为3
  if (Array.isArray(value) && value.length === 3) {
    const [provinceCode, cityCode, districtCode] = value as string[];
    personalForm.province = provinceCode || "";
    personalForm.city = cityCode || "";
    personalForm.district = districtCode || "";
  }
};

/**
 * 处理个人身份证正面上传
 * @param file 上传的文件
 */
const handleIdCardFrontUpload = async (file: File) => {
  try {
    // 验证文件
    if (!validateFile(file)) {
      return;
    }
    
    personalForm.idCardFront = file;
    const url = await uploadToOSS(file);
    personalForm.cardpicz = url;
    ElMessage.success("身份证正面上传成功");
  } catch (error) {
    ElMessage.error("身份证正面上传失败");
  }
};

/**
 * 处理个人身份证反面上传
 * @param file 上传的文件
 */
const handleIdCardBackUpload = async (file: File) => {
  try {
    // 验证文件
    if (!validateFile(file)) {
      return;
    }
    
    personalForm.idCardBack = file;
    const url = await uploadToOSS(file);
    personalForm.cardpicf = url;
    ElMessage.success("身份证反面上传成功");
  } catch (error) {
    ElMessage.error("身份证反面上传失败");
  }
};

/**
 * 处理保存操作
 */
const handleSave = async () => {
  try {
    // 验证个人认证表单
    if (!personalForm.cnname || !personalForm.cardnum) {
      ElMessage.warning("请填写完整的个人信息");
      return;
    }

    const submitData = {
      cnname: personalForm.cnname,
      cardnum: personalForm.cardnum,
      gerenyinhang: personalForm.gerenyinhang,
      gerenkahao: personalForm.gerenkahao,
      biaoqian: personalForm.biaoqian.join(","), // 转换为逗号分隔的字符串
      province: personalForm.province,
      city: personalForm.city,
      district: personalForm.district,
      cardpicz: personalForm.cardpicz,
      cardpicf: personalForm.cardpicf,
      status: 2, // 保存
      type: 1, // 个人认证
      member_id: userStore.userInfo?.id || 0,
    };

    emit('save', submitData);
  } catch (error) {
    console.error("保存失败:", error);
    ElMessage.error("保存失败");
  }
};

/**
 * 处理提交审核操作
 */
const handleSubmit = async () => {
  try {
    // 验证个人认证表单
    if (
      !personalForm.cnname ||
      !personalForm.cardnum ||
      !personalForm.cardpicz ||
      !personalForm.cardpicf
    ) {
      ElMessage.warning("请填写完整的个人信息并上传身份证件");
      return;
    }

    // 准备提交数据
    const submitData = {
      cnname: personalForm.cnname,
      cardnum: personalForm.cardnum,
      gerenyinhang: personalForm.gerenyinhang,
      gerenkahao: personalForm.gerenkahao,
      biaoqian: personalForm.biaoqian.join(","), // 转换为逗号分隔的字符串
      province: personalForm.province,
      city: personalForm.city,
      district: personalForm.district,
      cardpicz: personalForm.cardpicz,
      cardpicf: personalForm.cardpicf,
      status: 3, // 提交
      type: 1, // 个人认证
    };

    emit('submit', submitData);
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("提交失败");
  }
};

/**
 * 加载个人认证信息
 */
const loadPersonalCertificationInfo = async () => {
  try {
    const memberId = userStore.userInfo?.id;
    if (!memberId) {
      // console.warn('用户ID不存在'); 已移除
      return;
    }

    // 获取个人认证信息
    const personalResult = await userApi.getCertificationInfo({
      member_id: memberId,
      type: 1
    });

    if (personalResult.code === 1 && personalResult.data) {
      const data = personalResult.data;
      // 回显个人认证数据
      personalForm.cnname = data.cnname || '';
      personalForm.cardnum = data.cardnum || '';
      personalForm.gerenyinhang = data.gerenyinhang || '';
      personalForm.gerenkahao = data.gerenkahao || '';
      personalForm.biaoqian = data.biaoqian ? data.biaoqian.split(',') : [];
      personalForm.province = data.province || '';
      personalForm.city = data.city || '';
      personalForm.district = data.district || '';
      personalForm.cardpicz = data.cardpicz || '';
      personalForm.cardpicf = data.cardpicf || '';

      // 地区回显
      personalForm.selectedArea = [data.province.toString(), data.city.toString(), data.district.toString()];
    }
  } catch (error) {
    console.error("加载个人认证信息失败:", error);
  }
};

// 暴露方法给父组件
defineExpose({
  handleSave,
  handleSubmit
});

// 组件挂载时加载认证信息
onMounted(() => {
  loadPersonalCertificationInfo();
});
</script>

<style scoped lang="scss">
.personal-verification {
  .form-section {
    .form-item {
      padding: 20px 0;
      border-bottom: 1px solid;
      // 渐变
      border-image: linear-gradient(
        to right,
        rgba(221, 221, 221, 1),
        rgba(255, 255, 255, 1)
      );
      border-image-slice: 1;

      .form-label {
        width: 120px;
        font-size: 16px;
        color: #333;
        flex-shrink: 0;
        font-family: "PingFang Bold";
      }

      .form-value {
        margin-top: 10px;
        flex: 1;

        .form-input {
          width: 100%;
          border: none; // 输入框没有边框
          outline: none; // 去除聚焦时的边框
          font-size: 16px;
          color: #333;
          background: transparent;
          padding: 8px 0;

          &::placeholder {
            color: #999;
          }
        }

        .checkbox-group {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;

          .checkbox-item {
            margin-right: 0;
          }
        }

        .area-cascader {
          width: 100%;
        }

        .upload-section {
          .upload-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 16px;
            line-height: 1.5;
          }

          .upload-group {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
          }
        }
      }
    }
  }
}
</style>