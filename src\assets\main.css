@import './base.css';

#app {
  margin: 0 auto;
  /* font-weight: normal; */
  --el-color-primary: #004C66;
  --el-color-primary-light-3: #4d8ca8;
  --el-color-primary-light-5: #80b3cc;
  --el-color-primary-light-7: #b3d9e6;
  --el-color-primary-light-8: #d9ecf3;
  --el-color-primary-light-9: #ecf5f9;
  --el-color-primary-dark-2: #003d52;

  /* 自定义输入框和相关组件圆角 */
  --el-input-border-radius: 6px;
  --el-select-border-radius: 6px;
  --el-textarea-border-radius: 6px;
  --el-date-editor-border-radius: 6px;
      --el-border-radius-base: 6px;

  font-family: 'PingFang Medium' 'Microsoft YaHei';
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3PX;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

@media (min-width: 1024PX) {
  body {
    display: flex;
    place-items: center;
  }

  #app {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }
}
