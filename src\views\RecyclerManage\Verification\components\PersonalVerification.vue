<template>
  <div class="personal-verification">
    <div class="form-section">
      <!-- 真实姓名 -->
      <div class="form-item">
        <label class="form-label">真实姓名</label>
        <div class="form-value">
          <el-input 
            v-model="personalForm.name" 
            placeholder="请输入真实姓名" 
            class="form-input" 
          />
        </div>
      </div>

      <!-- 手机号 -->
      <div class="form-item">
        <label class="form-label">手机号</label>
        <div class="form-value">
          <el-input 
            v-model="personalForm.phone" 
            placeholder="请输入手机号" 
            class="form-input" 
          />
        </div>
      </div>

      <!-- 证件类型 -->
      <div class="form-item">
        <label class="form-label">证件类型</label>
        <div class="form-value">
          <el-radio-group v-model="personalForm.cartType">
            <el-radio :value="1">身份证</el-radio>
          </el-radio-group>
        </div>
      </div>

      <!-- 证件号 -->
      <div class="form-item">
        <label class="form-label">证件号</label>
        <div class="form-value">
          <el-input 
            v-model="personalForm.cartId" 
            placeholder="请输入证件号" 
            class="form-input" 
          />
        </div>
      </div>

      <!-- 身份证正反面照片 -->
      <div class="form-item">
        <label class="form-label">身份证正反面照片</label>
        <div class="form-value">
          <div class="upload-group">
            <div class="upload-item">
              <NewUploadCard
                upload-text="上传身份证正面"
                :background-image="idCardFrontBg"
                :image-url="personalForm.idCardFront"
                @upload-success="handleIdCardFrontUpload"
              />
              <span class="upload-label">身份证正面</span>
            </div>
            <div class="upload-item">
              <NewUploadCard
                upload-text="上传身份证反面"
                :background-image="idCardBackBg"
                :image-url="personalForm.idCardBack"
                @upload-success="handleIdCardBackUpload"
              />
              <span class="upload-label">身份证反面</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import NewUploadCard from '@/components/NewUploadCard.vue'
import { systemApi } from '@/utils/api-new'

// 定义组件事件接口
interface Emits {
  (e: 'save', data: any): void
  (e: 'submit', data: any): void
}

// 定义组件事件
const emit = defineEmits<Emits>()

// 个人认证表单数据
const personalForm = reactive({
  name: '',
  phone: '',
  cartType: 1,
  cartId: '',
  idCardFront: '', // 身份证正面图片URL
  idCardBack: '', // 身份证反面图片URL
  review: 1,
})

// 背景图片
const idCardFrontBg = ref('@/assets/icons/upload/idcard-front.svg')
const idCardBackBg = ref('@/assets/icons/upload/idcard-reverse.svg')

// 加载状态
const loading = ref(false)

/**
 * 处理身份证正面上传成功
 */
const handleIdCardFrontUpload = (data: { url: string; file: File }) => {
  personalForm.idCardFront = data.url
  console.log('身份证正面上传成功:', data.url)
}

/**
 * 处理身份证反面上传成功
 */
const handleIdCardBackUpload = (data: { url: string; file: File }) => {
  personalForm.idCardBack = data.url
  console.log('身份证反面上传成功:', data.url)
}

/**
 * 验证表单数据
 */
const validateForm = (): boolean => {
  if (!personalForm.name.trim()) {
    ElMessage.error('请输入真实姓名')
    return false
  }

  if (!personalForm.phone.trim()) {
    ElMessage.error('请输入手机号')
    return false
  }

  // 验证手机号格式
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(personalForm.phone.replace(/\s/g, ''))) {
    ElMessage.error('请输入正确的手机号格式')
    return false
  }

  if (!personalForm.cartId.trim()) {
    ElMessage.error('请输入证件号')
    return false
  }

  // 验证身份证号格式
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  if (!idCardRegex.test(personalForm.cartId)) {
    ElMessage.error('请输入正确的身份证号格式')
    return false
  }

  if (!personalForm.idCardFront) {
    ElMessage.error('请上传身份证正面照片')
    return false
  }

  if (!personalForm.idCardBack) {
    ElMessage.error('请上传身份证反面照片')
    return false
  }

  return true
}

/**
 * 构建提交数据
 */
const buildSubmitData = () => {
  // 构建附件列表
  const attachmentList = []
  
  if (personalForm.idCardFront) {
    attachmentList.push({
      bizType: 'SFZ',
      fileName: '身份证正面.jpg',
      filePath: personalForm.idCardFront,
      fileSize: '0',
      fileType: 'image'
    })
  }
  
  if (personalForm.idCardBack) {
    attachmentList.push({
      bizType: 'SFZ',
      fileName: '身份证反面.jpg',
      filePath: personalForm.idCardBack,
      fileSize: '0',
      fileType: 'image'
    })
  }

  return {
    name: personalForm.name.trim(),
    phone: personalForm.phone.trim(),
    cartType: personalForm.cartType,
    cartId: personalForm.cartId.trim(),
    review: personalForm.review,
    attachmentList
  }
}

/**
 * 处理保存操作
 */
const handleSave = () => {
  if (!validateForm()) return
  
  const submitData = buildSubmitData()
  emit('save', submitData)
}

/**
 * 处理提交审核操作
 */
const handleSubmit = () => {
  if (!validateForm()) return
  
  const submitData = buildSubmitData()
  emit('submit', submitData)
}

/**
 * 加载个人认证数据
 */
const loadPersonalAuthData = async (userId: string) => {
  loading.value = true
  
  try {
    const response = await systemApi.getPersonalAuth(userId)
    
    if (response && response.success && response.result) {
      const authData = response.result.hgyPersonalAuth
      const attachmentList = response.result.hgyAttachmentList
      
      if (authData) {
        Object.assign(personalForm, {
          name: authData.name || '',
          phone: authData.phone || '',
          cartType: authData.cartType || 1,
          cartId: authData.cartId || '',
          review: 1
        })
      }
      
      // 处理附件数据
      if (attachmentList && attachmentList.length > 0) {
        attachmentList.forEach((item: any) => {
          if (item.bizType === 'SFZ') {
            if (item.fileName.includes('正面') || attachmentList.indexOf(item) === 0) {
              personalForm.idCardFront = item.filePath
            } else {
              personalForm.idCardBack = item.filePath
            }
          }
        })
      }
    }
  } catch (error) {
    console.error('加载个人认证数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 暴露方法给父组件调用
defineExpose({
  handleSave,
  handleSubmit,
  loadPersonalAuthData
})

onMounted(() => {
  // 组件挂载时可以加载数据
})
</script>

<style scoped lang="scss">
.personal-verification {
  .form-section {
    .form-item {
      padding: 20px 0;
      border-bottom: 1px solid;
      border-image: linear-gradient(to right, rgba(221, 221, 221, 1), rgba(255, 255, 255, 1));
      border-image-slice: 1;

      .form-label {
        font-size: 16px;
        color: #333;
        flex-shrink: 0;
        font-family: 'PingFang Bold';
        margin-bottom: 10px;
        display: block;
      }

      .form-value {
        flex: 1;

        .form-input {
          width: 100%;
          
          :deep(.el-input__wrapper) {
            border: none;
            box-shadow: none;
            background: transparent;
            padding: 0;
            
            .el-input__inner {
              font-size: 16px;
              color: #666;
              background: transparent;
              
              &::placeholder {
                color: #999;
                font-size: 16px;
              }
            }
          }
        }

        :deep(.el-radio-group) {
          .el-radio {
            color: #666;
            font-size: 16px;
            
            .el-radio__label {
              color: #666;
            }
          }
        }

        .upload-group {
          display: flex;
          gap: 20px;
          flex-wrap: wrap;

          .upload-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;

            .upload-label {
              font-size: 12px;
              color: #666;
              text-align: center;
            }
          }
        }
      }
    }
  }
}
</style>
