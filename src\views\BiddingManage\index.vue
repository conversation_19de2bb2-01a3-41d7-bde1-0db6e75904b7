<template>
  <div class="bidding-manage-container">
    <!-- 左侧导航栏 -->
    <div class="bidding-sidebar">
      <!-- 标题 -->
      <div class="sidebar-title">竞拍管理</div>
      <!-- 分割线 -->
      <div class="sidebar-divider"></div>
      <!-- 导航菜单 -->
      <div class="sidebar-nav">
        <div
          v-for="item in navItems"
          :key="item.key"
          class="nav-item"
          :class="{ active: activeNav === item.key }"
          @click="handleNavClick(item.key)"
        >
          <SvgIcon :iconName="item.icon" class="nav-icon" />
          <span class="nav-text">{{ item.label }}</span>
        </div>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="bidding-content">
      <!-- 我的收藏页面 -->
      <MyFavorites v-if="activeNav === 'favorites'" />
      <!-- 我的报名页面 -->
      <MyRegistrations v-if="activeNav === 'registrations'" />
      <!-- 参与标的页面 -->
      <MyParticipations v-if="activeNav === 'participations'" />
      <!-- 竞得标的页面 -->
      <MyWonAuctions v-if="activeNav === 'won'" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SvgIcon from '@/components/SvgIcon.vue'
import MyFavorites from './components/MyFavorites.vue'
import MyRegistrations from './components/MyRegistrations.vue'
import MyParticipations from './components/MyParticipations.vue'
import MyWonAuctions from './components/MyWonAuctions.vue'

// 当前激活的导航项
const activeNav = ref('favorites')

// 导航菜单配置
const navItems = [
  { key: 'favorites', label: '我的收藏', icon: 'login-data-attention' },
  { key: 'registrations', label: '我的报名', icon: 'login-data-real-name' },
  { key: 'participations', label: '参与标的', icon: 'login-data-security' },
  { key: 'won', label: '竞得标的', icon: 'login-data-personal' }
]

/**
 * 处理导航点击事件
 * @param key 导航项的key
 */
const handleNavClick = (key: string) => {
  activeNav.value = key
}
</script>

<style scoped lang="scss">
.bidding-manage-container {
  display: flex;
  gap: 10px;
  padding: 20px;
  min-height: calc(100vh - 80px); // 减去header高度
  background-color: #f5f5f5;
  max-width: 1280px; // 限制最大宽度，避免在大屏幕上过度拉伸
  margin: 0 auto; // 居中显示
  width: 100%; // 确保在小屏幕上能够充分利用空间
}

// 左侧导航栏样式
.bidding-sidebar {
  width: 240px;
  background-color: #ffffff;
  border-radius: 10px;
  padding: 20px;
  height: fit-content;

  .sidebar-title {
    font-size: 18px;
    font-weight: 600;
    color: #333333;
    margin-bottom: 16px;
  }

  .sidebar-divider {
    height: 1px;
    background-color: #e5e5e5;
    margin-bottom: 20px;
  }

  .sidebar-nav {
    .nav-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .nav-icon {
        width: 20px;
        height: 20px;
        margin-right: 12px;
        color: #999999;
        transition: color 0.3s ease;
      }

      .nav-text {
        font-size: 16px;
        color: #999999;
        transition: color 0.3s ease;
      }

      &:hover {
        background-color: rgba(0, 76, 102, 0.05);
      }

      &.active {
        background-color: rgba(0, 76, 102, 0.1);

        .nav-icon,
        .nav-text {
          color: #004C66;
        }
      }
    }
  }
}

// 右侧内容区域样式
.bidding-content {
  flex: 1;
  background-color: #ffffff;
  border-radius: 10px;
  min-height: 600px;
}
</style>