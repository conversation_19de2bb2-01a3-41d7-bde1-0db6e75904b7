<template>
  <div class="supply-demand-detail">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>

    <!-- 详情内容 -->
    <div v-else class="detail-container">
      <!-- 基本信息展示 -->
      <div class="info-section">
        <span class="section-title">基本信息</span>
        <div class="info-grid-four">
          <div class="info-item">
            <span class="label">{{ serviceType === 4 ? '供应' : '求购' }}单号：</span>
            <span class="value">{{ detailData?.id || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">{{ serviceType === 4 ? '供应' : '求购' }}标题：</span>
            <span class="value">{{ detailData?.infoTitle || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">物资类型：</span>
            <span class="value">{{ detailData?.materialTypeName || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">联系人：</span>
            <span class="value">{{ detailData?.relationUser || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">联系电话：</span>
            <span class="value">{{ detailData?.relationPhone || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ formatDateTime(detailData?.createTime) }}</span>
          </div>
        </div>
      </div>

      <!-- 详细信息 -->
      <div class="detail-section">
        <span class="section-title">{{ serviceType === 4 ? '供应' : '求购' }}详情</span>
        <div class="detail-content scrollable-content">
          <div class="detail-grid">
            <div class="detail-group">
              <div class="info-grid">
                <div class="info-item">
                  <span class="label">物资品牌：</span>
                  <span class="value">{{ detailData?.brand || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">物资型号：</span>
                  <span class="value">{{ detailData?.model || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">新旧程度：</span>
                  <span class="value">{{ getDepreciationText(detailData?.depreciationDegree) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">物资数量：</span>
                  <span class="value">{{ detailData?.quantity || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">物资单位：</span>
                  <span class="value">{{ detailData?.unit || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">物资价格：</span>
                  <span class="value">¥{{ formatPrice(detailData?.price) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">存放方式：</span>
                  <span class="value">{{ getStorageMethodText(detailData?.storageMethod) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">所在地区：</span>
                  <span class="value">{{ getFullAddress(detailData) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">有效期：</span>
                  <span class="value">{{ formatDateTime(detailData?.validDate) }}</span>
                </div>
              </div>

              <!-- 亮点描述 -->
              <div v-if="detailData?.highlights" class="info-item">
                <span class="label">{{ serviceType === 4 ? '供应' : '求购' }}亮点：</span>
                <span class="value">{{ detailData.highlights }}</span>
              </div>

              <!-- 物资详细描述 -->
              <div v-if="detailData?.materialDesc" class="info-item full-width">
                <span class="label">物资详细描述：</span>
                <div class="rich-content" v-html="detailData.materialDesc"></div>
              </div>
            </div>

            <!-- 附件信息 -->
            <div v-if="detailData?.attachmentList && detailData.attachmentList.length > 0" class="detail-group">
              <h4 class="group-title">附件信息</h4>
              <div class="attachment-list">
                <div v-for="attachment in detailData.attachmentList" :key="attachment.id" class="attachment-item">
                  <div class="attachment-info">
                    <span class="file-name">{{ attachment.fileName }}</span>
                    <span class="file-size">{{ formatFileSize(attachment.fileSize) }}</span>
                    <el-button type="primary" link size="small" @click="previewFile(attachment)">预览</el-button>
                    <el-button type="primary" link size="small" @click="downloadAttachment(attachment)">下载</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 状态信息 -->
      <div v-if="detailData?.status" class="status-section">
        <span class="section-title">状态信息</span>
        <div class="status-info">
          <div class="status-result">
            <span class="label">当前状态：</span>
            <el-tag :type="getStatusTagType(detailData.status)">
              {{ getStatusText(detailData.status) }}
            </el-tag>
          </div>
          <div v-if="detailData?.auditOpinion" class="audit-opinion">
            <span class="label">审核意见：</span>
            <span class="content">{{ detailData.auditOpinion }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { supplyDemandApi } from '@/utils/api-new'

interface Props {
  recordId: string
  serviceType: number // 4-供应 5-求购
}

interface Emits {
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const detailData = ref<any>(null)

// 获取详情数据
async function fetchDetailData() {
  if (!props.recordId) {
    console.error('缺少recordId')
    return
  }

  loading.value = true
  try {
    // 调用API获取详细数据
    const response = await supplyDemandApi.querySupplyDemandById({ id: props.recordId })
    
    if (response.success && response.result) {
      detailData.value = response.result
    } else {
      ElMessage.error(response.message || '获取详情失败')
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 格式化日期时间
function formatDateTime(dateTime: string | null | undefined) {
  if (!dateTime) return '-'
  try {
    const date = new Date(dateTime)
    if (isNaN(date.getTime())) return '-'
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (error) {
    return '-'
  }
}

// 格式化价格
function formatPrice(price: number) {
  if (!price && price !== 0) return '-'
  return price.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

// 获取新旧程度文本
function getDepreciationText(degree: number) {
  if (!degree) return '-'
  const degreeMap: Record<number, string> = {
    1: '一成新',
    2: '二成新',
    3: '三成新',
    4: '四成新',
    5: '五成新',
    6: '六成新',
    7: '七成新',
    8: '八成新',
    9: '九成新'
  }
  return degreeMap[degree] || '未知'
}

// 获取存放方式文本
function getStorageMethodText(method: number) {
  if (!method) return '-'
  const methodMap: Record<number, string> = {
    1: '仓库',
    2: '露天堆放',
    3: '集装箱',
    4: '其他'
  }
  return methodMap[method] || '未知'
}

// 获取完整地址
function getFullAddress(data: any) {
  if (!data) return '-'
  
  const province = data.provinceName || ''
  const city = data.cityName || ''
  const district = data.districtName || ''
  const address = data.address || ''
  
  const parts = [province, city, district, address].filter(Boolean)
  return parts.length > 0 ? parts.join(' ') : '-'
}

// 获取状态文本
function getStatusText(status: number): string {
  switch (status) {
    case 1: return '草稿'
    case 2: return '待审核'
    case 3: return '审核通过'
    case 4: return '审核拒绝'
    case 5: return '已发布'
    case 6: return '已成交'
    case 7: return '已撤拍'
    case 8: return '已过期'
    default: return '未知'
  }
}

// 获取状态标签类型
function getStatusTagType(status: number): string {
  switch (status) {
    case 1: return 'info'      // 草稿
    case 2: return 'warning'   // 待审核
    case 3: return 'success'   // 审核通过
    case 4: return 'danger'    // 审核拒绝
    case 5: return 'success'   // 已发布
    case 6: return 'success'   // 已成交
    case 7: return 'info'      // 已撤拍
    case 8: return 'info'      // 已过期
    default: return 'info'
  }
}

// 格式化文件大小
function formatFileSize(size: number) {
  if (!size) return '-'
  if (size < 1024) return size + 'B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB'
  if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + 'MB'
  return (size / (1024 * 1024 * 1024)).toFixed(1) + 'GB'
}

// 预览文件
function previewFile(attachment: any) {
  if (!attachment.filePath) {
    ElMessage.warning('附件路径不存在')
    return
  }
  // 简单的预览实现，在新窗口打开
  window.open(attachment.filePath, '_blank')
}

// 下载附件
function downloadAttachment(attachment: any) {
  if (!attachment.filePath) {
    ElMessage.warning('附件路径不存在')
    return
  }
  
  // 创建下载链接
  const link = document.createElement('a')
  link.href = attachment.filePath
  link.download = attachment.fileName || '附件'
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

onMounted(() => {
  fetchDetailData()
})
</script>

<style lang="scss" scoped>
.supply-demand-detail {
  padding: 20px;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 0;
    color: #666;
    gap: 12px;
    
    .el-icon {
      font-size: 24px;
    }
  }

  .detail-container {
    .info-section,
    .detail-section,
    .status-section {
      margin-bottom: 20px;

      .section-title {
        display: flex;
        align-items: center;
        font-size: 16px;
        color: #262626;
        margin-bottom: 10px;
        padding-bottom: 8px;
        font-weight: 600;
        
        &::before {
          content: '';
          display: block;
          width: 4px;
          height: 20px;
          background-color: #004c66;
          margin-right: 10px;
        }
      }
    }

    .info-grid-four {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 10px;
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 6px;
      border: 1px solid #e9ecef;

      @media (max-width: 1200px) {
        grid-template-columns: repeat(3, 1fr);
      }

      @media (max-width: 768px) {
        grid-template-columns: repeat(2, 1fr);
      }

      @media (max-width: 480px) {
        grid-template-columns: 1fr;
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 10px;

      @media (max-width: 768px) {
        grid-template-columns: repeat(2, 1fr);
      }

      @media (max-width: 480px) {
        grid-template-columns: 1fr;
      }
    }

    .info-item {
      display: flex;
      align-items: flex-start;
      min-height: 32px;

      &.full-width {
        grid-column: 1 / -1;
        flex-direction: column;
        align-items: stretch;

        .label {
          margin-bottom: 8px;
        }

        .value,
        .rich-content {
          flex: 1;
          line-height: 1.6;
        }

        .rich-content {
          background-color: #fff;
          border-radius: 6px;
          border: 1px solid #e8e8e8;
          padding: 10px;
          min-height: 100px;

          :deep(p) {
            margin-bottom: 8px;
            &:last-child {
              margin-bottom: 0;
            }
          }

          :deep(img) {
            max-width: 100%;
            height: auto;
          }
        }
      }

      .label {
        font-weight: 500;
        color: #595959;
        white-space: nowrap;
        margin-right: 12px;
        min-width: 80px;
      }

      .value {
        flex: 1;
        color: #262626;
        word-break: break-all;
      }
    }

    .detail-content {
      .detail-grid {
        display: flex;
        flex-direction: column;
        gap: 24px;
      }

      .detail-group {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 6px;
        border: 1px solid #e9ecef;
        
        .group-title {
          font-size: 14px;
          font-weight: 600;
          color: #1890ff;
          margin-bottom: 16px;
          padding-left: 8px;
          border-left: 3px solid #1890ff;
        }
      }
    }

    .scrollable-content {
      max-height: 400px;
      min-height: 200px;
      overflow-y: auto;
      padding-right: 8px;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a8a8a8;
        }
      }
    }

    .attachment-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 12px;

      .attachment-item {
        padding: 12px;
        background-color: #fafafa;
        border-radius: 6px;
        border: 1px solid #d9d9d9;

        .attachment-info {
          display: flex;
          align-items: center;
          gap: 8px;

          .file-name {
            flex: 1;
            font-weight: 500;
            color: #262626;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .file-size {
            color: #8c8c8c;
            font-size: 12px;
            white-space: nowrap;
          }
        }
      }
    }

    .status-section {
      .status-info {
        padding: 16px;
        background-color: #f6f8fa;
        border-radius: 6px;
        border: 1px solid #e1e4e8;

        .status-result {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          .label {
            font-weight: 500;
            color: #595959;
            margin-right: 12px;
          }
        }

        .audit-opinion {
          .label {
            font-weight: 500;
            color: #595959;
            margin-right: 12px;
          }

          .content {
            color: #262626;
            line-height: 1.6;
          }
        }
      }
    }
  }
}
</style>
