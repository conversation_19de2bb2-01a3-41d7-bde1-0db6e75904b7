// 拍卖相关的类型定义

// 拍卖商品数据类型
export interface PropertyItem {
  productId: string;
  productName: string;
  productImage: string;
  currentPrice: number;
  priceUnit: string;
  viewCount: number;
  enterpriseLogo: string;
  enterpriseName: string;
  enterpriseType: string;
  status: string;
  statusName: string;
  productCount: number;
  productCountUnit: string;
  productWeight: number;
  productWeightUnit: string;
  productPower?: number;
  productPowerUnit?: string;
}