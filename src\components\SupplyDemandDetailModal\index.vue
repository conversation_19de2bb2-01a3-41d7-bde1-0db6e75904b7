<template>
  <Modal
    v-model="visible"
    :title="modalTitle"
    :width="1200"
    top="8vh"
    :show-footer="false"
    :destroy-on-close="true"
    @close="handleClose"
    @cancel="handleClose"
  >
    <SupplyDemandDetail
      v-if="recordId"
      :record-id="recordId"
      :service-type="serviceType"
      @close="handleClose"
    />
  </Modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import Modal from '@/components/Modal.vue'
import SupplyDemandDetail from '@/components/SupplyDemandDetail/index.vue'

interface Props {
  modelValue: boolean
  recordId: string | null
  serviceType: number // 4-供应 5-求购
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)

// 计算弹窗标题
const modalTitle = computed(() => {
  const typeText = props.serviceType === 4 ? '供应' : '求购'
  return `${typeText}信息详情`
})

// 监听外部传入的modelValue状态
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal
  },
  { immediate: true }
)

// 监听内部visible状态变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 关闭弹窗
function handleClose() {
  visible.value = false
  emit('close')
}
</script>

<style lang="scss" scoped>
:deep(.modal-content) {
  padding: 24px;
  max-height: 80vh;
  overflow-y: auto;
}
</style>
