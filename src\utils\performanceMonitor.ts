/**
 * 性能监控工具
 * 用于监测首屏加载性能、资源加载情况和用户体验指标
 */

// 性能指标接口
interface PerformanceMetrics {
  // 首屏加载时间
  firstContentfulPaint: number;
  // 最大内容绘制时间
  largestContentfulPaint: number;
  // 首次输入延迟
  firstInputDelay: number;
  // 累积布局偏移
  cumulativeLayoutShift: number;
  // DOM内容加载完成时间
  domContentLoaded: number;
  // 页面完全加载时间
  loadComplete: number;
  // 资源加载时间
  resourceLoadTimes: ResourceLoadTime[];
}

// 资源加载时间接口
interface ResourceLoadTime {
  name: string;
  type: string;
  startTime: number;
  duration: number;
  size?: number;
}

// 性能阈值配置
const PERFORMANCE_THRESHOLDS = {
  // 首屏内容绘制时间阈值（毫秒）
  FCP_THRESHOLD: 1800,
  // 最大内容绘制时间阈值（毫秒）
  LCP_THRESHOLD: 2500,
  // 首次输入延迟阈值（毫秒）
  FID_THRESHOLD: 100,
  // 累积布局偏移阈值
  CLS_THRESHOLD: 0.1,
  // 资源加载时间阈值（毫秒）
  RESOURCE_THRESHOLD: 3000
};

/**
 * 性能监控器类
 */
export class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {};
  private observers: PerformanceObserver[] = [];
  private startTime: number;

  constructor() {
    this.startTime = performance.now();
    this.initObservers();
    this.monitorPageLoad();
  }

  /**
   * 初始化性能观察器
   */
  private initObservers(): void {
    // 监控绘制性能
    if ('PerformanceObserver' in window) {
      this.observePaintMetrics();
      this.observeLayoutShift();
      this.observeResourceTiming();
    }
  }

  /**
   * 监控绘制性能指标
   */
  private observePaintMetrics(): void {
    try {
      const paintObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.name === 'first-contentful-paint') {
            this.metrics.firstContentfulPaint = entry.startTime;
            // console.log(`🎨 首屏内容绘制时间: ${entry.startTime.toFixed(2)}ms`); 已移除
            this.checkThreshold('FCP', entry.startTime, PERFORMANCE_THRESHOLDS.FCP_THRESHOLD);
          }
        }
      });
      
      paintObserver.observe({ entryTypes: ['paint'] });
      this.observers.push(paintObserver);
    } catch (error) {
      // console.warn('绘制性能监控初始化失败:', error); 已移除
    }

    // 监控最大内容绘制
    try {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.metrics.largestContentfulPaint = lastEntry.startTime;
        // console.log(`🖼️ 最大内容绘制时间: ${lastEntry.startTime.toFixed(2)}ms`); 已移除
        this.checkThreshold('LCP', lastEntry.startTime, PERFORMANCE_THRESHOLDS.LCP_THRESHOLD);
      });
      
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.push(lcpObserver);
    } catch (error) {
      // console.warn('LCP监控初始化失败:', error); 已移除
    }
  }

  /**
   * 监控布局偏移
   */
  private observeLayoutShift(): void {
    try {
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        }
        this.metrics.cumulativeLayoutShift = clsValue;
        // console.log(`📐 累积布局偏移: ${clsValue.toFixed(4)}`); 已移除
        this.checkThreshold('CLS', clsValue, PERFORMANCE_THRESHOLDS.CLS_THRESHOLD);
      });
      
      clsObserver.observe({ entryTypes: ['layout-shift'] });
      this.observers.push(clsObserver);
    } catch (error) {
      // console.warn('布局偏移监控初始化失败:', error); 已移除
    }
  }

  /**
   * 监控资源加载时间
   */
  private observeResourceTiming(): void {
    try {
      const resourceObserver = new PerformanceObserver((list) => {
        const resources: ResourceLoadTime[] = [];
        
        for (const entry of list.getEntries()) {
          const resource: ResourceLoadTime = {
            name: entry.name,
            type: this.getResourceType(entry.name),
            startTime: entry.startTime,
            duration: entry.duration,
            size: (entry as any).transferSize || 0
          };
          
          resources.push(resource);
          
          // 检查慢资源
          if (entry.duration > PERFORMANCE_THRESHOLDS.RESOURCE_THRESHOLD) {
            // console.warn(`🐌 慢资源检测: ${entry.name} 加载时间 ${entry.duration.toFixed(2)}ms`); 已移除
          }
        }
        
        this.metrics.resourceLoadTimes = [
          ...(this.metrics.resourceLoadTimes || []),
          ...resources
        ];
      });
      
      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.push(resourceObserver);
    } catch (error) {
      // console.warn('资源时间监控初始化失败:', error); 已移除
    }
  }

  /**
   * 监控页面加载事件
   */
  private monitorPageLoad(): void {
    // DOM内容加载完成
    document.addEventListener('DOMContentLoaded', () => {
      this.metrics.domContentLoaded = performance.now() - this.startTime;
      // console.log(`📄 DOM内容加载完成: ${this.metrics.domContentLoaded.toFixed(2)}ms`); 已移除
    });

    // 页面完全加载
    window.addEventListener('load', () => {
      this.metrics.loadComplete = performance.now() - this.startTime;
      // console.log(`✅ 页面完全加载: ${this.metrics.loadComplete.toFixed(2)}ms`); 已移除
      
      // 延迟生成性能报告
      setTimeout(() => {
        this.generatePerformanceReport();
      }, 1000);
    });

    // 监控首次输入延迟
    this.monitorFirstInputDelay();
  }

  /**
   * 监控首次输入延迟
   */
  private monitorFirstInputDelay(): void {
    try {
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.metrics.firstInputDelay = (entry as any).processingStart - entry.startTime;
          // console.log(`⌨️ 首次输入延迟: ${this.metrics.firstInputDelay.toFixed(2)}ms`); 已移除
          this.checkThreshold('FID', this.metrics.firstInputDelay, PERFORMANCE_THRESHOLDS.FID_THRESHOLD);
        }
      });
      
      fidObserver.observe({ entryTypes: ['first-input'] });
      this.observers.push(fidObserver);
    } catch (error) {
      // console.warn('首次输入延迟监控初始化失败:', error); 已移除
    }
  }

  /**
   * 获取资源类型
   */
  private getResourceType(url: string): string {
    if (url.includes('.js')) return 'script';
    if (url.includes('.css')) return 'stylesheet';
    if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/i)) return 'image';
    if (url.match(/\.(woff|woff2|ttf|otf)$/i)) return 'font';
    return 'other';
  }

  /**
   * 检查性能阈值
   */
  private checkThreshold(metric: string, value: number, threshold: number): void {
    if (value > threshold) {
      // console.warn(`⚠️ ${metric} 超过阈值: ${value.toFixed(2)} > ${threshold}`); 已移除
    } else {
      // console.log(`✅ ${metric} 性能良好: ${value.toFixed(2)} <= ${threshold}`); 已移除
    }
  }

  /**
   * 生成性能报告
   */
  private generatePerformanceReport(): void {
    console.group('📊 性能监控报告');
    
    // 核心性能指标
    // console.log('🎯 核心性能指标:'); 已移除
    if (this.metrics.firstContentfulPaint) {
      // console.log(`  首屏内容绘制: ${this.metrics.firstContentfulPaint.toFixed(2)}ms`); 已移除
    }
    if (this.metrics.largestContentfulPaint) {
      // console.log(`  最大内容绘制: ${this.metrics.largestContentfulPaint.toFixed(2)}ms`); 已移除
    }
    if (this.metrics.firstInputDelay) {
      // console.log(`  首次输入延迟: ${this.metrics.firstInputDelay.toFixed(2)}ms`); 已移除
    }
    if (this.metrics.cumulativeLayoutShift) {
      // console.log(`  累积布局偏移: ${this.metrics.cumulativeLayoutShift.toFixed(4)}`); 已移除
    }
    
    // 页面加载时间
    // console.log('⏱️ 页面加载时间:'); 已移除
    if (this.metrics.domContentLoaded) {
      // console.log(`  DOM加载完成: ${this.metrics.domContentLoaded.toFixed(2)}ms`); 已移除
    }
    if (this.metrics.loadComplete) {
      // console.log(`  页面完全加载: ${this.metrics.loadComplete.toFixed(2)}ms`); 已移除
    }
    
    // 慢资源分析
    this.analyzeSlowResources();
    
    console.groupEnd();
  }

  /**
   * 分析慢资源
   */
  private analyzeSlowResources(): void {
    if (!this.metrics.resourceLoadTimes) return;
    
    const slowResources = this.metrics.resourceLoadTimes
      .filter(resource => resource.duration > PERFORMANCE_THRESHOLDS.RESOURCE_THRESHOLD)
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 5); // 取前5个最慢的资源
    
    if (slowResources.length > 0) {
      // console.log('🐌 慢资源分析 (前5个):'); 已移除
      slowResources.forEach((resource, index) => {
        // console.log(`  ${index + 1}. ${resource.name}`); 已移除
        // console.log(`     类型: ${resource.type}, 时间: ${resource.duration.toFixed(2)}ms, 大小: ${this.formatBytes(resource.size || 0)}`); 已移除
      });
    } else {
      // console.log('✅ 没有检测到慢资源'); 已移除
    }
  }

  /**
   * 格式化字节大小
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 获取性能指标
   */
  public getMetrics(): Partial<PerformanceMetrics> {
    return { ...this.metrics };
  }

  /**
   * 销毁监控器
   */
  public destroy(): void {
    this.observers.forEach(observer => {
      observer.disconnect();
    });
    this.observers = [];
  }
}

// 创建全局性能监控实例
let performanceMonitor: PerformanceMonitor | null = null;

/**
 * 初始化性能监控
 */
export function initPerformanceMonitor(): PerformanceMonitor {
  if (!performanceMonitor) {
    performanceMonitor = new PerformanceMonitor();
    // console.log('🚀 性能监控已启动'); 已移除
  }
  return performanceMonitor;
}

/**
 * 获取性能监控实例
 */
export function getPerformanceMonitor(): PerformanceMonitor | null {
  return performanceMonitor;
}

/**
 * 销毁性能监控
 */
export function destroyPerformanceMonitor(): void {
  if (performanceMonitor) {
    performanceMonitor.destroy();
    performanceMonitor = null;
    // console.log('🛑 性能监控已停止'); 已移除
  }
}

/**
 * 手动记录自定义性能指标
 */
export function recordCustomMetric(name: string, value: number): void {
  // console.log(`📈 自定义指标 ${name}: ${value}`); 已移除
  
  // 如果支持，使用Performance API记录
  if ('performance' in window && 'mark' in performance) {
    performance.mark(`custom-${name}-${value}`);
  }
}

/**
 * 测量代码执行时间
 */
export function measureExecutionTime<T>(name: string, fn: () => T): T {
  const startTime = performance.now();
  const result = fn();
  const endTime = performance.now();
  const duration = endTime - startTime;
  
  // console.log(`⏱️ ${name} 执行时间: ${duration.toFixed(2)}ms`); 已移除
  recordCustomMetric(name, duration);
  
  return result;
}

/**
 * 异步测量代码执行时间
 */
export async function measureAsyncExecutionTime<T>(name: string, fn: () => Promise<T>): Promise<T> {
  const startTime = performance.now();
  const result = await fn();
  const endTime = performance.now();
  const duration = endTime - startTime;
  
  // console.log(`⏱️ ${name} 异步执行时间: ${duration.toFixed(2)}ms`); 已移除
  recordCustomMetric(name, duration);
  
  return result;
}