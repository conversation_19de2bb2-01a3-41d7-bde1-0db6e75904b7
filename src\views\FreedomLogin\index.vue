<template>
  <div class="login-container">
    <!-- 左侧3D logo区域 -->
    <div class="login-left">
      <!-- 使用提供的3D SVG图标 -->
      <div class="logo-3d-wrapper">
        <img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/1_1754963030315.png" alt="" class="logo-3d-image-1">
        <img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/2_1754963119472.png" alt="" class="logo-3d-image-2">
        <img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/3_1754963135630.png" alt="" class="logo-3d-image-3">
      </div>
    </div>

    <!-- 右侧登录表单区域 -->
    <div class="login-right">
      <div class="login-form-container">
        <!-- logo图标 -->
        <div class="form-logo">
          <SvgIcon iconName="login-logo" className="logo-icon" />
        </div>

        <!-- 欢迎标题 -->
        <h2 class="welcome-title">回收商登录</h2>
        <p class="login-subtitle">请使用回收商账号登录</p>

        <!-- 动态组件渲染 -->
        <component 
          :is="currentComponent" 
          @login-success="handleLoginSuccess"
        />

        <!-- 返回主登录链接 -->
        <div class="back-link">
          <span>返回</span>
          <a href="#" @click.prevent="backToMainLogin">主站登录</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import SvgIcon from '@/components/SvgIcon.vue'

// 导入回收商登录组件
import FreedomPasswordLogin from './components/FreedomPasswordLogin.vue'

// 路由实例
const router = useRouter()
const route = useRoute()

// 当前组件（回收商只有密码登录）
const currentComponent = computed(() => {
  return FreedomPasswordLogin
})

/**
 * 处理登录成功
 */
const handleLoginSuccess = () => {
  // 获取返回地址
  const returnUrl = route.query.returnUrl as string
  
  if (returnUrl) {
    // 如果有返回地址，跳转到返回地址
    router.push(returnUrl)
  } else {
    // 否则跳转到回收商首页
    router.push('/freedom/freedomHome')
  }
}

/**
 * 返回主登录
 */
const backToMainLogin = () => {
  router.push('/login')
}
</script>

<style lang="scss" scoped>
.login-container {
  display: flex;
  position: relative;
  overflow: hidden;
  height: 885px;
  // 引入login-bg.png
  background: url("https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/login-bg_1754963733964.jpg") no-repeat;
  background-size: cover;
  background-position: center;
  &::-webkit-scrollbar {
    display: none;
  }
}

// 左侧3D logo区域
.login-left {
  flex: 1.6;
  display: flex;
  align-items: center;
  justify-content: center;
  .logo-3d-wrapper {
    width: 680px;
    height: 601.18px;
    position: relative;
    color: #fff;
    .logo-3d-image-1 {
      width: 201.6px;
      height: 325.51px;
      position: absolute;
      top: 110px;
      left: 420px;
      z-index: 2;
      // 第一个图标：简单上下浮动动画，3秒周期
      animation: float3d 3s ease-in-out infinite;
    }
    .logo-3d-image-2 {
      position: absolute;
      top: -9px;
      left: 110px;
      width: 379.39px;
      height: 504.01px;
      z-index: 1;
      // 第二个图标：上下浮动动画，4秒周期，延迟1秒开始（错开效果）
      animation: floatSecond 3s ease-in-out infinite 1s;
    }
    .logo-3d-image-3 {
      position: absolute;
      bottom: 9px;
      left: -30px;
      width: 680px;
      height: 378.6px;
      // 第三个图标：圆盘旋转动画，8秒周期
      // animation: rotateCircle 8s linear infinite;
    }

    // 响应式
    @media (max-width: 768px) {
     display: none;
    }
  }
}

// 简单上下浮动动画 - 第一个图标
@keyframes float3d {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

// 上下浮动动画 - 第二个图标
@keyframes floatSecond {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

// 右侧登录表单区域
.login-right {
  flex: 1.16;
  display: flex;
  position: relative;
  z-index: 1;
  padding-top: 163px;

  .login-form-container {
    width: 100%;
    max-width: 445px;

    .form-logo {
      margin-bottom: 21px;
      .logo-icon {
        width: 57px;
        height: 58px;
      }
    }

    .welcome-title {
      font-size: 30px;
      font-weight: 600;
      color: #004c66;
      margin-bottom: 10px;
      line-height: 1;
    }

    .login-subtitle {
      font-size: 14px;
      color: #666;
      margin-bottom: 45px;
      line-height: 1;
    }
  }
}

.back-link {
  text-align: center;
  margin-top: 24px;
  color: #666;
  font-size: 14px;

  a {
    color: #004c66;
    text-decoration: none;
    margin-left: 4px;

    &:hover {
      text-decoration: underline;
      background-color: transparent;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .login-left {
    padding: 40px;
  }

  .login-right {
    width: 420px;
    padding: 40px;
  }
}

@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
  }

  .login-left {
    flex: none;
    height: 300px;
    padding: 20px;
  }

  .login-right {
    width: 100%;
    padding: 30px 20px;
  }
}
</style>