<template>
  <!-- 密码登录表单 -->
  <el-form
    ref="passwordFormRef"
    :model="passwordForm"
    :rules="passwordRules"
    :validate-on-rule-change="false"
    class="login-form"
  >
    <!-- 用户类型选择 -->
    <!-- <el-form-item prop="userType">
      <el-select
        v-model="passwordForm.userType"
        placeholder="请选择登录身份"
        class="user-type-select"
        size="large"
      >
        <el-option label="竞买人" value="personal" />
        <el-option label="处置企业" value="enterprise" />
      </el-select>
    </el-form-item> -->

    <!-- 用户名输入框 -->
    <el-form-item prop="username">
      <el-input
        v-model="passwordForm.username"
        placeholder="请输入手机号"
        size="large"
        class="login-input"
      />
    </el-form-item>

    <!-- 密码输入框 -->
    <el-form-item prop="password" class="password">
      <el-input
        v-model="passwordForm.password"
        type="password"
        placeholder="请输入登录密码"
        size="large"
        class="login-input"
        show-password
      />
    </el-form-item>

    <!-- 记住密码和忘记密码 -->
    <el-form-item class="form-options">
      <el-checkbox v-model="passwordForm.rememberMe" class="remember-checkbox">
        记住密码
      </el-checkbox>
      <div class="forgot-password-link">
        <a href="#" @click.prevent="handleForgotPassword">忘记密码</a>
      </div>
    </el-form-item>

    <!-- 登录按钮 -->
    <el-form-item>
      <el-button
        type="primary"
        size="large"
        class="login-button"
        :loading="loginLoading"
        @click="handleLogin"
      >
        登录
      </el-button>
    </el-form-item>

    <!-- 注册链接 -->
    <div class="register-link">
      <span>还没有账号？</span>
      <a href="#" @click.prevent="handleRegister">立即注册</a>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { authApi } from "@/utils/api";
import { useUserStore } from "@/stores/user";
import { saveEncryptedPassword, getDecryptedPassword, removeEncryptedPassword } from "@/utils/crypto";

// 定义组件事件
const emit = defineEmits<{
  switchTab: [tab: string];
  loginSuccess: [];
}>();

// 用户状态管理
const userStore = useUserStore();

// 登录加载状态
const loginLoading = ref(false);

// 表单引用
const passwordFormRef = ref<FormInstance>();

// 密码登录表单数据
const passwordForm = reactive({
  userType: "", // 用户类型
  username: "", // 用户名（手机号）
  password: "", // 密码
  rememberMe: false, // 记住密码
});

// 密码登录验证规则
const passwordRules: FormRules = {
  userType: [{ required: true, message: "请选择登录身份", trigger: "change" }],
  username: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号格式",
      trigger: "blur",
    },
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, message: "密码长度不能少于6位", trigger: "blur" },
  ],
};

/**
 * 处理密码登录
 */
const handleLogin = async () => {
  if (!passwordFormRef.value) return;

  try {
    // 表单验证
    await passwordFormRef.value.validate();

    loginLoading.value = true;

    // 调用密码登录API
    const response = await authApi.login({
      mobile: passwordForm.username,
      password: passwordForm.password,
    });

    if (response.code === 1) {
      // 登录成功
      const userInfo = response.data

      // 保存用户状态
      userStore.login(userInfo);

      // 如果选择记住密码，加密保存到本地存储
      if (passwordForm.rememberMe) {
        localStorage.setItem("rememberedUsername", passwordForm.username);
        saveEncryptedPassword("rememberedPassword", passwordForm.password);
        localStorage.setItem("rememberedUserType", passwordForm.userType);
      } else {
        localStorage.removeItem("rememberedUsername");
        removeEncryptedPassword("rememberedPassword");
        localStorage.removeItem("rememberedUserType");
      }

      ElMessage.success("登录成功");
      emit("loginSuccess");
    } else {
      ElMessage.error(response.msg || "登录失败，请检查用户名和密码");
    }
  } catch (error) {
    console.error("登录失败:", error);
    ElMessage.error("登录失败，请检查用户名和密码");
  } finally {
    loginLoading.value = false;
  }
};

/**
 * 处理忘记密码
 */
const handleForgotPassword = () => {
  emit("switchTab", "forgot");
};

/**
 * 处理注册
 */
const handleRegister = () => {
  emit("switchTab", "register");
};

// 组件挂载时检查是否有记住的用户名和密码
const rememberedUsername = localStorage.getItem("rememberedUsername");
const rememberedPassword = getDecryptedPassword("rememberedPassword");
const rememberedUserType = localStorage.getItem("rememberedUserType");

if (rememberedUsername && rememberedPassword && rememberedUserType) {
  passwordForm.username = rememberedUsername;
  passwordForm.password = rememberedPassword;
  passwordForm.userType = rememberedUserType;
  passwordForm.rememberMe = true;
}
</script>

<style lang="scss" scoped>
.login-form {
  .el-form-item {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .form-options {
    margin-bottom: 38px;

    :deep(.el-form-item__content) {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .remember-checkbox {
        :deep(.el-checkbox__label) {
          color: #666;
          font-size: 14px;
        }

        :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
          background-color: #004c66 !important;
          border-color: #004c66;
        }
      }

      .forgot-password-link {
        text-align: right;
        a {
          color: #004c66;
          text-decoration: none;
          font-size: 14px;

          &:hover {
            text-decoration: underline;
            background-color: transparent;
          }
        }
      }
    }
  }

  .password {
    margin-bottom: 8px;
  }

  :deep(.el-select__wrapper) {
    height: 46px;
  }

  .user-type-select,
  .login-input {
    width: 100%;
    height: 46px;
  }

  .login-button {
    width: 100%;
    height: 48px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;

    &:hover {
      background: rgba($color: #004c66, $alpha: 0.9);
    }
  }
}

.register-link {
  text-align: center;
  margin-top: 24px;
  color: #666;
  font-size: 14px;

  a {
    color: #004c66;
    text-decoration: none;
    margin-left: 4px;

    &:hover {
      text-decoration: underline;
      background-color: transparent;
    }
  }
}
</style>
