<template>
  <div class="auction-card" @click="handleButtonClick">
    <!-- 状态标签 -->
    <div
      class="status-tag"
      :class="{ 'status-active': isActive, 'status-verifying': isVerifying }"
    >
      <span>{{ statusText }}</span>
    </div>

    <!-- 标的图片 -->
    <div class="product-image">
      <img :src="bdPic" :alt="bdName" />
    </div>

    <!-- 标的信息 - 上中下结构 -->
    <div class="product-info">
      <!-- 上部分：标的标题 -->
      <span class="product-name">{{ bdName }}</span>

      <!-- 中间部分：价格和围观次数 -->
      <div class="price-view-container">
        <!-- 左侧：价格 -->
        <div class="price-section">
          <div class="current-price">
            <span class="price-integer">{{ priceInteger }}</span>
            <span class="price-decimal">.{{ priceDecimal }}</span>
          </div>
          <div class="price-label">
            <SvgIcon iconName="auction-action" class="price-icon"></SvgIcon>
            <span>{{ qpjDanwie }}</span>
          </div>
        </div>

        <!-- 分割线 -->
        <div class="divider"></div>

        <!-- 右侧：围观次数 -->
        <div class="view-section">
          <div class="view-count">{{ bdWeiguan }}</div>
          <div class="view-label">
            <SvgIcon iconName="auction-view" class="view-icon"></SvgIcon>
            <span>围观(次)</span>
          </div>
        </div>
      </div>

      <!-- 下部分：时间和按钮 -->
      <div class="time-button-container">
        <!-- 左侧：时间信息 -->
        <div class="time-section">
          <div class="time-info">
            <span class="time-label">{{ timeLabel }}</span>
            <span class="time-value">{{ endTime }}</span>
          </div>
          <div class="schedule-info">
            <span class="schedule-label">{{ scheduleLabel }}</span>
            <span class="schedule-value">{{ startTime }}</span>
          </div>
        </div>

        <!-- 右侧：操作按钮 -->
        <div class="button-section">
          <div
            class="action-button"
            :class="{ 'button-active': isButtonActive }"
          >
            <span>{{ buttonText }}</span>
            <SvgIcon
              iconName="auction-arrows-right"
              class="button-icon"
            ></SvgIcon>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

import type { AuctionItem } from "@/types/auction";

interface Props extends AuctionItem {}

const props = withDefaults(defineProps<Props>(), {
  priceUnit: "起拍价(元/吨)",
  timeLabel: "截止报名",
  scheduleLabel: "预计开始",
  status: 0,
});

const emit = defineEmits<{
  (e: "click", value: { productId: string; pmhId: string }): void;
}>();

// 计算属性
// status: 1-即将开始, 5-交易达成, 6-核实中
const isActive = computed(() => props.status === 1); // 即将开始状态为激活状态
const isVerifying = computed(() => props.status === 6); // 核实中状态
const isButtonActive = computed(() => props.status === 1); // 只有即将开始状态按钮为激活状态

// 根据status数字类型判断状态文本
const statusText = computed(() => {
  switch (props.status) {
    case 1:
      return "即将开始";
    case 5:
      return "交易完成";
    case 6:
      return "核实中";
    default:
      return "未知状态";
  }
});

// 根据status判断按钮文本：即将开始显示"立即报名"，其他显示"查看详情"
const buttonText = computed(() => {
  return props.status === 1 ? "立即报名" : "查看详情";
});

// 价格格式化
const priceInteger = computed(() => {
  return Math.floor(Number(props.bdQipaijia));
});

const priceDecimal = computed(() => {
  const decimal =
    (Number(props.bdQipaijia) - Math.floor(Number(props.bdQipaijia))) * 100;
  return decimal.toFixed(0).padStart(2, "0");
});

// 方法
const handleButtonClick = () => {
  emit("click", {
    productId: props.id.toString(),
    pmhId: props.pmhId.toString(),
  });
};
</script>

<style lang="scss" scoped>
.auction-card {
  position: relative;
  width: 295px;
  height: 352px;
  border-radius: 10px;
  border: 1px solid #eeeeee;
  background-color: #ffffff;
  overflow: hidden;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;

  &:hover {
    // transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  }

  // 状态标签样式
  .status-tag {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 23px;
    background: linear-gradient(90deg, #009678, #004c66);
    color: white;
    border-top-left-radius: 10px;
    border-bottom-right-radius: 10px;
    font-size: 12px;
    z-index: 10;

    // 即将开始状态样式
    &.status-active {
      background: linear-gradient(90deg, #ff6b00, #ff0000);
    }

    // 核实中状态样式 - 浅灰色背景
    &.status-verifying {
      background: linear-gradient(90deg, #cccccc, #999999);
    }

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -150%;
      width: 30%;
      height: 100%;
      background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.3) 50%,
        rgba(255, 255, 255, 0) 100%
      );
      transform: skewX(-25deg);
      z-index: 1;
      transition: none;
      pointer-events: none; // 确保不影响鼠标事件
    }
  }

  &:hover {
    .status-tag::before {
      // 给左上角标签添加闪光动画
      animation: shinee 1s ease-in-out;
    }
  }

  // 定义亮光扫过动画
  @keyframes shinee {
    0% {
      left: -150%;
    }
    100% {
      left: 80%;
    }
  }

  // 标的图片样式
  .product-image {
    width: 100%;
    height: 198px;
    overflow: hidden;
    position: relative; // 添加相对定位

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.5s ease;
      transform: scale(1.02);
    }

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -150%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.3) 50%,
        rgba(255, 255, 255, 0) 100%
      );
      transform: skewX(-25deg);
      z-index: 1;
      transition: none;
      pointer-events: none; // 确保不影响鼠标事件
    }
  }

  &:hover {
    .product-image img {
      transform: scale(1.08);
    }

    .product-image::before {
      // animation: shine 1s ease-in-out;
    }

    .product-name {
      font-size: 16px;
      color: #004c66;
    }
  }

  // 定义亮光扫过动画
  @keyframes shine {
    0% {
      left: -150%;
    }
    100% {
      left: 150%;
    }
  }

  // 标的信息样式 - 上中下结构
  .product-info {
    display: flex;
    justify-content: center;
    flex-direction: column;
    height: 154px; // 总高度减去图片高度和padding
    color: #333;

    // 上部分：标的标题
    .product-name {
      font-size: 16px;
      padding-left: 10px;
      margin-top: 9px;
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-family: "PingFang Bold";
    }

    // 中间部分：价格和围观次数
    .price-view-container {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 15px;
      font-family: "DIN Bold";

      // 左侧：价格
      .price-section {
        flex: 1;
        display: flex;
        align-items: center;
        flex-direction: column;

        .current-price {
          display: flex;
          align-items: baseline;
          color: #004c66;
          line-height: 1.15;

          .price-integer {
            font-size: 28px;
          }

          .price-decimal {
            font-size: 20px;
            font-weight: bold;
          }
        }

        .price-label {
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #999;
          font-family: "PingFang Medium";

          .price-icon {
            width: 10px;
            height: 10px;
            margin-right: 4px;
          }
        }
      }

      // 分割线
      .divider {
        width: 1px;
        height: 40px;
        background-color: #eeeeee;
      }

      // 右侧：围观次数
      .view-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-family: "DIN Bold";

        .view-count {
          font-size: 28px;
          color: #004c66;
          line-height: 1.15;
        }

        .view-label {
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #999;
          font-family: "PingFang Medium";

          .view-icon {
            width: 10px;
            height: 10px;
            margin-top: 2px;
            margin-right: 5px;
          }
        }
      }
    }

    // 下部分：时间和按钮
    .time-button-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: auto; // 推到底部
      border-top: 1px solid #dddddd;
      height: 49px;

      // 左侧：时间信息
      .time-section {
        flex: 0.95;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .time-info,
        .schedule-info {
          display: flex;
          justify-content: flex-start;
          font-size: 12px;

          .time-label,
          .schedule-label {
            color: #999;
            margin-right: 5px;
            font-family: "PingFang Medium";
            font-size: 12px;
            line-height: 1.5;
          }

          .time-value,
          .schedule-value {
            color: #999;
            font-family: "DIN Bold";
            font-size: 13px;
            line-height: 1.1;
            margin-bottom: 3.5px;
          }
        }
      }

      // 右侧：操作按钮
      .button-section {
        .action-button {
          display: flex;
          justify-content: center;
          width: 104px;
          height: 52px;
          background-color: #e6eef0;
          color: #004c66;
          border-bottom-right-radius: 5px;
          font-size: 14px;
          cursor: pointer;
          transition: background-color 0.3s ease;
          font-family: "FZZongYi-M05S";
          padding-top: 17px;

          &:hover {
            filter: brightness(0.9);
          }

          &.button-active {
            background-color: #fcf1e6;
            color: #de6f00;

            &:hover {
              filter: brightness(0.9);
            }
          }

          .button-icon {
            width: 6px;
            height: 10px;
            margin-left: 5px;
          }
        }
      }
    }
  }
}
</style>
