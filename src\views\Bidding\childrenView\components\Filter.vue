<template>
  <div class="filter-container">
    <div class="filter-content">
      <!-- 左侧标签区域 -->
      <div class="labels-section">
        <!-- <div class="label-item">标的类型</div> -->
        <div class="label-item area" ref="provinceTitleRef">所属省份</div>
        <div class="label-item">标的状态</div>
        <!-- <div class="label-item">开拍时间</div> -->
        <div class="label-item">已选条件</div>
      </div>

      <!-- 中间分割线 -->
      <div class="main-divider"></div>

      <!-- 右侧选项区域 -->
      <div class="options-section">
        <!-- 标的类型选项 -->
        <!-- <div class="option-row">
          <div class="filter-options">
            <div
              v-for="option in typeOptions"
              :key="option.value"
              :class="[
                'filter-option',
                { active: filters.type === option.value },
              ]"
              @click="handleTypeChange(option.value)"
            >
              {{ option.label }}
            </div>
          </div>
        </div> -->

        <!-- 所属省份选项 -->
        <div
          class="option-row province-city-container"
          ref="provinceOptionsRef"
          style="margin-top: 10px;"
        >
          <!-- 第一行省份选项 -->
          <div class="province-row-group">
            <div class="filter-options province-options first-row">
              <div
                v-for="option in firstRowProvinces"
                :key="option.value"
                :class="[
                  'filter-option province-option',
                  { active: filters.province === option.value },
                ]"
                @click="handleProvinceChange(option.value, 'first')"
              >
                {{ option.label }}
              </div>
            </div>
            
            <!-- 第一行城市选项 -->
            <transition name="city-slide">
              <div
                v-if="showFirstRowCities && firstRowCityOptions.length > 1"
                class="city-row first-row-cities"
              >
                <div class="filter-options city-options">
                  <div
                    v-for="option in firstRowCityOptions"
                    :key="option.value"
                    :class="[
                      'filter-option city-option',
                      { active: filters.city === option.value },
                    ]"
                    @click="handleCityChange(option.value)"
                  >
                    {{ option.label }}
                  </div>
                </div>
              </div>
            </transition>
          </div>
          
          <!-- 第二行省份选项 -->
          <div class="province-row-group">
            <div class="filter-options province-options second-row">
              <div
                v-for="option in secondRowProvinces"
                :key="option.value"
                :class="[
                  'filter-option province-option',
                  { active: filters.province === option.value },
                ]"
                @click="handleProvinceChange(option.value, 'second')"
              >
                {{ option.label }}
              </div>
            </div>
            
            <!-- 第二行城市选项 -->
            <transition name="city-slide">
              <div
                v-if="showSecondRowCities && secondRowCityOptions.length > 1"
                class="city-row second-row-cities"
              >
                <div class="filter-options city-options">
                  <div
                    v-for="option in secondRowCityOptions"
                    :key="option.value"
                    :class="[
                      'filter-option city-option',
                      { active: filters.city === option.value },
                    ]"
                    @click="handleCityChange(option.value)"
                  >
                    {{ option.label }}
                  </div>
                </div>
              </div>
            </transition>
          </div>
          
          <!-- 第三行省份选项 -->
          <div class="province-row-group">
            <div class="filter-options province-options third-row">
              <div
                v-for="option in thirdRowProvinces"
                :key="option.value"
                :class="[
                  'filter-option province-option',
                  { active: filters.province === option.value },
                ]"
                @click="handleProvinceChange(option.value, 'third')"
              >
                {{ option.label }}
              </div>
            </div>
            
            <!-- 第三行城市选项 -->
            <transition name="city-slide">
              <div
                v-if="showThirdRowCities && thirdRowCityOptions.length > 1"
                class="city-row third-row-cities"
              >
                <div class="filter-options city-options">
                  <div
                    v-for="option in thirdRowCityOptions"
                    :key="option.value"
                    :class="[
                      'filter-option city-option',
                      { active: filters.city === option.value },
                    ]"
                    @click="handleCityChange(option.value)"
                  >
                    {{ option.label }}
                  </div>
                </div>
              </div>
            </transition>
          </div>
        </div>

        <!-- 标的状态选项 -->
        <div class="option-row">
          <div class="filter-options status-options">
            <div
              v-for="option in statusOptions"
              :key="option.value"
              :class="[
                'filter-option',
                { active: filters.status === option.value },
              ]"
              @click="handleStatusChange(option.value)"
            >
              {{ option.label }}
            </div>
          </div>
        </div>

        <!-- 开拍时间选项 -->
        <!-- <div class="option-row">
          <div class="filter-options">
            <div
              v-for="option in timeOptions"
              :key="option.value"
              :class="[
                'filter-option',
                { active: filters.timeType === option.value },
              ]"
              @click="handleTimeTypeChange(option.value)"
            >
              {{ option.label }}
            </div>
            自定义时间范围
            <div
              class="custom-time-container"
              v-if="filters.timeType === 'custom'"
            >
              <el-date-picker
                v-model="filters.customTimeRange"
                type="daterange"
                range-separator="—"
                start-placeholder="2025年12月24日"
                end-placeholder="2025年12月30日"
                format="YYYY年MM月DD日"
                value-format="YYYY-MM-DD"
                @change="handleCustomTimeChange"
                class="filter-date-picker"
              />
              <el-button
                type="primary"
                size="small"
                @click="confirmCustomTime"
                class="confirm-btn"
              >
                确定
              </el-button>
            </div>
          </div>
        </div> -->

        <!-- 已选条件 -->
        <div class="selected-conditions">
          <div class="tags-container">
            <el-tag
              v-for="tag in selectedTags"
              :key="tag.key"
              closable
              @close="removeTag(tag.key)"
              class="condition-tag"
            >
              {{ tag.label }}
            </el-tag>
          </div>
          <div class="result-count">
            <span class="count-text">灰谷网为你筛选了</span>
            <span class="count-number">{{ props.total }}</span>
            <span class="count-text">条相关结果</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  watch,
  computed,
  onMounted,
  onUnmounted,
  nextTick,
} from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus"; // ElTag 由 unplugin-element-plus 自动引入
import SvgIcon from "@/components/SvgIcon.vue";
// @ts-ignore
import area from "@/assets/js/area.js";

const router = useRouter();

// props
const props = defineProps({
  total: {
    type: Number,
    default: 0,
  },
});

// DOM引用
const provinceOptionsRef = ref<HTMLElement>(); // 省份选项区域的DOM引用
const provinceTitleRef = ref<HTMLElement>(); // 省份标题的DOM引用

// 为area模块添加类型声明
interface AreaData {
  area: {
    province_list: Record<string, string>;
    city_list: Record<string, string>;
  };
}

const areaData = area as AreaData;

// 定义类型
interface FilterData {
  type: number;
  province: string;
  city: string;
  page: number;
  status: number | string;
  keyword: string;
}

interface TagItem {
  key: string;
  label: string;
}

interface OptionItem {
  label: string;
  value: any;
}

type TagLabelKey =
  | "equipment"
  | "vehicle"
  | "property"
  | "land"
  | "other"
  | "not_started"
  | "failed"
  | "completed"
  | "verifying"
  | "paused"
  | "withdrawn"
  | "3days"
  | "7days"
  | "15days";

// 筛选条件数据
const filters = reactive<FilterData>({
  province: "",
  city: "",
  keyword: "",
  page: 1,
  status: '1,5,6',
  type: 0,
});

// 当前选中省份所在的行（用于控制城市显示）
const selectedProvinceRow = ref<'first' | 'second' | 'third' | ''>('');

// 分割省份数据为三行
const firstRowProvinces = computed(() => {
  const thirdIndex = Math.ceil(provinceOptions.length / 3);
  return provinceOptions.slice(0, thirdIndex);
});

const secondRowProvinces = computed(() => {
  const thirdIndex = Math.ceil(provinceOptions.length / 3);
  return provinceOptions.slice(thirdIndex, thirdIndex * 2);
});

const thirdRowProvinces = computed(() => {
  const thirdIndex = Math.ceil(provinceOptions.length / 3);
  return provinceOptions.slice(thirdIndex * 2);
});

// 控制城市显示状态
const showFirstRowCities = computed(() => {
  return selectedProvinceRow.value === 'first' && filters.province;
});

const showSecondRowCities = computed(() => {
  return selectedProvinceRow.value === 'second' && filters.province;
});

const showThirdRowCities = computed(() => {
  return selectedProvinceRow.value === 'third' && filters.province;
});

// 第一行城市选项
const firstRowCityOptions = computed<OptionItem[]>(() => {
  if (!filters.province || selectedProvinceRow.value !== 'first') return [];
  
  const provincePrefix = filters.province.substring(0, 2);
  const cities = Object.entries(areaData.area.city_list)
    .filter(([code]) => code.substring(0, 2) === provincePrefix)
    .map(([code, name]) => ({
      label: name.replace(/[省市]$/g, ''),
      value: code,
    }));
  
  return [{ label: "不限", value: "" }, ...cities];
});

// 第二行城市选项
const secondRowCityOptions = computed<OptionItem[]>(() => {
  if (!filters.province || selectedProvinceRow.value !== 'second') return [];
  
  const provincePrefix = filters.province.substring(0, 2);
  const cities = Object.entries(areaData.area.city_list)
    .filter(([code]) => code.substring(0, 2) === provincePrefix)
    .map(([code, name]) => ({
      label: name.replace(/[省市]$/g, ''),
      value: code,
    }));
  
  return [{ label: "不限", value: "" }, ...cities];
});

// 第三行城市选项
const thirdRowCityOptions = computed<OptionItem[]>(() => {
  if (!filters.province || selectedProvinceRow.value !== 'third') return [];
  
  const provincePrefix = filters.province.substring(0, 2);
  const cities = Object.entries(areaData.area.city_list)
    .filter(([code]) => code.substring(0, 2) === provincePrefix)
    .map(([code, name]) => ({
      label: name.replace(/[省市]$/g, ''),
      value: code,
    }));
  
  return [{ label: "不限", value: "" }, ...cities];
});

// 筛选选项数据
const typeOptions: OptionItem[] = [
  { label: "不限", value: "" },
  { label: "物资设备", value: "equipment" },
  { label: "机动车", value: "vehicle" },
  { label: "房产", value: "property" },
  { label: "土地", value: "land" },
  { label: "其他", value: "other" },
];

const statusOptions: OptionItem[] = [
  { label: "不限", value: '1,5,6' },
  { label: "未开始", value: 1 },
  { label: "已成交", value: 5 },
  { label: "核实中", value: 6 },
];

const timeOptions: OptionItem[] = [
  { label: "不限", value: "" },
  { label: "未来3天", value: "3days" },
  { label: "未来7天", value: "7days" },
  { label: "未来15天", value: "15days" },
  { label: "自定义", value: "custom" },
];

// 省份选项数据
const provinceOptions: OptionItem[] = [
  { label: "不限", value: "" },
  ...Object.entries(areaData.area.province_list).map(([code, name]) => ({
    label: name.replace(/[省市]$/g, ""), // 同时去除省和市字
    value: code,
  })),
];

// 注释：cityOptions已被firstRowCityOptions和secondRowCityOptions替代

// 结果数量
const resultCount = ref<number>(70);

// 高度同步函数
const syncProvinceHeight = () => {
  if (provinceOptionsRef.value && provinceTitleRef.value) {
    // 等待DOM更新完成后再计算高度
    nextTick(() => {
      const optionsHeight = provinceOptionsRef.value!.offsetHeight;
      provinceTitleRef.value!.style.height = `${optionsHeight}px`;
    });
  }
};

// ResizeObserver实例
let resizeObserver: ResizeObserver | null = null;

// 标签映射
const tagLabels: Record<TagLabelKey, string> = {
  equipment: "物资设备",
  vehicle: "机动车",
  property: "房产",
  land: "土地",
  other: "其他",
  not_started: "未开始",
  failed: "已流拍",
  completed: "已成交",
  verifying: "核实中",
  paused: "暂停",
  withdrawn: "已撤拍",
  "3days": "未来3天",
  "7days": "未来7天",
  "15days": "未来15天",
};

// 计算已选标签
const selectedTags = computed<TagItem[]>(() => {
  const tags: TagItem[] = [];

  // 标的类型
  /* if (filters.type) {
    const typeKey = filters.type as TagLabelKey;
    tags.push({
      key: "type",
      label: tagLabels[typeKey] || filters.type,
    });
  } */

  // 省份
  if (filters.province) {
    const provinceName = areaData.area.province_list[filters.province];
    if (provinceName) {
      tags.push({
        key: "province",
        label: provinceName.replace(/[省市]$/g, ""), // 同时去除省和市字
      });
    }
  }

  // 城市
  if (filters.city) {
    const cityName = areaData.area.city_list[filters.city];
    if (cityName) {
      tags.push({
        key: "city",
        label: cityName.replace(/[省市]$/g, ""), // 同时去除省和市字
      });
    }
  }

  // 状态 - 只有当状态不是默认值'1,5,6'时才显示标签
  if (filters.status && filters.status !== '1,5,6') {
    // 根据状态获取label
    const statusLabel = statusOptions.find(
      (item) => item.value === filters.status
    )?.label;
    tags.push({
      key: "status",
      label: statusLabel as string,
    });
  }

  // 时间
  /* if (filters.timeType) {
    if (filters.timeType === "custom" && filters.customTimeRange) {
      tags.push({
        key: "time",
        label: `${filters.customTimeRange[0]} 至 ${filters.customTimeRange[1]}`,
      });
    } else {
      const timeKey = filters.timeType as TagLabelKey;
      tags.push({
        key: "time",
        label: tagLabels[timeKey] || filters.timeType,
      });
    }
  } */

  return tags;
});

// 事件处理函数
/* const handleTypeChange = (value: string): void => {
  filters.type = value;
  emitFilterChange();
}; */

const handleProvinceChange = (value: string, row: 'first' | 'second' | 'third'): void => {
  filters.province = value;
  selectedProvinceRow.value = value ? row : '';
  // 切换省份时清空城市选择
  filters.city = "";
  emitFilterChange();
};

const handleCityChange = (value: string): void => {
  filters.city = value;
  emitFilterChange();
};

const handleStatusChange = (value: number): void => {
  filters.status = value;
  emitFilterChange();
};

/* const handleTimeTypeChange = (value: string): void => {
  filters.timeType = value;
  if (value !== "custom") {
    filters.customTimeRange = null;
  }
  emitFilterChange();
}; */

/* const confirmCustomTime = (): void => {
  if (filters.customTimeRange) {
    emitFilterChange();
  } else {
    ElMessage.warning("请选择时间范围");
  }
}; */

/* const handleCustomTimeChange = (value: [string, string] | null): void => {
  // 时间范围变化时的处理
}; */

// 移除标签
const removeTag = (key: string): void => {
  switch (key) {
    case "type":
      filters.type = 0;
      break;
    case "province":
      filters.province = "";
      selectedProvinceRow.value = "";
      filters.city = "";
      break;
    case "city":
      filters.city = "";
      break;
    case "status":
      // 移除状态标签时重置为默认值'1,5,6'，这样不会显示标签但保持默认筛选
      filters.status = '1,5,6';
      break;
    /* case "time":
      filters.timeType = "";
      filters.customTimeRange = null;
      break; */
  }
  emitFilterChange();
};

// 发出筛选变化事件
const emit = defineEmits<{
  "filter-change": [value: FilterData & { selectedCount: number }];
}>();

const emitFilterChange = (): void => {
  emit("filter-change", {
    ...filters,
    selectedCount: selectedTags.value.length,
  });
};

// 监听省份筛选条件变化，重新同步高度
watch(
  () => filters.province,
  () => {
    // 等待DOM更新后重新同步高度
    nextTick(() => {
      syncProvinceHeight();
    });
  }
);

// 监听选中省份变化，重新同步高度（城市选项显示/隐藏时）
watch(
  () => filters.province,
  () => {
    // 等待DOM更新后重新同步高度
    nextTick(() => {
      syncProvinceHeight();
    });
  }
);

// 监听城市选项变化，同步高度
watch(
  [() => firstRowCityOptions.value.length, () => secondRowCityOptions.value.length, () => thirdRowCityOptions.value.length, selectedProvinceRow],
  () => {
    nextTick(() => {
      syncProvinceHeight();
    });
  }
);

// 组件挂载时加载数据
onMounted(() => {
  // 等待DOM渲染完成后初始化高度监听
  nextTick(() => {
    // 初始化高度同步
    syncProvinceHeight();

    // 创建ResizeObserver监听省份选项区域高度变化
    if (provinceOptionsRef.value) {
      resizeObserver = new ResizeObserver(() => {
        syncProvinceHeight();
      });
      resizeObserver.observe(provinceOptionsRef.value);
    }
    // console.log(router.currentRoute.value.query); 已移除
    const { name, type } = router.currentRoute.value.query;
    if (
      typeof name === "string" &&
      typeof type === "string" &&
      name in filters
    ) {
      // 类型安全的赋值
      (filters as any)[name] = type;
    }

    // 如果已经有选中的省份，确定其所在行
    if (filters.province) {
      const isInFirstRow = firstRowProvinces.value.some(p => p.value === filters.province);
      selectedProvinceRow.value = isInFirstRow ? 'first' : 'second';
    }
  });
});

// 组件卸载时清理资源
onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
});
</script>

<style lang="scss" scoped>
.filter-container {
  .filter-content {
    display: flex;
    align-items: flex-start;
    gap: 0;

    .labels-section {
      min-width: 80px;
      flex-shrink: 0;
      margin-top: 8px;

      .label-item {
        font-size: 14px;
        color: #999;
        white-space: nowrap;
        display: flex;
        align-items: center;
        margin: 0;
        padding: 8px 0;
      }

      .area {
        align-items: flex-start;
        transition: height 0.3s ease;
      }
    }

    // 分割线样式
    .main-divider {
      width: 1px;
      background: linear-gradient(
        180deg,
        #ffffff 0%,
        #dddddd 50%,
        #ffffff 100%
      );
      margin: 0 26px 0 16px;
      align-self: stretch;
      min-height: 100px; // 确保分割线有最小高度
      flex-shrink: 0; // 防止分割线被压缩
    }
  }
}

.options-section {
  flex: 1;
}

.option-row {
  display: flex;
  align-items: center;
  margin: 0;
  padding: 0;
}

.filter-options {
  display: flex;
  gap: 45px;
  align-items: center;
  flex: 1;
  // padding: 8px 0;
}

.status-options {
  padding: 0 0 8px 0;
}

// 省份城市容器样式
.province-city-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  align-items: stretch;

  // 注释：已移除province-options-wrapper，使用province-row-group
}

.province-row-group {
  display: flex;
  flex-direction: column;
}

.province-row-group:last-child {
  margin-bottom: 0;
}

.province-options.first-row,
.province-options.second-row {
  padding: 0;
}

.city-row.first-row-cities,
.city-row.second-row-cities {
  margin-top: 2px;
}

.filter-option {
  min-width: 52px;
  padding: 6px 0;
  border-radius: 4px;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;

  &:hover {
    color: #004c66;
  }

  &.active {
    color: #004c66;
  }

  &.city-option {
    background-color: transparent;
    color: #333;

    &:hover {
      color: #333;
    }

    &.active {
      color: #004c66;
    }
  }
}

// 城市选项行样式
.city-row {
  width: 100%;
  background-color: #eee;
  padding: 2px 8px;
  border-radius: 4px;
  position: relative;
  left: -8px;

  .city-options {
    display: flex;
    flex-wrap: wrap;
    gap: 0px 45px;
    padding: 0;
    align-items: center;
  }
}

.custom-time-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;

  .filter-date-picker {
    width: 280px;
  }

  .confirm-btn {
    padding: 6px 12px;
    font-size: 12px;
  }
}

.selected-conditions {
  display: flex;
  align-items: center;
  gap: 12px;

  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    flex: 1;
  }
}

.condition-tag {
  background: #d3e0e4;
  color: #004c66;
  border: 1px solid #004c66;
  padding: 5px 10px;
  font-size: 14px;

  :deep(.el-tag__close) {
    color: #004c66;

    &:hover {
      background: #004c66;
      color: #fff;
    }
  }
}

// 筛选部分样式
.result-count {
  display: flex;
  align-items: end;
  white-space: nowrap;
  margin-left: auto;

  .count-text {
    font-size: 14px;
    color: #666;
  }

  .count-number {
    font-size: 24px;
    font-family: "DIN Bold";
    color: #004c66;
    margin: 0 4px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .filter-content {
    flex-direction: column;
    gap: 16px;
  }

  .labels-section {
    min-width: auto;
    display: flex;
    gap: 16px;
    overflow-x: auto;
  }

  .area {
    height: 2000px;
  }

  // 在移动端隐藏分割线，因为布局变为垂直排列
  .main-divider {
    display: none;
  }

  .option-row {
    height: auto;
    display: none;

    &.active {
      display: flex;
    }
  }

  .filter-date-picker {
    width: 100%;
    max-width: 280px;
  }

  .selected-conditions {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .result-count {
    margin-left: 0;
    justify-content: center;
  }
}
</style>
