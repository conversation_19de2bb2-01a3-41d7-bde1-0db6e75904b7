// 拍卖相关的类型定义

// 拍卖商品数据类型
export interface AuctionItem {
  id: number;
  pmhId: number;
  bdName: string;
  startTime: string;
  endTime: string;
  bdPic: string;
  bdQipaijia: string;
  qpjDanwie: string;
  bdWeiguan: number;
  timeLabel: string;
  scheduleLabel: string;
  status: number;
}

// 列表项数据类型
export interface ListItem {
  productId: string;
  productName: string;
  productImage: string;
  description: string;
  companyName: string;
  timeValue: string;
  likeCount: number;
  commentCount: number;
  viewCount: number;
}