import axios from 'axios'
import type { AxiosResponse, AxiosError } from 'axios'
import { ElMessage } from "element-plus";

/**
 * 新的HTTP请求配置和拦截器
 * 用于处理新的后端服务地址：http://172.20.8.143:8080/jeecg-boot
 * 暂时不校验token
 */

// 创建新的axios实例
const httpNew = axios.create({
  // 开发环境使用代理，生产环境使用实际API地址
  baseURL: import.meta.env.DEV ? '/new-api' : (import.meta.env.VITE_NEW_API_BASE_URL || 'http://39.101.72.34:18080/jeecgboot'),
  timeout: 10000, // 请求超时时间
  withCredentials: false // 是否携带cookie
})

/**
 * 检测是否为IE9浏览器
 * @returns {boolean} 是否为IE9
 */
function isIE9(): boolean {
  if (
    navigator.appName === "Microsoft Internet Explorer" &&
    parseInt(
      navigator.appVersion
        .split(";")
        [1].replace(/[ ]/g, "")
        .replace("MSIE", "")
    ) <= 9
  ) {
    return true
  }
  return false
}

/**
 * 响应拦截器
 * 用于统一处理响应数据和错误
 */
httpNew.interceptors.response.use(
  (response: AxiosResponse) => {
    // 判断是否为IE9浏览器，进行特殊处理
    if (isIE9()) {
      // 特殊处理IE9的响应数据
      if (response.status === 200 && response.request) {
        if (
          response.request.responseType === "json" &&
          response.request.responseText
        ) {
          response.data = JSON.parse(response.request.responseText)
          // console.log("IE9 response processed:", response)
        }
      }
    }
    return response
  },
  (error: AxiosError) => {
    if (error.response) {
      // 处理HTTP错误状态码
      console.error("HTTP Error:", error)
      
      // 根据状态码显示不同的错误信息
      switch (error.response.status) {
        case 401:
          ElMessage.error('未授权，请重新登录')
          // 可以在这里处理登录跳转逻辑
          break
        case 403:
          ElMessage.error('拒绝访问')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error('网络错误，请稍后重试')
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应
      ElMessage.error('网络连接超时，请检查网络')
    } else {
      // 其他错误
      ElMessage.error('请求配置错误')
    }
    return Promise.reject(error)
  }
)

/**
 * 获取用户租户ID
 * 从缓存中的freedomUserInfo获取loginTenantId
 */
function getTenantId(): string | null {
  try {
    const userInfoStr = localStorage.getItem('freedomUserInfo') || sessionStorage.getItem('freedomUserInfo')
    if (userInfoStr) {
      const userInfo = JSON.parse(userInfoStr)
      return userInfo?.userInfo?.loginTenantId || null
    }
  } catch (error) {
    console.error('获取租户ID失败:', error)
  }
  return null
}

/**
 * 请求拦截器
 * 统一为所有请求添加时间戳参数、token认证、loginSource、租户ID和时间戳请求头
 */
httpNew.interceptors.request.use(
  (config) => {
    // 添加token认证信息
    const token = localStorage.getItem('freedomToken') || sessionStorage.getItem('freedomToken')
    if (token) {
      config.headers['x-access-token'] = token
    }

    // 添加loginSource到请求头
    config.headers['loginSource'] = '1'

    // 添加租户ID到请求头
    const tenantId = getTenantId()
    if (tenantId) {
      config.headers['X-Tenant-Id'] = tenantId
    }

    // 添加时间戳到请求头
    const timestamp = new Date().getTime()
    config.headers['X-Timestamp'] = timestamp.toString()

    // 统一添加时间戳参数到URL（防止缓存）
    const separator = config.url?.includes('?') ? '&' : '?'
    config.url = `${config.url}${separator}_t=${timestamp}`

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 导出配置好的新axios实例
export default httpNew

// 导出类型定义供其他文件使用（已移除）