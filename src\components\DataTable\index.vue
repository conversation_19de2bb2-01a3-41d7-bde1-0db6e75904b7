<template>
  <div class="data-table-container">
    <!-- 按钮切换栏 -->
    <div v-if="config.buttonBar?.show" class="button-bar">
      <div
        v-for="button in config.buttonBar.buttons"
        :key="button.key"
        :class="['button-item', { 'is-active': button.active }]"
        @click="handleButtonClick(button.key)"
      >
        <span class="button-text">{{ button.label }}</span>
        <div v-if="button.active" class="check-badge">
          <el-icon class="check-icon">
            <Check />
          </el-icon>
        </div>
      </div>
    </div>

    <!-- 筛选栏 -->
    <div v-if="config.filterBar?.show" class="filter-bar">
      <el-form :model="filterForm" inline class="filter-form">
        <el-form-item
          v-for="filter in config.filterBar.filters"
          :key="filter.key"
          :label="filter.label"
          class="filter-item"
        >
          <!-- 输入框 -->
          <el-input
            v-if="filter.type === 'input'"
            v-model="filterForm[filter.key]"
            :placeholder="filter.placeholder"
            :style="{ width: filter.width || '200px' }"
            clearable
            @change="handleFilterChange"
          />
          
          <!-- 选择框 -->
          <el-select
            v-else-if="filter.type === 'select'"
            v-model="filterForm[filter.key]"
            :placeholder="filter.placeholder"
            :style="{ width: filter.width || '200px' }"
            clearable
            @change="handleFilterChange"
          >
            <el-option
              v-for="option in filter.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
          
          <!-- 日期选择器 -->
          <el-date-picker
            v-else-if="filter.type === 'date'"
            v-model="filterForm[filter.key]"
            type="date"
            :placeholder="filter.placeholder"
            :style="{ width: filter.width || '200px' }"
            @change="handleFilterChange"
          />
          
          <!-- 日期范围选择器 -->
          <el-date-picker
            v-else-if="filter.type === 'daterange'"
            v-model="filterForm[filter.key]"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :style="{ width: filter.width || '300px' }"
            @change="handleFilterChange"
          />
        </el-form-item>
        
        <!-- 搜索和重置按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            :loading="searchLoading"
            @click="handleSearch"
          >
            <el-icon v-if="!searchLoading"><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 水平导航栏 -->
    <div v-if="config.tabBar?.show" class="tab-bar" :class="{ 'with-divider': shouldShowDivider }">
      <!-- 分割线 -->
      <div v-if="shouldShowDivider" class="tab-divider"></div>
      <div class="tab-bar-content">
        <div class="custom-tabs" ref="tabsContainer">
          <div
            v-for="(tab, index) in config.tabBar.tabs"
            :key="tab.key"
            :ref="el => setTabRef(el, index)"
            :class="['tab-item', { 'is-active': config.tabBar.activeKey === tab.key }]"
            @click="handleTabClick(tab, index)"
          >
            <span class="tab-label">{{ tab.label }}</span>
            <span v-if="tab.count !== undefined" class="tab-count">({{ tab.count }})</span>
          </div>
          <!-- 滑动下划线 -->
          <div class="sliding-underline" ref="slidingUnderline"></div>
        </div>

        <!-- 右侧按钮 -->
        <div v-if="config.tabBar.rightButtons && config.tabBar.rightButtons.length > 0" class="tab-right-buttons">
          <el-button
            v-for="button in config.tabBar.rightButtons"
            :key="button.key"
            :type="button.type || 'default'"
            :size="button.size || 'default'"
            :loading="button.loading"
            :icon="button.icon"
            @click="handleTabButtonClick(button)"
          >
            {{ button.label }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 表格 -->
    <div class="table-container">
      <el-table
        :data="config.data || []"
        :loading="config.loading"
        element-loading-text="加载中..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
        :stripe="config.tableStyle?.stripe !== false"
        :border="config.tableStyle?.border !== false"
        style="width: 100%"
        :max-height="config.tableStyle?.maxHeight || 380"
        @row-click="handleRowClick"
        @row-dblclick="handleRowDoubleClick"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <!-- 选择列 -->
        <el-table-column
          v-if="config.tableStyle?.showSelection"
          type="selection"
          width="55"
          align="center"
        />
        
        <!-- 数据列 -->
        <el-table-column
          v-for="column in config.columns"
          :key="column.key"
          :prop="column.key"
          :label="column.label"
          :width="column.width"
          :align="column.align || 'left'"
          :sortable="column.sortable"
          :fixed="column.type === 'action' ? 'right' : column.fixed"
        >
          <template #default="{ row, $index }">
            <span v-if="column.type === 'text' || !column.type">
              {{ column.formatter ? column.formatter(row[column.key], row) : (column.key === 'index' ? $index + 1 : row[column.key]) }}
            </span>
            
            <el-image
              v-else-if="column.type === 'image'"
              :src="row[column.key]"
              style="width: 50px; height: 50px"
              fit="cover"
              :preview-src-list="[row[column.key]]"
            />
            
            <el-tag
              v-else-if="column.type === 'tag'"
              :type="getTagType(row[column.key])"
            >
              {{ column.formatter ? column.formatter(row[column.key], row) : row[column.key] }}
            </el-tag>
            
            <span v-else-if="column.type === 'date'">
              {{ formatDate(row[column.key]) }}
            </span>
            
            <span v-else-if="column.type === 'money'">
              ¥{{ formatMoney(row[column.key]) }}
            </span>
            
            <!-- 操作列 -->
            <div v-else-if="column.type === 'action'" class="action-buttons">
              <el-button type="primary" link @click="handleEdit(row)">
                编辑
              </el-button>
              <span class="action-divider">|</span>
              <el-button type="danger" link @click="handleDelete(row)">
                删除
              </el-button>
              <span class="action-divider">|</span>
              <el-button type="primary" link @click="handleView(row)">
                查看
              </el-button>
            </div>

            <!-- 自定义渲染 -->
            <component
              v-else-if="column.render"
              :is="column.render(row[column.key], row)"
            />
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div v-if="config.pagination?.show" class="pagination-container">
      <el-pagination
        :current-page="config.pagination.current"
        :page-size="config.pagination.pageSize"
        :total="config.pagination.total"
        :page-sizes="config.pagination.pageSizes || [10, 20, 50, 100]"
        :show-size-changer="config.pagination.showSizeChanger"
        :show-quick-jumper="config.pagination.showQuickJumper"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, onMounted, computed } from 'vue'
import { Search, Refresh, Check } from '@element-plus/icons-vue'
import type { TableConfig } from '@/types/table'
import * as AntdIcons from '@ant-design/icons-vue'

interface Props {
  config: TableConfig
  searchLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  searchLoading: false
})

const emit = defineEmits<{
  onButtonClick: [key: string]
  onFilterChange: [filters: Record<string, any>]
  onTabChange: [key: string]
  onPageChange: [page: number, pageSize: number]
  onSortChange: [column: string, order: 'asc' | 'desc' | null]
  onRowClick: [row: any, index: number]
  onRowDoubleClick: [row: any, index: number]
  onSelectionChange: [selectedRows: any[]]
  onEdit: [row: any]
  onDelete: [row: any]
  onView: [row: any]
}>()

// 筛选表单数据
const filterForm = reactive<Record<string, any>>({})

// 计算是否显示分割线
// 当页面中只使用了水平导航栏时（没有按钮栏和筛选栏），则不显示分割线
const shouldShowDivider = computed(() => {
  const hasButtonBar = props.config.buttonBar?.show
  const hasFilterBar = props.config.filterBar?.show
  return hasButtonBar || hasFilterBar
})

// 水平导航栏相关
const tabsContainer = ref<HTMLElement>()
const slidingUnderline = ref<HTMLElement>()
const tabRefs = ref<(HTMLElement | null)[]>([])

// 设置tab元素引用
const setTabRef = (el: any, index: number) => {
  if (el) {
    tabRefs.value[index] = el as HTMLElement
  }
}

// 更新下划线位置
const updateUnderlinePosition = (activeIndex: number) => {
  if (!slidingUnderline.value || !tabRefs.value[activeIndex]) return

  const activeTab = tabRefs.value[activeIndex]
  const tabRect = activeTab.getBoundingClientRect()
  const containerRect = tabsContainer.value?.getBoundingClientRect()

  if (containerRect) {
    const left = tabRect.left - containerRect.left
    const width = tabRect.width - 20 // 减去padding

    slidingUnderline.value.style.left = `${left + 10}px` // 加上padding
    slidingUnderline.value.style.width = `${width}px`
  }
}

// 初始化筛选表单
const initFilterForm = () => {
  if (props.config.filterBar?.filters) {
    props.config.filterBar.filters.forEach(filter => {
      filterForm[filter.key] = filter.value || ''
    })
  }
}

// 获取Ant Design图标组件
const getAntdIcon = (iconName?: string) => {
  if (!iconName) return null
  
  let iconKey = iconName
  if (iconName.startsWith('ant-design:')) {
    iconKey = iconName.replace('ant-design:', '')
  }
  
  const pascalCase = iconKey
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join('')
  
  return (AntdIcons as any)[pascalCase] || null
}

// 获取标签页标签
const getTabLabel = (tab: any) => {
  let label = tab.label
  if (tab.count !== undefined) {
    label += ` (${tab.count})`
  }
  return label
}

// 获取标签类型
const getTagType = (value: any) => {
  // 可以根据值返回不同的标签类型
  return 'primary'
}

// 格式化日期
const formatDate = (date: any) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString()
}

// 格式化金额
const formatMoney = (money: any) => {
  if (!money) return '0.00'
  return Number(money).toFixed(2)
}

// 事件处理函数
const handleButtonClick = (key: string) => {
  emit('onButtonClick', key)
}

const handleFilterChange = () => {
  emit('onFilterChange', { ...filterForm })
}

const handleSearch = () => {
  emit('onFilterChange', { ...filterForm })
}

const handleReset = () => {
  Object.keys(filterForm).forEach(key => {
    filterForm[key] = ''
  })
  emit('onFilterChange', { ...filterForm })
}

const handleTabClick = (tab: any, index: number) => {
  emit('onTabChange', tab.key)
  updateUnderlinePosition(index)
}

// 处理水平导航栏右侧按钮点击
const handleTabButtonClick = (button: any) => {
  emit('onButtonClick', button.key)
}



const handleRowClick = (row: any) => {
  emit('onRowClick', row, props.config.data?.indexOf(row) || 0)
}

const handleRowDoubleClick = (row: any) => {
  emit('onRowDoubleClick', row, props.config.data?.indexOf(row) || 0)
}

const handleSelectionChange = (selection: any[]) => {
  emit('onSelectionChange', selection)
}

const handleSortChange = ({ column, prop, order }: any) => {
  emit('onSortChange', prop, order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : null)
}

const handleSizeChange = (size: number) => {
  emit('onPageChange', props.config.pagination?.current || 1, size)
}

const handleCurrentChange = (page: number) => {
  emit('onPageChange', page, props.config.pagination?.pageSize || 10)
}

// 操作列事件处理
const handleEdit = (row: any) => {
  emit('onEdit', row)
}

const handleDelete = (row: any) => {
  emit('onDelete', row)
}

const handleView = (row: any) => {
  emit('onView', row)
}

// 初始化
initFilterForm()

// 初始化下划线位置
onMounted(() => {
  if (props.config.tabBar?.show && props.config.tabBar.tabs.length > 0) {
    const activeIndex = props.config.tabBar.tabs.findIndex(tab => tab.key === props.config.tabBar?.activeKey)
    if (activeIndex >= 0) {
      nextTick(() => {
        updateUnderlinePosition(activeIndex)
      })
    }
  }
})
</script>

<style scoped lang="scss">
.data-table-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  
  .button-bar {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    gap: 12px;

    .button-item {
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      padding: 8px 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      background: #fff;
      position: relative;

      &:hover {
        border-color: #004C66;
        color: #004C66;
      }

      &.is-active {
        border-color: #004C66;
        background: #f0f8ff;
        color: #004C66;
      }

      .button-text {
        font-size: 14px;
        font-weight: 400;
      }

      .check-badge {
        position: absolute;
        top: 0;
        right: 0;
        width: 16px;
        height: 14px;
        background: #004C66;
        border-radius: 0 4px 0 4px;
        display: flex;
        align-items: center;
        justify-content: center;

        .check-icon {
          color: #fff;
          font-size: 10px;
        }
      }
    }
  }
  
  .filter-bar {
    padding: 16px 0;
    border-radius: 6px;
    
    .filter-form {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      overflow-x: auto;

      .el-form-item {
        margin-bottom: 0;
        margin-right: 16px;
        flex-shrink: 0;
        white-space: nowrap;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
  
  .tab-bar {
    margin-bottom: 16px;

    &.with-divider {
      .tab-divider {
        height: 1px;
        background-color: #dddddd;
        margin-bottom: 16px;
      }
    }

    .tab-bar-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .custom-tabs {
      display: flex;
      position: relative;

      .tab-item {
        padding: 12px 10px;
        cursor: pointer;
        color: #666;
        font-size: 14px;
        transition: color 0.3s ease;
        display: inline-block;

        &:hover {
          color: #004C66;
        }

        &.is-active {
          color: #004C66;
        }

        .tab-label {
          font-size: 18px;
          font-family: 'PingFang Bold';
        }

        .tab-count {
          color: #999;
          font-size: 12px;
        }
      }

      .sliding-underline {
        position: absolute;
        bottom: 0;
        height: 2px;
        background: #004C66;
        transition: all 0.3s ease;
        border-radius: 1px;
      }
    }
  }
  
  .table-container {
    margin-bottom: 16px;
  }
  
  .pagination-container {
    display: flex;
    justify-content: flex-end;
    padding-top: 16px;
    border-top: 1px solid #e5e5e5;
  }

  .action-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0;

    .el-button {
      padding: 0;
      font-size: 14px;
      height: auto;
      min-height: auto;

      &:hover {
        background: none;
      }
    }

    .action-divider {
      color: #e5e5e5;
      font-size: 12px;
      margin: 0 8px;
      user-select: none;
    }
  }

  .tab-right-buttons {
    display: flex;
    align-items: center;
    gap: 8px;

    .el-button {
      margin: 0;
    }
  }

  // 修复表格样式问题
  :deep(.el-table) {
    // 修复固定列层级问题
    .el-table__fixed-right {
      z-index: 3 !important;
    }

    .el-table__fixed-left {
      z-index: 3 !important;
    }

    // 设置鼠标悬停时的背景色
    .el-table__body tr:hover > td {
      background-color: #f5f7fa !important;
    }

    // 确保固定列在悬停时也有正确的背景色
    .el-table__fixed-right .el-table__body tr:hover > td,
    .el-table__fixed-left .el-table__body tr:hover > td {
      background-color: #f5f7fa !important;
    }

    // 修复固定列边框问题
    .el-table__fixed-right::before,
    .el-table__fixed-left::before {
      z-index: 4 !important;
    }
  }
}
</style>
