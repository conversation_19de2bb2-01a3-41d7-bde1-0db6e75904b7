/**
 * 懒加载组件工具
 * 用于实现路由级别的代码分割和组件懒加载
 */

import { defineAsyncComponent, type Component, type AsyncComponentLoader } from 'vue'
import type { RouteComponent } from 'vue-router'

// 加载状态组件
const LoadingComponent = {
  template: `
    <div class="lazy-loading">
      <div class="loading-spinner">
        <div class="spinner"></div>
        <p>页面加载中...</p>
      </div>
    </div>
  `,
  style: `
    <style scoped>
    .lazy-loading {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 200px;
      flex-direction: column;
    }
    
    .loading-spinner {
      text-align: center;
    }
    
    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #409eff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 16px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .lazy-loading p {
      color: #666;
      font-size: 14px;
      margin: 0;
    }
    </style>
  `
}

// 错误状态组件
const ErrorComponent = {
  template: `
    <div class="lazy-error">
      <div class="error-content">
        <h3>页面加载失败</h3>
        <p>请检查网络连接或刷新页面重试</p>
        <button @click="retry" class="retry-btn">重新加载</button>
      </div>
    </div>
  `,
  methods: {
    retry() {
      window.location.reload()
    }
  },
  style: `
    <style scoped>
    .lazy-error {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 200px;
      padding: 20px;
    }
    
    .error-content {
      text-align: center;
      max-width: 400px;
    }
    
    .error-content h3 {
      color: #f56c6c;
      margin-bottom: 12px;
    }
    
    .error-content p {
      color: #666;
      margin-bottom: 20px;
      line-height: 1.5;
    }
    
    .retry-btn {
      background: #409eff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }
    
    .retry-btn:hover {
      background: #66b1ff;
    }
    </style>
  `
}

/**
 * 懒加载配置选项
 */
interface LazyLoadOptions {
  /** 加载延迟时间（毫秒），用于显示加载状态 */
  delay?: number
  /** 超时时间（毫秒） */
  timeout?: number
  /** 是否显示加载状态 */
  showLoading?: boolean
  /** 是否显示错误状态 */
  showError?: boolean
  /** 自定义加载组件 */
  loadingComponent?: Component
  /** 自定义错误组件 */
  errorComponent?: Component
  /** 重试次数 */
  retries?: number
}

/**
 * 创建懒加载组件
 * @param loader 组件加载器函数
 * @param options 懒加载配置选项
 * @returns 异步组件
 */
export function createLazyComponent(
  loader: AsyncComponentLoader,
  options: LazyLoadOptions = {}
): Component {
  const {
    delay = 200,
    timeout = 10000,
    showLoading = true,
    showError = true,
    loadingComponent = LoadingComponent,
    errorComponent = ErrorComponent,
    retries = 3
  } = options

  // 带重试机制的加载器
  const loaderWithRetry = async (): Promise<Component> => {
    let lastError: Error
    
    for (let i = 0; i <= retries; i++) {
      try {
        // console.log(`🔄 尝试加载组件 (第${i + 1}次)...`)
        const component = await loader()
        console.log('✅ 组件加载成功')
        return component
      } catch (error) {
        lastError = error as Error
        console.warn(`❌ 组件加载失败 (第${i + 1}次):`, error)
        
        // 如果不是最后一次重试，等待一段时间后重试
        if (i < retries) {
          await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
        }
      }
    }
    
    throw lastError!
  }

  return defineAsyncComponent({
    loader: loaderWithRetry,
    loadingComponent: showLoading ? loadingComponent : undefined,
    errorComponent: showError ? errorComponent : undefined,
    delay,
    timeout
  })
}

/**
 * 创建路由懒加载组件（简化版，避免 defineAsyncComponent 警告）
 * @param importFn 动态导入函数
 * @param chunkName 代码块名称（用于webpack命名）
 * @returns 路由组件
 */
export function createRouteComponent(
  importFn: () => Promise<any>,
  chunkName?: string
): RouteComponent {
  // 直接返回动态导入函数，避免 defineAsyncComponent 警告
  return () => {
    console.log(`📦 开始加载路由组件${chunkName ? ` (${chunkName})` : ''}...`)
    return importFn().then(module => {
      console.log(`✅ 路由组件${chunkName ? ` (${chunkName})` : ''}加载成功`)
      return module
    }).catch(error => {
      console.error(`❌ 路由组件${chunkName ? ` (${chunkName})` : ''}加载失败:`, error)
      throw error
    })
  }
}

/**
 * 预加载路由组件
 * @param importFn 动态导入函数
 * @returns Promise<void>
 */
export function preloadRouteComponent(importFn: () => Promise<any>): Promise<void> {
  return importFn()
    .then(() => {
      console.log('✅ 路由组件预加载成功')
    })
    .catch((error) => {
      console.warn('❌ 路由组件预加载失败:', error)
    })
}

/**
 * 批量预加载路由组件
 * @param importFns 动态导入函数数组
 * @returns Promise<void[]>
 */
export function preloadRouteComponents(importFns: (() => Promise<any>)[]): Promise<void[]> {
  const preloadPromises = importFns.map(importFn => 
    preloadRouteComponent(importFn)
  )
  
  return Promise.all(preloadPromises)
}

/**
 * 智能预加载策略
 * 根据用户行为和网络状况智能预加载组件
 */
export class SmartPreloader {
  private preloadedRoutes: Set<string> = new Set()
  private preloadQueue: (() => Promise<any>)[] = []
  private isPreloading = false

  /**
   * 添加到预加载队列
   * @param routeName 路由名称
   * @param importFn 动态导入函数
   */
  addToQueue(routeName: string, importFn: () => Promise<any>): void {
    if (this.preloadedRoutes.has(routeName)) {
      return
    }

    this.preloadQueue.push(importFn)
    this.preloadedRoutes.add(routeName)
    
    // 如果当前没有在预加载，开始预加载
    if (!this.isPreloading) {
      this.processQueue()
    }
  }

  /**
   * 处理预加载队列
   */
  private async processQueue(): Promise<void> {
    if (this.preloadQueue.length === 0 || this.isPreloading) {
      return
    }

    this.isPreloading = true

    while (this.preloadQueue.length > 0) {
      const importFn = this.preloadQueue.shift()!
      
      try {
        // 检查网络状况，如果网络较慢则延迟预加载
        if (this.isSlowNetwork()) {
          await new Promise(resolve => setTimeout(resolve, 2000))
        }
        
        await importFn()
        console.log('✅ 智能预加载完成一个组件')
        
        // 每次预加载后稍作延迟，避免影响主要功能
        await new Promise(resolve => setTimeout(resolve, 100))
      } catch (error) {
        console.warn('❌ 智能预加载失败:', error)
      }
    }

    this.isPreloading = false
  }

  /**
   * 检查是否为慢网络
   * @returns boolean
   */
  private isSlowNetwork(): boolean {
    // 检查网络连接信息（如果浏览器支持）
    const connection = (navigator as any).connection
    if (connection) {
      // 如果是2G或慢3G，认为是慢网络
      return connection.effectiveType === '2g' || connection.effectiveType === 'slow-2g'
    }
    
    // 默认认为不是慢网络
    return false
  }

  /**
   * 清空预加载队列
   */
  clearQueue(): void {
    this.preloadQueue = []
    this.isPreloading = false
  }
}

// 导出默认智能预加载器实例
export const smartPreloader = new SmartPreloader()

/**
 * 路由懒加载辅助函数集合
 */
export const LazyLoadHelpers = {
  /**
   * 创建视图组件懒加载
   */
  view: (path: string) => createRouteComponent(
    () => import(`../views/${path}.vue`),
    `view-${path.replace('/', '-')}`
  ),
  
  /**
   * 创建组件懒加载
   */
  component: (path: string) => createLazyComponent(
    () => import(`../components/${path}.vue`)
  ),
  
  /**
   * 创建布局组件懒加载
   */
  layout: (path: string) => createRouteComponent(
    () => import(`../layout/${path}.vue`),
    `layout-${path}`
  )
}