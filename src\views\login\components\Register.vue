<template>
  <!-- 注册表单 -->
  <el-form
    ref="registerFormRef"
    :model="registerForm"
    :rules="registerRules"
    :validate-on-rule-change="false"
    class="login-form"
  >
    <!-- 手机号输入框 -->
    <el-form-item prop="phone">
      <el-input
        v-model="registerForm.phone"
        placeholder="请输入手机号"
        size="large"
        class="login-input"
        maxlength="11"
      />
    </el-form-item>

    <!-- 验证码输入框 -->
    <el-form-item prop="smsCode">
      <el-input
        v-model="registerForm.smsCode"
        placeholder="请输入验证码"
        size="large"
        class="login-input sms-input-with-button"
        maxlength="6"
      >
        <template #suffix>
          <span
            class="sms-text-button"
            :class="{ disabled: isSmsButtonDisabled() }"
            @click="handleSendSms"
          >
            {{ getSmsButtonText() }}
          </span>
        </template>
      </el-input>
    </el-form-item>

    <!-- 密码输入框 -->
    <el-form-item prop="password">
      <el-input
        v-model="registerForm.password"
        type="password"
        placeholder="请输入密码"
        size="large"
        class="login-input"
        show-password
      />
    </el-form-item>

    <!-- 确认密码输入框 -->
    <el-form-item prop="confirmPassword">
      <el-input
        v-model="registerForm.confirmPassword"
        type="password"
        placeholder="请再次输入密码"
        size="large"
        class="login-input"
        show-password
      />
    </el-form-item>

    <!-- 用户协议和隐私政策 -->
    <el-form-item prop="agreeTerms" class="agreement-item">
      <el-checkbox v-model="registerForm.agreeTerms" class="agreement-checkbox">
        我已阅读并同意
        <a href="#" class="agreement-link" @click.prevent="showServiceAgreement">
          《平台服务协议》
        </a>
        和
        <a href="#" class="agreement-link" @click.prevent="showPrivacyPolicy">
          《隐私政策》
        </a>
      </el-checkbox>
    </el-form-item>

    <!-- 注册按钮 -->
    <el-form-item>
      <el-button
        type="primary"
        size="large"
        class="login-button"
        :loading="registerLoading"
        @click="handleRegister"
      >
        立即注册
      </el-button>
    </el-form-item>

    <!-- 登录链接 -->
    <div class="login-link">
      <span>已有账号？</span>
      <a href="#" @click.prevent="handleBackToLogin">立即登录</a>
    </div>
  </el-form>

  <!-- 服务协议弹窗 -->
  <Modal
    v-model="serviceAgreementVisible"
    title="平台服务协议"
    width="600px"
    :show-footer="false"
  >
    <div class="agreement-content">
      <p>这里是平台服务协议的内容，后续会添加具体条款...</p>
    </div>
  </Modal>

  <!-- 隐私政策弹窗 -->
  <Modal
    v-model="privacyPolicyVisible"
    title="隐私政策"
    width="600px"
    :show-footer="false"
  >
    <div class="agreement-content">
      <p>这里是隐私政策的内容，后续会添加具体条款...</p>
    </div>
  </Modal>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from "element-plus";
import type { FormInstance, FormRules } from 'element-plus'
import { useSms } from '../composables/useSms'
import { authApi } from '@/utils/api'
import Modal from '@/components/Modal.vue'

// 定义组件事件
const emit = defineEmits<{
  switchTab: [tab: string]
  registerSuccess: []
}>()

// 使用短信验证码组合式函数
const { sendSms, getSmsButtonText, isSmsButtonDisabled } = useSms()

// 注册加载状态
const registerLoading = ref(false)

// 弹窗显示状态
const serviceAgreementVisible = ref(false)
const privacyPolicyVisible = ref(false)

// 表单引用
const registerFormRef = ref<FormInstance>()

// 注册表单数据
const registerForm = reactive({
  phone: '', // 手机号
  smsCode: '', // 短信验证码
  password: '', // 密码
  confirmPassword: '', // 确认密码
  agreeTerms: false // 是否同意协议
})

// 确认密码验证器
const validateConfirmPassword = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 协议同意验证器
const validateAgreeTerms = (rule: any, value: boolean, callback: any) => {
  if (!value) {
    callback(new Error('请先同意用户协议和隐私政策'))
  } else {
    callback()
  }
}

// 注册验证规则
const registerRules: FormRules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号格式',
      trigger: 'blur'
    }
  ],
  smsCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    {
      pattern: /^\d{4}$/,
      message: '验证码为4位数字',
      trigger: 'blur'
    }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20位', trigger: 'blur' },
    {
      pattern: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/,
      message: '密码必须包含字母和数字',
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, validator: validateConfirmPassword, trigger: 'blur' }
  ],
  agreeTerms: [
    { required: true, validator: validateAgreeTerms, trigger: 'change' }
  ]
}

/**
 * 发送短信验证码
 */
const handleSendSms = async () => {
  // 先验证手机号
  if (!registerFormRef.value) return

  try {
    await registerFormRef.value.validateField('phone')
    // 调用公共的发送短信方法
    await sendSms(registerForm.phone)
  } catch (error) {
    // 验证失败，不发送短信
    console.log('手机号验证失败')
  }
}

/**
 * 处理注册
 */
const handleRegister = async () => {
  if (!registerFormRef.value) return

  try {
    // 表单验证
    await registerFormRef.value.validate()

    registerLoading.value = true

    // 调用注册API
    const response = await authApi.register({
      mobile: registerForm.phone,
      password: registerForm.password,
      yzcode: registerForm.smsCode
    })

    if (response.code === 1) {
      // 注册成功
      ElMessage.success('注册成功，请登录')
      emit('registerSuccess')
      // 切换到密码登录页面
      emit('switchTab', 'password')
    } else {
      ElMessage.error(response.msg || '注册失败，请重试')
    }
  } catch (error) {
    console.error('注册失败:', error)
    ElMessage.error('注册失败，请重试')
  } finally {
    registerLoading.value = false
  }
}

/**
 * 显示服务协议
 */
const showServiceAgreement = () => {
  serviceAgreementVisible.value = true
}

/**
 * 显示隐私政策
 */
const showPrivacyPolicy = () => {
  privacyPolicyVisible.value = true
}

/**
 * 返回登录页面
 */
const handleBackToLogin = () => {
  emit('switchTab', 'password')
}
</script>

<style lang="scss" scoped>
.login-form {
  .el-form-item {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .password {
    margin-bottom: 8px;
  }

  .user-type-select,
  .login-input {
    width: 100%;
    height: 46px;
  }

  .sms-input-with-button {
    :deep(.el-input__suffix) {
      padding-right: 0px;
    }
  }

  .sms-text-button {
    color: #004c66;
    font-size: 12px;
    font-weight: 400;
    cursor: pointer;
    user-select: none;
    transition: opacity 0.3s ease;

    &:hover:not(.disabled) {
      opacity: 0.8;
    }

    &.disabled {
      color: #ccc;
      cursor: not-allowed;
    }
  }

  .agreement-item {
    margin-bottom: 32px;

    .agreement-checkbox {
      :deep(.el-checkbox__label) {
        color: #666;
        font-size: 14px;
        line-height: 1.5;
      }

      :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
        background-color: #004c66 !important;
        border-color: #004c66;
      }

      .agreement-link {
        color: #004c66;
        text-decoration: none;
        margin: 0 2px;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

  .login-button {
    width: 100%;
    height: 48px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;

    &:hover {
      background: rgba($color: #004c66, $alpha: 0.9);
    }
  }
}

.login-link {
  text-align: center;
  margin-top: 24px;
  color: #666;
  font-size: 14px;

  a {
    color: #004c66;
    text-decoration: none;
    margin-left: 4px;

    &:hover {
      text-decoration: underline;
      background-color: transparent;
    }
  }
}

.agreement-content {
  max-height: 400px;
  overflow-y: auto;
  padding: 20px;
  line-height: 1.6;

  h3 {
    color: #004c66;
    margin-bottom: 16px;
  }

  p {
    margin-bottom: 12px;
    color: #666;
  }
}
</style>