import { ref, onUnmounted } from 'vue';
import type { ChujiaiItem, FayanItem, BiaodInfo } from '../types/auctionDetail';

/**
 * WebSocket连接管理 Composable
 * 用于管理拍卖详情页面的WebSocket连接和消息处理
 */
export function useWebSocket() {
  // WebSocket相关响应式数据
  const websock = ref<WebSocket | null>(null); // WebSocket连接实例
  const isOnline = ref<boolean>(false); // WebSocket连接状态
  const jishi = ref<number | null>(null); // 心跳定时器
  const audioRef = ref<HTMLAudioElement | null>(null); // 音频元素引用

  /**
   * 初始化WebSocket连接
   * @param targetId 标的ID
   * @param pmhId 拍卖会ID
   * @param userId 用户ID
   * @param onMessageCallback 消息处理回调函数
   * @param getHeartbeatData 获取心跳数据的回调函数
   */
  const initWebSocket = (
    targetId: number,
    pmhId: number,
    userId: number,
    onMessageCallback: (data: any) => void,
    getHeartbeatData?: () => { fayanId: number; chujiaiId: number }
  ): void => {
    try {
      // 创建WebSocket连接
      websock.value = new WebSocket('wss://mai-wss.hghello.com/wss');
      
      // 设置消息接收处理
      websock.value.onmessage = (event) => onMessage(event, onMessageCallback);
      
      // 设置连接成功处理
      websock.value.onopen = () => onOpen(targetId, pmhId, userId, getHeartbeatData);
      
      // 设置连接错误处理
      websock.value.onerror = onError;
      
      // 设置连接关闭处理
      websock.value.onclose = onClose;
    } catch (error) {
      console.error('WebSocket初始化失败:', error);
    }
  };

  /**
   * 处理接收到的WebSocket消息
   * @param event WebSocket消息事件
   * @param callback 消息处理回调函数
   */
  const onMessage = (event: MessageEvent, callback: (data: any) => void): void => {
    try {
      const data = JSON.parse(event.data);
      // console.log('WebSocket收到消息:', data); 已移除
      
      // 调用外部传入的回调函数处理消息
      callback(data);
      
      // 根据消息类型播放提示音
      if (data.pmh_type === 'chujia' || data.bd_status) {
        const audioUrl = 'https://huigupaimai.oss-cn-beijing.aliyuncs.com/audio/chujia.mp3';
        playAudio(audioUrl);
      }
    } catch (error) {
      console.error('WebSocket消息解析失败:', error);
    }
  };

  /**
   * WebSocket连接成功处理
   * @param targetId 标的ID
   * @param pmhId 拍卖会ID
   * @param userId 用户ID
   * @param getHeartbeatData 获取心跳数据的回调函数
   */
  const onOpen = (
    targetId: number, 
    pmhId: number, 
    userId: number,
    getHeartbeatData?: () => { fayanId: number; chujiaiId: number }
  ): void => {
    // console.log('WebSocket连接成功'); 已移除
    isOnline.value = true;
    
    // 启动心跳定时器，每秒发送一次心跳数据
    jishi.value = setInterval(() => {
      const heartbeatData = getHeartbeatData ? getHeartbeatData() : { fayanId: 0, chujiaiId: 0 };
      
      websocketsend({
        pmh_id: pmhId,
        bd_id: targetId,
        user_id: userId,
        fayan_id: heartbeatData.fayanId,
        chujia_id: heartbeatData.chujiaiId,
      });
    }, 1000);
  };

  /**
   * WebSocket连接错误处理
   */
  const onError = (): void => {
    console.error('WebSocket连接错误');
    isOnline.value = false;
  };

  /**
   * WebSocket连接关闭处理
   */
  const onClose = (): void => {
    // console.log('WebSocket连接已关闭'); 已移除
    isOnline.value = false;
    
    // 清除心跳定时器
    if (jishi.value) {
      clearInterval(jishi.value);
      jishi.value = null;
    }
    
    // 清空WebSocket实例
    websock.value = null;
  };

  /**
   * 发送WebSocket消息
   * @param data 要发送的数据
   */
  const websocketsend = (data: any): void => {
    try {
      if (websock.value && websock.value.readyState === WebSocket.OPEN) {
        const message = JSON.stringify(data);
        websock.value.send(message);
        // console.log('WebSocket发送消息:', message); 已移除
      } else {
        // console.warn('WebSocket未连接，无法发送消息'); 已移除
      }
    } catch (error) {
      console.error('WebSocket发送消息失败:', error);
    }
  };

  /**
   * 播放音频提示
   * @param audioUrl 音频文件URL
   */
  const playAudio = (audioUrl: string): void => {
    try {
      if (audioRef.value) {
        // 使用模板中的audio元素播放默认提示音
        audioRef.value.play().catch(error => {
          // console.warn('音频播放失败:', error); 已移除
        });
      } else {
        // 如果audioRef不可用，则创建新的Audio对象
        const audio = new Audio(audioUrl);
        audio.play().catch(error => {
          // console.warn('音频播放失败:', error); 已移除
        });
      }
    } catch (error) {
      // console.warn('音频播放失败:', error); 已移除
    }
  };

  /**
   * 关闭WebSocket连接并清理资源
   */
  const closeWebSocket = (): void => {
    // 清除心跳定时器
    if (jishi.value) {
      clearInterval(jishi.value);
      jishi.value = null;
    }
    
    // 关闭WebSocket连接
    if (websock.value) {
      websock.value.close();
      websock.value = null;
    }
    
    isOnline.value = false;
  };

  // 组件卸载时自动清理资源
  onUnmounted(() => {
    closeWebSocket();
  });

  return {
    // 响应式数据
    websock,
    isOnline,
    jishi,
    audioRef,
    
    // 方法
    initWebSocket,
    websocketsend,
    playAudio,
    closeWebSocket,
  };
}