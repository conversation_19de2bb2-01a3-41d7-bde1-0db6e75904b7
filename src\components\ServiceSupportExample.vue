<template>
  <div>
    <!-- 服务支持组件使用示例 -->
    <ServiceSupport
      :title="supportConfig.title"
      :placeholder="supportConfig.placeholder"
      :navigation-items="supportConfig.navigationItems"
      :on-search="handleSearch"
      :on-get-questions="handleGetQuestions"
      :on-helpful-feedback="handleHelpfulFeedback"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import ServiceSupport from './ServiceSupport.vue'

// 定义导航项目接口
interface NavigationItem {
  icon: string
  text: string
}

// 定义问题接口
interface Question {
  title: string
  answer: string
}

// 配置服务支持组件的参数
const supportConfig = reactive({
  title: '嗨！有什么需要帮忙的吗？',
  placeholder: '请输入您要搜索的问题关键词',
  navigationItems: [
    { icon: 'common-problem', text: '常见问题' },
    { icon: 'account-help', text: '账户帮助' },
    { icon: 'payment-help', text: '支付帮助' },
    { icon: 'auction-help', text: '竞拍帮助' },
    { icon: 'contact-us', text: '联系我们' }
  ] as NavigationItem[]
})

// 模拟问题数据
const mockQuestions: Question[][] = [
  // 常见问题
  [
    {
      title: '如何注册账户？',
      answer: '您可以点击页面右上角的"注册"按钮，填写手机号码和验证码即可完成注册。注册过程简单快捷，只需要几分钟时间。'
    },
    {
      title: '忘记密码怎么办？',
      answer: '如果您忘记了密码，可以在登录页面点击"忘记密码"链接，通过手机验证码重置密码。系统会发送验证码到您注册时使用的手机号码。'
    },
    {
      title: '如何参与竞拍？',
      answer: '参与竞拍需要先完成实名认证和缴纳保证金。认证通过后，您就可以在竞拍页面出价参与竞拍了。请注意竞拍规则和时间限制。'
    }
  ],
  // 账户帮助
  [
    {
      title: '如何完成实名认证？',
      answer: '进入个人中心，点击"实名认证"，上传身份证正反面照片和手持身份证照片，填写真实姓名和身份证号码，提交后等待审核即可。'
    },
    {
      title: '如何修改个人信息？',
      answer: '登录后进入个人中心，在"个人信息"页面可以修改头像、昵称、联系方式等信息。部分重要信息修改可能需要验证码确认。'
    }
  ],
  // 支付帮助
  [
    {
      title: '支持哪些支付方式？',
      answer: '我们支持支付宝、微信支付、银行卡支付等多种支付方式。您可以根据自己的需要选择合适的支付方式。'
    },
    {
      title: '支付失败怎么办？',
      answer: '如果支付失败，请检查网络连接和支付账户余额。如果问题持续存在，请联系客服或尝试更换支付方式。'
    }
  ],
  // 竞拍帮助
  [
    {
      title: '竞拍保证金如何缴纳？',
      answer: '在参与竞拍前，需要先缴纳相应的保证金。保证金金额根据拍品价值确定，竞拍结束后会自动退还给未中标用户。'
    },
    {
      title: '竞拍成功后如何付款？',
      answer: '竞拍成功后，系统会发送付款通知。您需要在规定时间内完成尾款支付，逾期未付款将视为违约，保证金不予退还。'
    }
  ],
  // 联系我们
  [
    {
      title: '客服工作时间？',
      answer: '我们的客服工作时间为周一至周日 9:00-18:00。您可以通过在线客服、电话或邮件联系我们。'
    },
    {
      title: '如何联系客服？',
      answer: '您可以通过以下方式联系我们：\n1. 在线客服：点击页面右下角的客服图标\n2. 客服电话：400-123-4567\n3. 客服邮箱：<EMAIL>'
    }
  ]
]

/**
 * 处理搜索功能
 * @param keyword 搜索关键词
 */
const handleSearch = (keyword: string) => {
  // console.log('搜索关键词:', keyword)
  // 这里应该调用实际的搜索API
  // 例如：searchAPI(keyword)
  
  // 模拟搜索请求
  // 实际项目中，这里应该是调用后端搜索接口
  alert(`正在搜索: ${keyword}`)
}

/**
 * 获取指定导航的问题列表
 * @param navIndex 导航索引
 * @returns Promise<Question[]> 问题列表
 */
const handleGetQuestions = async (navIndex: number): Promise<Question[]> => {
  console.log('获取问题列表，导航索引:', navIndex)
  
  // 模拟API请求延迟
  await new Promise(resolve => setTimeout(resolve, 500))
  
  // 这里应该调用实际的API获取问题列表
  // 例如：const response = await getQuestionsAPI(navIndex)
  // return response.data
  
  // 返回模拟数据
  return mockQuestions[navIndex] || []
}

/**
 * 处理用户对问题答案的帮助反馈
 * @param questionIndex 问题索引
 * @param isHelpful 是否有帮助
 */
const handleHelpfulFeedback = (questionIndex: number, isHelpful: boolean) => {
  console.log('用户反馈:', { questionIndex, isHelpful })
  
  // 这里应该调用实际的反馈API
  // 例如：submitFeedbackAPI(questionIndex, isHelpful)
  
  // 显示反馈结果
  const message = isHelpful ? '感谢您的反馈！' : '我们会继续改进，感谢您的建议！'
  alert(message)
}
</script>

<style scoped>
/* 示例组件不需要额外样式 */
</style>