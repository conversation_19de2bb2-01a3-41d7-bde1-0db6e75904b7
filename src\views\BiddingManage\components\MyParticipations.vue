<template>
  <div class="my-participations">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>参与标的</h2>
      <p>共 {{ total }} 条参与记录</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-text">加载中...</div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!loading && auctionList.length === 0" class="empty-container">
      <div class="empty-text">暂无参与记录</div>
    </div>

    <!-- 拍卖卡片列表 -->
    <div v-else class="auction-grid">
      <BiddingCard
        v-for="item in auctionList"
        :key="item.id"
        v-bind="item"
        button-type="record"
        @card-click="handleCardClick"
        @button-click="handleViewRecord"
      />
    </div>

    <!-- 分页组件 -->
    <div v-if="total > 0" class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        layout="prev, pager, next, jumper"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from "element-plus";
import BiddingCard from '@/components/BiddingCard.vue'
import { userApi } from '@/utils/api'
import type { AuctionItem } from '@/types/auction'
import { useUserStore } from "@/stores/user";

const router = useRouter()

// 用户状态管理
const userStore = useUserStore();

// 响应式数据
const loading = ref(false)
const auctionList = ref<AuctionItem[]>([])
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)

/**
 * 获取参与标的列表数据
 */
const fetchParticipations = async () => {
  try {
    loading.value = true
    
    // 从缓存中获取用户信息
    const userInfo = userStore.userInfo;
    if (!userInfo?.id) {
      ElMessage.error('请先登录')
      return
    }

    const params = {
      member_id: userInfo.id,
      page: currentPage.value,
      type: 1
    }

    const response = await userApi.getMyParticipatedAuctions(params)
    
    if (response.code === 1) {
      // 创建Promise数组，用于并行获取详细数据
      const promises = response.data.data.map(async (item: any) => {
        const obj: AuctionItem = {
          id: item.id,
          pmhId: item.pmh_id || 0,
          bdName: item.bd_title,
          startTime: item.start_time_name,
          endTime: item.end_time_name,
          bdPic: "https://huigupaimai.oss-cn-beijing.aliyuncs.com/" + item.bd_url,
          bdQipaijia: item.bd_qipaijia,
          qpjDanwie: item.qpj_danwie,
          bdWeiguan: item.bd_weiguan,
          timeLabel: "截止报名",
          scheduleLabel: "预计开始",
          status: item.bd_status,
        };
        return obj;
      });

      // 等待所有异步操作完成，获取实际的数据数组
      const list = await Promise.all(promises);
      
      auctionList.value = list
      total.value = response.data.total || 0
    } else {
      ElMessage.error(response.msg || '获取参与列表失败')
    }
  } catch (error) {
    console.error('获取参与列表失败:', error)
    ElMessage.error('获取参与列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 处理页码变化
 */
const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchParticipations()
}

/**
 * 处理卡片点击事件
 */
const handleCardClick = (data: { productId: string; pmhId: string }) => {
  // 跳转到拍卖详情页
  router.push({
    name: 'auctionDetail',
    query: { id: data.productId }
  })
}

/**
 * 处理查看出价记录
 */
const handleViewRecord = (data: { productId: string; pmhId: string; type: string }) => {
  // TODO: 实现查看出价记录功能
  ElMessage.info('查看出价记录功能待实现')
  // console.log('查看出价记录:', data)
}

// 组件挂载时获取数据
onMounted(() => {
  fetchParticipations()
})
</script>

<style scoped lang="scss">
.my-participations {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;
    
    h2 {
      font-size: 24px;
      color: #333;
      margin: 0 0 8px 0;
    }
    
    p {
      font-size: 14px;
      color: #666;
      margin: 0;
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    
    .loading-text {
      font-size: 16px;
      color: #666;
    }
  }

  .empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    
    .empty-text {
      font-size: 16px;
      color: #999;
    }
  }

  .auction-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(295px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 30px;
  }
}
</style>