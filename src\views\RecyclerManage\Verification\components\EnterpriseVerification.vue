<template>
  <div class="enterprise-verification">
    <div class="form-section">
      <!-- 企业名称 -->
      <div class="form-item">
        <label class="form-label">企业名称</label>
        <div class="form-value">
          <el-input 
            v-model="enterpriseForm.enterpriseName" 
            placeholder="请输入企业名称" 
            class="form-input" 
          />
        </div>
      </div>

      <!-- 统一社会信用代码 -->
      <div class="form-item">
        <label class="form-label">统一社会信用代码</label>
        <div class="form-value">
          <el-input 
            v-model="enterpriseForm.creditCode" 
            placeholder="请输入统一社会信用代码" 
            class="form-input" 
          />
        </div>
      </div>

      <!-- 业务联系人 -->
      <div class="form-item">
        <label class="form-label">业务联系人</label>
        <div class="form-value">
          <el-input 
            v-model="enterpriseForm.relationUser" 
            placeholder="请输入业务联系人" 
            class="form-input" 
          />
        </div>
      </div>

      <!-- 联系电话 -->
      <div class="form-item">
        <label class="form-label">联系电话</label>
        <div class="form-value">
          <el-input 
            v-model="enterpriseForm.relationPhone" 
            placeholder="请输入联系电话" 
            class="form-input" 
          />
        </div>
      </div>

      <!-- 法人真实姓名 -->
      <div class="form-item">
        <label class="form-label">法人真实姓名</label>
        <div class="form-value">
          <el-input 
            v-model="enterpriseForm.legalName" 
            placeholder="请输入法人真实姓名" 
            class="form-input" 
          />
        </div>
      </div>

      <!-- 法人证件类型 -->
      <div class="form-item">
        <label class="form-label">法人证件类型</label>
        <div class="form-value">
          <el-radio-group v-model="enterpriseForm.cartType">
            <el-radio :value="1">身份证</el-radio>
          </el-radio-group>
        </div>
      </div>

      <!-- 法人证件号 -->
      <div class="form-item">
        <label class="form-label">法人证件号</label>
        <div class="form-value">
          <el-input 
            v-model="enterpriseForm.cartId" 
            placeholder="请输入法人证件号" 
            class="form-input" 
          />
        </div>
      </div>

      <!-- 营业执照照片 -->
      <div class="form-item">
        <label class="form-label">营业执照照片</label>
        <div class="form-value">
          <div class="upload-group">
            <div class="upload-item">
              <NewUploadCard
                upload-text="上传营业执照"
                :background-image="businessLicenseBg"
                :image-url="enterpriseForm.businessLicense"
                @upload-success="handleBusinessLicenseUpload"
              />
              <span class="upload-label">营业执照</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 身份证正反面照片 -->
      <div class="form-item">
        <label class="form-label">身份证正反面照片</label>
        <div class="form-value">
          <div class="upload-group">
            <div class="upload-item">
              <NewUploadCard
                upload-text="上传身份证正面"
                :background-image="idCardFrontBg"
                :image-url="enterpriseForm.idCardFront"
                @upload-success="handleIdCardFrontUpload"
              />
              <span class="upload-label">身份证正面</span>
            </div>
            <div class="upload-item">
              <NewUploadCard
                upload-text="上传身份证反面"
                :background-image="idCardBackBg"
                :image-url="enterpriseForm.idCardBack"
                @upload-success="handleIdCardBackUpload"
              />
              <span class="upload-label">身份证反面</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 企业logo -->
      <div class="form-item">
        <label class="form-label">企业logo</label>
        <div class="form-value">
          <div class="upload-group">
            <div class="upload-item">
              <NewUploadCard
                upload-text="上传企业logo"
                :image-url="enterpriseForm.companyLogo"
                @upload-success="handleCompanyLogoUpload"
              />
              <span class="upload-label">企业logo</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 企业简介 -->
      <div class="form-item">
        <label class="form-label">企业简介</label>
        <div class="form-value">
          <el-input 
            v-model="enterpriseForm.description" 
            type="textarea"
            placeholder="请输入企业简介" 
            :rows="4"
            class="form-input" 
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import NewUploadCard from '@/components/NewUploadCard.vue'
import { systemApi } from '@/utils/api-new'

// 定义组件事件接口
interface Emits {
  (e: 'save', data: any): void
  (e: 'submit', data: any): void
}

// 定义组件事件
const emit = defineEmits<Emits>()

// 企业认证表单数据
const enterpriseForm = reactive({
  enterpriseName: '',
  creditCode: '',
  relationUser: '',
  relationPhone: '',
  legalName: '',
  cartType: 1,
  cartId: '',
  businessLicense: '', // 营业执照图片URL
  idCardFront: '', // 身份证正面图片URL
  idCardBack: '', // 身份证反面图片URL
  companyLogo: '', // 企业logo图片URL
  description: '',
  review: 1,
})

// 背景图片
const businessLicenseBg = ref('@/assets/icons/upload/business-license.svg')
const idCardFrontBg = ref('@/assets/icons/upload/idcard-front.svg')
const idCardBackBg = ref('@/assets/icons/upload/idcard-reverse.svg')

// 加载状态
const loading = ref(false)

/**
 * 处理营业执照上传成功
 */
const handleBusinessLicenseUpload = (data: { url: string; file: File }) => {
  enterpriseForm.businessLicense = data.url
  console.log('营业执照上传成功:', data.url)
}

/**
 * 处理身份证正面上传成功
 */
const handleIdCardFrontUpload = (data: { url: string; file: File }) => {
  enterpriseForm.idCardFront = data.url
  console.log('身份证正面上传成功:', data.url)
}

/**
 * 处理身份证反面上传成功
 */
const handleIdCardBackUpload = (data: { url: string; file: File }) => {
  enterpriseForm.idCardBack = data.url
  console.log('身份证反面上传成功:', data.url)
}

/**
 * 处理企业logo上传成功
 */
const handleCompanyLogoUpload = (data: { url: string; file: File }) => {
  enterpriseForm.companyLogo = data.url
  console.log('企业logo上传成功:', data.url)
}

/**
 * 验证表单数据
 */
const validateForm = (): boolean => {
  if (!enterpriseForm.enterpriseName.trim()) {
    ElMessage.error('请输入企业名称')
    return false
  }

  if (!enterpriseForm.creditCode.trim()) {
    ElMessage.error('请输入统一社会信用代码')
    return false
  }

  if (!enterpriseForm.relationUser.trim()) {
    ElMessage.error('请输入业务联系人')
    return false
  }

  if (!enterpriseForm.relationPhone.trim()) {
    ElMessage.error('请输入联系电话')
    return false
  }

  if (!enterpriseForm.legalName.trim()) {
    ElMessage.error('请输入法人真实姓名')
    return false
  }

  if (!enterpriseForm.cartId.trim()) {
    ElMessage.error('请输入法人证件号')
    return false
  }

  if (!enterpriseForm.businessLicense) {
    ElMessage.error('请上传营业执照照片')
    return false
  }

  if (!enterpriseForm.idCardFront) {
    ElMessage.error('请上传身份证正面照片')
    return false
  }

  if (!enterpriseForm.idCardBack) {
    ElMessage.error('请上传身份证反面照片')
    return false
  }

  return true
}

/**
 * 构建提交数据
 */
const buildSubmitData = () => {
  // 构建附件列表
  const attachmentList = []

  if (enterpriseForm.businessLicense) {
    attachmentList.push({
      bizType: 'YYZZ',
      fileName: '营业执照.jpg',
      filePath: enterpriseForm.businessLicense,
      fileSize: '0',
      fileType: 'image'
    })
  }

  if (enterpriseForm.idCardFront) {
    attachmentList.push({
      bizType: 'SFZ',
      fileName: '身份证正面.jpg',
      filePath: enterpriseForm.idCardFront,
      fileSize: '0',
      fileType: 'image'
    })
  }

  if (enterpriseForm.idCardBack) {
    attachmentList.push({
      bizType: 'SFZ',
      fileName: '身份证反面.jpg',
      filePath: enterpriseForm.idCardBack,
      fileSize: '0',
      fileType: 'image'
    })
  }

  return {
    enterpriseName: enterpriseForm.enterpriseName.trim(),
    creditCode: enterpriseForm.creditCode.trim(),
    relationUser: enterpriseForm.relationUser.trim(),
    relationPhone: enterpriseForm.relationPhone.trim(),
    legalName: enterpriseForm.legalName.trim(),
    cartType: enterpriseForm.cartType,
    cartId: enterpriseForm.cartId.trim(),
    companyLogo: enterpriseForm.companyLogo,
    description: enterpriseForm.description.trim(),
    review: enterpriseForm.review,
    attachmentList
  }
}

/**
 * 处理保存操作
 */
const handleSave = () => {
  if (!validateForm()) return

  const submitData = buildSubmitData()
  emit('save', submitData)
}

/**
 * 处理提交审核操作
 */
const handleSubmit = () => {
  if (!validateForm()) return

  const submitData = buildSubmitData()
  emit('submit', submitData)
}

/**
 * 加载企业认证数据
 */
const loadEnterpriseAuthData = async (userId: string) => {
  loading.value = true

  try {
    const response = await systemApi.getEnterpriseAuth(userId)

    if (response && response.success && response.result) {
      const authData = response.result.hgyEnterpriseAuth
      const attachmentList = response.result.hgyAttachmentList

      if (authData) {
        Object.assign(enterpriseForm, {
          enterpriseName: authData.enterpriseName || '',
          creditCode: authData.creditCode || '',
          relationUser: authData.relationUser || '',
          relationPhone: authData.relationPhone || '',
          legalName: authData.legalName || '',
          cartType: authData.cartType || 1,
          cartId: authData.cartId || '',
          companyLogo: authData.companyLogo || '',
          description: authData.description || '',
          review: 1
        })
      }

      // 处理附件数据
      if (attachmentList && attachmentList.length > 0) {
        attachmentList.forEach((item: any) => {
          if (item.bizType === 'YYZZ') {
            enterpriseForm.businessLicense = item.filePath
          } else if (item.bizType === 'SFZ') {
            if (item.fileName.includes('正面')) {
              enterpriseForm.idCardFront = item.filePath
            } else {
              enterpriseForm.idCardBack = item.filePath
            }
          }
        })
      }
    }
  } catch (error) {
    console.error('加载企业认证数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 暴露方法给父组件调用
defineExpose({
  handleSave,
  handleSubmit,
  loadEnterpriseAuthData
})

onMounted(() => {
  // 组件挂载时可以加载数据
})
</script>

<style scoped lang="scss">
.enterprise-verification {
  .form-section {
    .form-item {
      padding: 20px 0;
      border-bottom: 1px solid;
      border-image: linear-gradient(to right, rgba(221, 221, 221, 1), rgba(255, 255, 255, 1));
      border-image-slice: 1;

      .form-label {
        font-size: 16px;
        color: #333;
        flex-shrink: 0;
        font-family: 'PingFang Bold';
        margin-bottom: 10px;
        display: block;
      }

      .form-value {
        flex: 1;

        .form-input {
          width: 100%;

          :deep(.el-input__wrapper) {
            border: none;
            box-shadow: none;
            background: transparent;
            padding: 0;

            .el-input__inner {
              font-size: 16px;
              color: #666;
              background: transparent;

              &::placeholder {
                color: #999;
                font-size: 16px;
              }
            }
          }

          :deep(.el-textarea__inner) {
            border: none;
            box-shadow: none;
            background: transparent;
            padding: 0;
            font-size: 16px;
            color: #666;
            resize: vertical;

            &::placeholder {
              color: #999;
              font-size: 16px;
            }
          }
        }

        :deep(.el-radio-group) {
          .el-radio {
            color: #666;
            font-size: 16px;

            .el-radio__label {
              color: #666;
            }
          }
        }

        .upload-group {
          display: flex;
          gap: 20px;
          flex-wrap: wrap;

          .upload-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;

            .upload-label {
              font-size: 12px;
              color: #666;
              text-align: center;
            }
          }
        }
      }
    }
  }
}
</style>
