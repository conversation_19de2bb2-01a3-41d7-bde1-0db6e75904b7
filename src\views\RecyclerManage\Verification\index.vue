<template>
  <div
    class="verification-container"
    v-loading="loading"
    element-loading-text="加载中..."
  >
    <!-- 顶部导航切换 -->
    <div class="verification-nav">
      <div
        class="nav-tab enterprise-nav-tab"
        :class="{ active: activeTab === 'enterprise' }"
        @click="switchTab('enterprise')"
      >
        企业认证
      </div>
      <div
        class="nav-tab personal-nav-tab"
        :class="{ active: activeTab === 'personal' }"
        @click="switchTab('personal')"
      >
        个人认证
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="verification-content">
      <!-- 企业认证组件 -->
      <EnterpriseVerification
        v-if="activeTab === 'enterprise'"
        ref="enterpriseVerificationRef"
        @save="handleEnterpriseSave"
        @submit="handleEnterpriseSubmit"
      />

      <!-- 个人认证组件 -->
      <PersonalVerification
        v-if="activeTab === 'personal'"
        ref="personalVerificationRef"
        @save="handlePersonalSave"
        @submit="handlePersonalSubmit"
      />

      <!-- 底部按钮 -->
      <div class="action-buttons">
        <button
          class="submit-btn"
          @click="handleSubmit"
          :disabled="submitLoading"
        >
          {{ submitLoading ? "提交中..." : "提交审核" }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { ElMessage } from "element-plus";
import { systemApi } from "@/utils/api-new";
import PersonalVerification from "./components/PersonalVerification.vue";
import EnterpriseVerification from "./components/EnterpriseVerification.vue";

// 用户信息接口
interface UserInfo {
  id?: string;
  username?: string;
  phone?: string;
  avatar?: string;
  realname?: string;
}

// 当前激活的标签页
const activeTab = ref<"enterprise" | "personal">("enterprise");

// 加载状态
const loading = ref(false);
const submitLoading = ref(false);

// 用户信息
const userInfo = ref<UserInfo>({});

// 子组件引用
const personalVerificationRef =
  ref<InstanceType<typeof PersonalVerification>>();
const enterpriseVerificationRef =
  ref<InstanceType<typeof EnterpriseVerification>>();

// 计算用户ID
const userId = computed(() => userInfo.value.id || "");

/**
 * 获取用户信息
 */
const getUserInfo = async () => {
  try {
    const response = await systemApi.getUserData();

    if (response && response.success && response.result) {
      userInfo.value = response.result;
    } else if (response) {
      userInfo.value = response as any;
    }

    console.log("获取到的用户信息:", userInfo.value);
  } catch (error) {
    console.error("获取用户信息失败:", error);
    ElMessage.error("获取用户信息失败");
  }
};

/**
 * 加载认证数据
 */
const loadAuthData = async () => {
  if (!userId.value) {
    console.warn("用户ID不存在，无法加载认证数据");
    return;
  }

  try {
    if (activeTab.value === "enterprise") {
      // 加载企业认证数据
      enterpriseVerificationRef.value?.loadEnterpriseAuthData(userId.value);
    } else {
      // 加载个人认证数据
      personalVerificationRef.value?.loadPersonalAuthData(userId.value);
    }
  } catch (error) {
    console.error("加载认证数据失败:", error);
  }
};

/**
 * 切换标签页
 * @param tab 标签页类型
 */
const switchTab = (tab: "enterprise" | "personal") => {
  activeTab.value = tab;
  // 切换标签页时重新加载对应的认证数据
  setTimeout(() => {
    loadAuthData();
  }, 100);
};

/**
 * 处理个人认证保存
 * @param data 个人认证数据
 */
const handlePersonalSave = async (data: any) => {
  try {
    const response = await systemApi.submitPersonalAuth(data);
    if (response.success) {
      ElMessage.success("保存成功");
    } else {
      ElMessage.error(response.message || "保存失败");
    }
  } catch (error) {
    console.error("保存失败:", error);
    ElMessage.error("保存失败");
  }
};

/**
 * 处理个人认证提交
 * @param data 个人认证数据
 */
const handlePersonalSubmit = async (data: any) => {
  try {
    const response = await systemApi.submitPersonalAuth(data);
    if (response.success) {
      ElMessage.success("提交审核成功");
    } else {
      ElMessage.error(response.message || "提交失败");
    }
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("提交失败");
  }
};

/**
 * 处理企业认证保存
 * @param data 企业认证数据
 */
const handleEnterpriseSave = async (data: any) => {
  try {
    const response = await systemApi.submitEnterpriseAuth(data);
    if (response.success) {
      ElMessage.success("保存成功");
    } else {
      ElMessage.error(response.message || "保存失败");
    }
  } catch (error) {
    console.error("保存失败:", error);
    ElMessage.error("保存失败");
  }
};

/**
 * 处理企业认证提交
 * @param data 企业认证数据
 */
const handleEnterpriseSubmit = async (data: any) => {
  try {
    const response = await systemApi.submitEnterpriseAuth(data);
    if (response.success) {
      ElMessage.success("提交审核成功");
    } else {
      ElMessage.error(response.message || "提交失败");
    }
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("提交失败");
  }
};

/**
 * 处理提交审核操作
 */
const handleSubmit = () => {
  if (submitLoading.value) return;

  submitLoading.value = true;

  try {
    if (activeTab.value === "personal") {
      personalVerificationRef.value?.handleSubmit();
    } else {
      enterpriseVerificationRef.value?.handleSubmit();
    }
  } finally {
    // 延迟重置状态，避免按钮闪烁
    setTimeout(() => {
      submitLoading.value = false;
    }, 1000);
  }
};

// 组件挂载时的初始化
onMounted(async () => {
  loading.value = true;

  try {
    // 先获取用户信息
    await getUserInfo();

    // 然后加载认证数据
    setTimeout(() => {
      loadAuthData();
    }, 100);
  } catch (error) {
    console.error("初始化失败:", error);
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped lang="scss">
.verification-container {
  min-height: 400px;
  overflow-y: hidden;

  .verification-nav {
    display: flex;
    height: 55px;
    width: 100%;
    border-radius: 10px;

    .nav-tab {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      background-color: #eee;
      color: #999;

      &.active {
        background-color: #fff;
        color: #004c66;
      }

      &:hover:not(.active) {
        background-color: #e0e0e0;
      }
    }

    .enterprise-nav-tab {
      border-top-left-radius: 10px;
    }

    .personal-nav-tab {
      border-top-right-radius: 10px;
    }
  }

  .verification-content {
    padding: 20px;
    height: 620px;
    overflow: auto;
    position: relative;

  .action-buttons {
    display: flex;
    margin-top: 20px;
    .submit-btn {
      padding: 9px 24px;
      border: none;
      border-radius: 6px;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      min-width: 120px;
      background-color: #004c66;
      color: #fff;

      &:hover:not(:disabled) {
        background-color: #003a4d;
      }

      &:disabled {
        background-color: #ccc;
        cursor: not-allowed;
      }
    }
  }
  }
}
</style>
