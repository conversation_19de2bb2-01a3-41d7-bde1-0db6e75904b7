<script setup lang="ts">
import { ref } from "vue";

interface CarouselItem {
  image: string;
  title: string;
}

const props = defineProps<{
  items: CarouselItem[];
}>();
</script>

<template>
  <el-carousel height="340px" :interval="5000" arrow="never" :autoplay="true">
    <el-carousel-item v-for="item in items" :key="item.title">
      <div class="carousel-item">
        <img :src="item.image" :alt="item.title" class="carousel-image" />
        <div class="carousel-content">
          <h2 class="carousel-title">{{ item.title }}</h2>
        </div>
      </div>
    </el-carousel-item>
  </el-carousel>
</template>

<style lang="scss" scoped>
:deep(.el-carousel__indicators--horizontal) {
  bottom: 16px;
  left: 50%; /* 水平居中定位 */
  transform: translateX(-50%) translateY(20px); /* 通过transform实现真正的居中 */
  .is-active button {
    background-color: #004c66 !important;
  }
}
:deep(.el-carousel__indicators .el-carousel__button) {
  background-color: #fff !important;
  opacity: 1;
}

.carousel-item {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 10px;

  .carousel-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
  }

  .carousel-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    color: #fff;
    border-radius: 10px;

    .carousel-title {
      font-size: 24px;
      font-weight: bold;
      margin: 0 0 10px;
    }

    /* .carousel-price {
      font-size: 18px;
      color: #1890ff;
      margin: 0 0 5px;
    }

    .carousel-description {
      font-size: 14px;
      margin: 0;
      opacity: 0.8;
    } */
  }
}

:deep(.el-carousel__arrow) {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  width: 40px;
  height: 40px;

  &:hover {
    background-color: rgba(0, 0, 0, 0.5);
  }
}

:deep(.el-carousel__indicators) {
  /* 移除这里的transform，因为已在上面的horizontal样式中设置了居中 */

  .el-carousel__button {
    width: 30px;
    height: 2px;
    background-color: rgba(255, 255, 255, 0.4);
    transition: all 0.3s;
  }

  .el-carousel__indicator.is-active .el-carousel__button {
    background-color: #fff;
  }
}
</style>
