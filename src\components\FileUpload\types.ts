import type { UploadFile } from 'element-plus'

// 文件上传类型
export type FileUploadType = 'image' | 'video' | 'other'

// 文件上传配置接口
export interface FileUploadConfig {
  type?: FileUploadType
  limit?: number
  tip?: string
  accept?: string
  disabled?: boolean
}

// 文件上传事件接口
export interface FileUploadEvents {
  onChange?: (files: UploadFile[]) => void
  onSuccess?: (response: any, file: UploadFile) => void
  onError?: (error: any, file: UploadFile) => void
}

// 文件数据接口
export interface FileData {
  fileName: string
  filePath: string
  fileSize: number
}
