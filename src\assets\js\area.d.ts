// area.js 类型声明文件

/**
 * 地区数据接口定义
 */
interface AreaData {
  /** 省份列表 */
  province_list: Record<string, string>
  /** 城市列表 */
  city_list: Record<string, string>
  /** 区县列表 */
  county_list: Record<string, string>
}

/**
 * 地区模块导出接口
 */
interface AreaModule {
  /** 地区数据 */
  area: AreaData
}

/**
 * 默认导出地区模块
 */
declare const areaData: AreaModule
export default areaData

declare module '@/assets/js/area.js' {
  export default areaData
}