<template>
  <div class="inbox-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>收件箱</h2>
      <p>管理您收到的所有消息</p>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <div class="search-form">
        <el-input
          v-model="searchForm.keywords"
          placeholder="请输入关键词搜索"
          clearable
          style="width: 300px; margin-right: 16px"
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="messageList"
        stripe
        style="width: 100%"
        empty-text="暂无收到的消息"
      >
        <el-table-column prop="sendUserName" label="发送者" width="120" />
        <el-table-column prop="productInfo" label="关联产品信息" min-width="200">
          <template #default="{ row }">
            <div class="product-info">
              <span class="product-name">{{ row.productName || '未知产品' }}</span>
              <span class="product-id" v-if="row.entrustOrderId">ID: {{ row.entrustOrderId }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="message" label="留言内容" min-width="300">
          <template #default="{ row }">
            <div class="message-content" v-html="row.message"></div>
          </template>
        </el-table-column>
        <el-table-column prop="source" label="消息来源" width="120">
          <template #default="{ row }">
            <el-tag type="info">{{ getMessageSource(row) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="发送时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleReply(row)"
            >
              回复
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="pagination.pageNo"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 回复对话框 -->
    <el-dialog
      v-model="replyDialog.visible"
      title="回复消息"
      width="600px"
      @close="handleCloseReplyDialog"
    >
      <div class="reply-form">
        <div class="original-message">
          <h4>原消息：</h4>
          <div class="original-content">
            <p><strong>发送者：</strong>{{ replyDialog.originalMessage?.sendUserName }}</p>
            <p><strong>内容：</strong></p>
            <div v-html="replyDialog.originalMessage?.message"></div>
          </div>
        </div>
        <div class="reply-input">
          <h4>回复内容：</h4>
          <el-input
            v-model="replyDialog.content"
            type="textarea"
            :rows="4"
            placeholder="请输入回复内容..."
            maxlength="500"
            show-word-limit
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="replyDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="handleSendReply" :loading="replyDialog.sending">
            发送回复
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>