<template>
  <div class="inbox-container">
    <DataTable
      :config="tableConfig"
      :search-loading="searchLoading"
      @on-filter-change="handleFilterChange"
      @on-page-change="handlePageChange"
      @on-delete="handleDeleteMessage"
      @on-edit="handleReplyMessage"
      @on-view="handleViewMessage"
    />

    <!-- 回复对话框 -->
    <el-dialog
      v-model="replyDialog.visible"
      title="回复消息"
      width="600px"
      @close="handleCloseReplyDialog"
    >
      <div class="reply-form">
        <div class="original-message">
          <h4>原消息：</h4>
          <div class="original-content">
            <p><strong>发送者：</strong>{{ replyDialog.originalMessage?.sendUserName }}</p>
            <p><strong>内容：</strong></p>
            <div v-html="replyDialog.originalMessage?.message"></div>
          </div>
        </div>
        <div class="reply-input">
          <h4>回复内容：</h4>
          <el-input
            v-model="replyDialog.content"
            type="textarea"
            :rows="4"
            placeholder="请输入回复内容..."
            maxlength="500"
            show-word-limit
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="replyDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="handleSendReply" :loading="replyDialog.sending">
            发送回复
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import DataTable from '@/components/DataTable/index.vue'
import type { TableConfig } from '@/types/table'
import { newApi } from '@/utils/api-new'

// 表格配置
const tableConfig = ref<TableConfig>({
  // 不显示按钮筛选栏
  buttonBar: {
    show: false,
    buttons: []
  },

  // 筛选栏配置
  filterBar: {
    show: true,
    filters: [
      {
        key: 'keywords',
        label: '关键词',
        type: 'input',
        placeholder: '请输入关键词搜索',
        width: '200px'
      }
    ]
  },

  // 不显示水平导航栏
  tabBar: {
    show: false,
    activeKey: '',
    tabs: []
  },

  // 表格列配置
  columns: [
    {
      key: 'index',
      label: '序号',
      width: '60px',
      align: 'center'
    },
    {
      key: 'sendUserName',
      label: '发送者',
      width: '120px',
      align: 'center'
    },
    {
      key: 'projectName',
      label: '关联产品',
      width: '200px',
      align: 'left',
    },
    {
      key: 'message',
      label: '留言内容',
      width: '300px',
      align: 'left'
    },
    {
      key: 'sendSource',
      label: '消息来源',
      width: '120px',
      align: 'center',
      type: 'tag',
    },
    {
      key: 'createTime',
      label: '发送时间',
      width: '180px',
      align: 'center',
      type: 'date'
    },
    {
      key: 'actions',
      label: '操作',
      width: '160px',
      align: 'center',
      type: 'action'
    }
  ],

  // 分页配置
  pagination: {
    show: true,
    current: 1,
    pageSize: 10,
    total: 0
  },

  // 数据
  data: [],
  loading: true,

  // 表格样式配置
  tableStyle: {
    maxHeight: 420,
    border: false,
    stripe: true,
    showSelection: false
  }
})

// 加载状态
const searchLoading = ref(false)

// 回复对话框相关
const replyDialog = reactive({
  visible: false,
  content: '',
  originalMessage: null as any,
  sending: false
})

// 查询参数
const queryParams = reactive({
  keywords: '',
  page: 1,
  pageSize: 10,
  total: 0
})

// 获取当前用户ID
const getCurrentUserId = () => {
  const freedomUserInfo = JSON.parse(localStorage.getItem('freedomUserInfo') || '{}')
  return freedomUserInfo.userInfo?.id
}

// 事件处理函数
const handleFilterChange = (filters: Record<string, any>) => {
  console.log('筛选变化:', filters)
  Object.assign(queryParams, filters)
  queryParams.page = 1
  loadData(true)
}

const handlePageChange = (page: number, pageSize: number) => {
  console.log('分页变化:', page, pageSize)
  queryParams.page = page
  queryParams.pageSize = pageSize
  loadData()
}

const handleDeleteMessage = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条消息吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await newApi.deleteMessage(row.id)

    if (response.success) {
      ElMessage.success('删除成功')
      loadData()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败，请稍后重试')
    }
  }
}

const handleReplyMessage = (row: any) => {
  replyDialog.originalMessage = row
  replyDialog.content = ''
  replyDialog.visible = true
}

const handleViewMessage = (row: any) => {
  // 查看消息详情，这里可以打开详情弹窗或跳转到详情页
  console.log('查看消息详情:', row)
  ElMessage.info('查看消息详情功能')
}

const handleCloseReplyDialog = () => {
  replyDialog.visible = false
  replyDialog.content = ''
  replyDialog.originalMessage = null
}

const handleSendReply = async () => {
  if (!replyDialog.content.trim()) {
    ElMessage.warning('请输入回复内容')
    return
  }

  const currentUserId = getCurrentUserId()
  if (!currentUserId) {
    ElMessage.error('请先登录')
    return
  }

  try {
    replyDialog.sending = true

    const response = await newApi.sendMessage({
      sendUserId: currentUserId,
      receUserId: replyDialog.originalMessage.sendUserId,
      enterustOrderId: replyDialog.originalMessage.entrustOrderId,
      message: replyDialog.content,
      attachmentList: []
    })

    if (response.success) {
      ElMessage.success('回复发送成功')
      handleCloseReplyDialog()
    } else {
      ElMessage.error(response.message || '回复发送失败')
    }
  } catch (error) {
    console.error('发送回复失败:', error)
    ElMessage.error('回复发送失败，请稍后重试')
  } finally {
    replyDialog.sending = false
  }
}

// 加载数据
const loadData = async (showSearchLoading = false) => {
  tableConfig.value.loading = true

  if (showSearchLoading) {
    searchLoading.value = true
  }

  try {
    const currentUserId = getCurrentUserId()
    if (!currentUserId) {
      ElMessage.error('请先登录')
      return
    }

    const apiParams: any = {
      pageNo: queryParams.page,
      pageSize: queryParams.pageSize,
      receiveUserId: currentUserId
    }

    if (queryParams.keywords) {
      apiParams.keywords = queryParams.keywords
    }

    const response = await newApi.getReceivedMessages(apiParams)

    if (response.success && response.result) {
      const { records, total } = response.result

      const processedData = records.map((item: any, index: number) => ({
        ...item,
        index: (queryParams.page - 1) * queryParams.pageSize + index + 1
      }))

      tableConfig.value.data = processedData

      if (tableConfig.value.pagination) {
        tableConfig.value.pagination.current = queryParams.page
        tableConfig.value.pagination.pageSize = queryParams.pageSize
        tableConfig.value.pagination.total = total || 0
      }

      queryParams.total = total || 0
    } else {
      ElMessage.error(response.message || '获取数据失败')
      tableConfig.value.data = []
      if (tableConfig.value.pagination) {
        tableConfig.value.pagination.total = 0
      }
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败，请稍后重试')
    tableConfig.value.data = []
    if (tableConfig.value.pagination) {
      tableConfig.value.pagination.total = 0
    }
  } finally {
    tableConfig.value.loading = false
    searchLoading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
.inbox-container {
  height: 100%;

  .reply-form {
    .original-message {
      margin-bottom: 20px;
      padding: 16px;
      background: #f5f7fa;
      border-radius: 8px;

      h4 {
        margin: 0 0 12px 0;
        color: #333;
        font-size: 16px;
        font-weight: 600;
      }

      .original-content {
        p {
          margin: 8px 0;
          color: #666;
          font-size: 14px;

          strong {
            color: #333;
          }
        }

        div {
          margin-top: 8px;
          padding: 8px;
          background: #fff;
          border-radius: 4px;
          border: 1px solid #e5e5e5;
          color: #333;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }

    .reply-input {
      h4 {
        margin: 0 0 12px 0;
        color: #333;
        font-size: 16px;
        font-weight: 600;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>