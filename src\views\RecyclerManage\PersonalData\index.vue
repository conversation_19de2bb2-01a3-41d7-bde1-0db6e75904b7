<template>
  <div class="personal-info" v-loading="loading" element-loading-text="加载中...">
    <!-- 用户信息顶部 -->
    <div class="user-header">
      <div class="user-avatar">
        <div class="avatar-bg" v-if="!userInfo.avatar">
          <SvgIcon iconName="user" className="avatar-icon" />
        </div>
        <img v-else :src="userInfo.avatar" alt="头像" class="avatar-image" />
        <span class="phone-display">{{ formatPhone(userInfo.phone) }}</span>
      </div>
    </div>

    <!-- 用户资料列表 -->
    <div class="info-list">
      <!-- 真实姓名 -->
      <div class="info-item">
        <div class="info-content">
          <div class="label">真实姓名</div>
          <div class="value">{{ userInfo.realname || '暂未填写' }}</div>
        </div>
      </div>

      <!-- 用户名 -->
      <div class="info-item">
        <div class="info-content">
          <div class="label">用户名</div>
          <div class="value">{{ userInfo.username || '暂未填写' }}</div>
        </div>
      </div>

      <!-- 用户ID -->
      <div class="info-item">
        <div class="info-content">
          <div class="label">用户ID</div>
          <div class="value">{{ userInfo.id || '暂未填写' }}</div>
        </div>
      </div>

      <!-- 联系电话 -->
      <div class="info-item">
        <div class="info-content">
          <div class="label">联系电话</div>
          <div class="value">{{ userInfo.phone || '暂未填写' }}</div>
        </div>
      </div>

      <!-- 邮箱 -->
      <div class="info-item">
        <div class="info-content">
          <div class="label">邮箱</div>
          <div class="value">{{ userInfo.email || '暂未填写' }}</div>
        </div>
      </div>

      <!-- 性别 -->
      <div class="info-item">
        <div class="info-content">
          <div class="label">性别</div>
          <div class="value">{{ userInfo.sex_dictText || '暂未填写' }}</div>
        </div>
      </div>

      <!-- 生日 -->
      <div class="info-item">
        <div class="info-content">
          <div class="label">生日</div>
          <div class="value">{{ userInfo.birthday || '暂未填写' }}</div>
        </div>
      </div>

      <!-- 工号 -->
      <div class="info-item">
        <div class="info-content">
          <div class="label">工号</div>
          <div class="value">{{ userInfo.workNo || '暂未填写' }}</div>
        </div>
      </div>

      <!-- 状态 -->
      <div class="info-item">
        <div class="info-content">
          <div class="label">状态</div>
          <div class="value">{{ userInfo.status_dictText || '暂未填写' }}</div>
        </div>
      </div>

      <!-- 创建时间 -->
      <div class="info-item">
        <div class="info-content">
          <div class="label">创建时间</div>
          <div class="value">{{ userInfo.createTime || '暂未填写' }}</div>
        </div>
      </div>

      <!-- 更新时间 -->
      <div class="info-item no-border">
        <div class="info-content">
          <div class="label">更新时间</div>
          <div class="value">{{ userInfo.updateTime || '暂未填写' }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import SvgIcon from "@/components/SvgIcon.vue";
import { ElMessage } from "element-plus";
import { systemApi } from "@/utils/api-new";

// 定义用户信息接口
interface UserInfo {
  birthday?: string;
  relTenantIds?: string;
  bpmStatus?: any;
  sex_dictText?: string;
  smsCode?: any;
  activitiSync?: number;
  userIdentity?: number;
  status_dictText?: string;
  delFlag?: number;
  reviewStatus_dictText?: any;
  workNo?: string;
  post?: any;
  updateBy?: string;
  orgCode?: string;
  izBindThird?: boolean;
  roleCode?: any;
  id?: string;
  email?: string;
  post_dictText?: any;
  clientId?: any;
  roleCode_dictText?: any;
  sex?: number;
  homePath?: any;
  departIds_dictText?: any;
  telephone?: any;
  updateTime?: string;
  departIds?: string;
  avatar?: string;
  realname?: string;
  createBy?: any;
  postText?: any;
  phone?: string;
  createTime?: string;
  orgCodeTxt?: any;
  reviewStatus?: any;
  loginTenantId?: number;
  loginSource?: any;
  username?: string;
  status?: number;
}

// 用户信息数据
const userInfo = ref<UserInfo>({});
const loading = ref(false);

/**
 * 格式化手机号显示
 * @param phone 手机号
 * @returns 格式化后的手机号
 */
const formatPhone = (phone?: string): string => {
  if (!phone) return '暂未绑定手机号';

  // 如果手机号长度为11位，进行脱敏处理
  if (phone.length === 11) {
    return `${phone.slice(0, 3)} ****${phone.slice(-4)}`;
  }

  return phone;
};

/**
 * 获取用户数据
 */
const getUserData = async () => {
  loading.value = true;

  try {
    const response = await systemApi.getUserData();

    // 根据API响应格式处理数据
    if (response && response.success && response.result) {
      userInfo.value = response.result;
      console.log('获取到的用户数据:', response.result);
    } else if (response) {
      // 如果直接返回用户数据（根据你提供的数据结构）
      userInfo.value = response as any;
      console.log('获取到的用户数据:', response);
    } else {
      ElMessage.error('获取用户数据失败');
    }
  } catch (error) {
    console.error('获取用户数据失败:', error);
    ElMessage.error('获取用户数据失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 组件挂载时获取用户数据
onMounted(() => {
  getUserData();
});
</script>

<style lang="scss" scoped>
.personal-info {
  margin: 30px 20px 20px 20px;
  min-height: 400px; // 确保加载状态有足够的高度
}

// 用户信息顶部样式
.user-header {
  margin-bottom: 10px;

  .user-avatar {
    display: flex;
    align-items: center;
    gap: 9px;

    .avatar-bg {
      background-color: #004c66;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;

      .avatar-icon {
        width: 24px;
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .avatar-image {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid #004c66;
    }

    .phone-display {
      background-color: #E6EEF0;
      border: 1px solid #004C66;
      padding: 5px 10px;
      border-radius: 6px;
      font-size: 12px;
      color: #004C66;
    }
  }
}

// 信息列表样式
.info-list {
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid;
    // 渐变
    border-image: linear-gradient(to right, rgba(221, 221, 221, 1), rgba(255, 255, 255, 1));
    border-image-slice: 1;

    &.no-border {
      border-bottom: none;
    }

    .info-content {
      flex: 1;

      .label {
        font-size: 18px;
        color: #333;
        margin-bottom: 10px;
        font-family: 'PingFang Bold';
      }

      .value {
        font-size: 14px;
        color: #999;
        line-height: 1.5;
        word-break: break-all;
      }
    }
  }
}
</style>