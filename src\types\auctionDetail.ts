// 拍卖详情页面相关接口定义

// 定义标的项目接口
export interface BiaodItem {
	id: number
	bd_title: string
	bd_status: number
	bd_url: string
	[key: string]: any // 允许其他属性
}

// 定义出价记录接口
export interface ChujiaiItem {
	id: number // 出价记录ID
	jingpaihaopai: string // 竞拍号牌
	dangqianjia: string // 当前价格
	addtime: number // 添加时间
}

// 定义发言记录接口
export interface FayanItem {
	id: number // 发言记录ID
	type_name: string // 发言类型名称
	addtime_name: string // 添加时间名称
	content: string // 发言内容
}

// 定义倒计时信息接口
export interface CountdownInfo {
	dayDiff: string
	hours: string
	minutes: string
	seconds: string
}

// 定义收款信息接口
export interface ShoukuanInfo {
	kaihunum: string // 银行账号
	kaihuname: string // 开户行
	[key: string]: any // 允许其他属性
}

// 定义标的信息接口
export interface BiaodInfo {
	id: number
	pmh_id: number
	pmh_name: string
	pmh_xingshi: number
	pmh_type: number
	pmh_status: number
	qiyemingcheng: string
	qiyephone: string
	bd_title: string
	bd_status: number
	bd_qipaijia: string
	qpj_danwie: string
	bd_baozhengjin: string
	bd_pinggujiage: string
	bd_baoliujia: string
	bd_jiajiafudu: string
	bd_num: string
	bd_danwei: string
	pms_name: string
	pms_card: string
	address: string
	bd_weiguan: number
	pmh_shengming: string
	pmh_gonggao: string
	pmh_xuzhi: string
	bd_jieshao: string
	yongjin: string
	parentid: number,
	province: number,
	city: number,
	district: number,
	[key: string]: any
}