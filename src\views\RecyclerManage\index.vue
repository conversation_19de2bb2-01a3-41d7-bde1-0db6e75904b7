<template>
  <div class="recycler-manage-container">
    <!-- 左侧导航栏 -->
    <div class="recycler-sidebar">
      <!-- 标题 -->
      <div class="sidebar-title">回收商管理中心</div>
      <!-- 分割线 -->
      <div class="sidebar-divider"></div>
      <!-- 导航菜单 -->
      <div class="sidebar-nav">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="3" animated />
        </div>

        <!-- 动态菜单 -->
        <el-menu
          v-else-if="menuItems.length > 0"
          :default-active="activeNav"
          class="custom-menu"
          :collapse="false"
          :unique-opened="false"
          background-color="transparent"
          text-color="#666666"
          active-text-color="#004C66"
          @select="handleMenuSelect"
        >
          <template v-for="menuItem in menuItems" :key="menuItem.id">
            <!-- 有子菜单的项目 -->
            <el-sub-menu
              v-if="menuItem.children && menuItem.children.length > 0"
              :index="menuItem.id"
              class="custom-submenu"
            >
              <template #title>
                <el-icon v-if="getAntdIcon(menuItem.meta?.icon)" class="menu-icon" :size="13">
                  <component :is="getAntdIcon(menuItem.meta?.icon)" />
                </el-icon>
                <span class="menu-title">{{ menuItem.meta?.title || menuItem.name }}</span>
              </template>

              <el-menu-item
                v-for="subItem in menuItem.children"
                :key="subItem.id"
                :index="subItem.path"
                class="custom-menu-item submenu-item"
              >
                <el-icon v-if="getAntdIcon(subItem.meta?.icon)" class="menu-icon" :size="13">
                  <component :is="getAntdIcon(subItem.meta?.icon)" />
                </el-icon>
                <span class="menu-title">{{ subItem.meta?.title || subItem.name }}</span>
              </el-menu-item>
            </el-sub-menu>

            <!-- 没有子菜单的项目 -->
            <el-menu-item
              v-else
              :index="menuItem.path"
              class="custom-menu-item"
            >
              <el-icon v-if="getAntdIcon(menuItem.meta?.icon)" class="menu-icon" :size="13">
                <component :is="getAntdIcon(menuItem.meta?.icon)" />
              </el-icon>
              <span class="menu-title">{{ menuItem.meta?.title || menuItem.name }}</span>
            </el-menu-item>
          </template>
        </el-menu>

        <!-- 默认菜单（当动态菜单加载失败时） -->
        <el-menu
          v-else
          :default-active="activeNav"
          class="custom-menu"
          background-color="transparent"
          text-color="#666666"
          active-text-color="#004C66"
          @select="handleMenuSelect"
        >
          <el-menu-item
            v-for="item in defaultNavItems"
            :key="item.key"
            :index="item.key"
            class="custom-menu-item"
          >
            <el-icon v-if="getAntdIcon(item.icon)" class="menu-icon">
              <component :is="getAntdIcon(item.icon)" />
            </el-icon>
            <span class="menu-title">{{ item.label }}</span>
          </el-menu-item>
        </el-menu>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="recycler-content">
      <!-- 发布供应信息页面 -->
      <SupplyPublish v-if="activeNav === '/supplyAndDemandInfo/supply'" />
      <!-- 发布求购信息页面 -->
      <DemandPublish v-if="activeNav === '/supplyAndDemandInfo/demand'" />
      <!-- 供应信息列表页面 -->
      <SupplyList v-if="activeNav === '/publishList/supply'" />
      <!-- 求购信息列表页面 -->
      <DemandList v-if="activeNav === '/publishList/demand'" />
      <!-- 行业资讯列表页面 -->
      <IndustryInfoList v-if="activeNav === '/hgy/waste/hgyIndustryInfoList'" />
      <!-- 个人资料页面 -->
      <PersonalData v-if="activeNav === '/hgy/personalCenter/personalData'" />
      <!-- 账号安全页面 -->
       <AccountSecurity v-if="activeNav === '/hgy/personalCenter/accountSecurity'" />
       <!-- 实名认证页面 -->
       <Verification v-if="activeNav === '/realNameAuth'" />
       <!-- 收藏记录页面 -->
       <CollectList v-if="activeNav === '/hgy/personalCenter/hgyCollectLogList'" />
       <!-- 浏览记录页面 -->
       <BrowseList v-if="activeNav === '/hgy/personalCenter/hgyBrowseLogList'" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import SupplyPublish from './SupplyAndDemandPubilsh/Supply/index.vue'
import DemandPublish from './SupplyAndDemandPubilsh/Demand/index.vue'
import SupplyList from './PublishList/Supply/index.vue'
import DemandList from './PublishList/Demand/index.vue'
import IndustryInfoList from './IndustryInfoList/index.vue'
import PersonalData from './PersonalData/index.vue'
import AccountSecurity from './AccountSecurity/index.vue'
import Verification from './Verification/index.vue'
import CollectList from './CollectList/index.vue'
import BrowseList from './BrowseList/index.vue'
import { systemApi, type MenuItem } from '@/utils/api-new'
import { ElMessage } from 'element-plus'
// 导入全部 Ant Design Vue 图标
import * as AntdIcons from '@ant-design/icons-vue'


// 当前激活的导航项
const activeNav = ref('')

// 动态菜单数据
const menuItems = ref<MenuItem[]>([])
// 加载状态
const loading = ref(false)

// 静态导航菜单配置（作为默认值）
const defaultNavItems = [
  { key: '/supplyAndDemandInfo/supply', label: '发布供应信息', icon: 'ant-design:plus-circle-outlined' },
  { key: '/supplyAndDemandInfo/demand', label: '发布求购信息', icon: 'ant-design:file-text-outlined' },
  { key: '/publishList/supply', label: '供应信息列表', icon: 'ant-design:unordered-list-outlined' },
  { key: '/publishList/demand', label: '求购信息列表', icon: 'ant-design:bars-outlined' }
]

/**
 * 获取用户权限菜单
 */
const getUserPermissionMenu = async () => {
  try {
    loading.value = true

    const response = await systemApi.getUserPermissionByToken()

    if (response.success !== false && response.result?.menu) {
      menuItems.value = response.result.menu
      console.log('获取到的菜单数据:', menuItems.value)
    } else if (response.data?.menu) {
      // 尝试从data字段获取菜单数据
      menuItems.value = response.data.menu
      console.log('从data字段获取到的菜单数据:', menuItems.value)
    } else {
      console.warn('获取菜单数据失败，响应数据:', response)
      // 如果获取失败，可以使用默认菜单或显示错误信息
    }
  } catch (error) {
    console.error('获取用户权限菜单失败:', error)
    const errorMessage = error instanceof Error ? error.message : '网络错误'
    ElMessage.error(`获取菜单数据失败: ${errorMessage}`)
  } finally {
    loading.value = false
  }
}

/**
 * 处理el-menu的选择事件
 * @param index 选中的菜单项索引（path）
 */
const handleMenuSelect = (index: string) => {
  activeNav.value = index
  // 保存当前选中的菜单到 sessionStorage
  sessionStorage.setItem('recyclerManage_activeNav', index)
  console.log('切换到组件:', index)
}

/**
 * 根据图标名称获取对应的 Ant Design 图标组件
 * @param iconName 图标名称，支持 ant-design: 前缀
 */
const getAntdIcon = (iconName?: string) => {
  if (!iconName) return null

  // 处理 ant-design: 前缀的图标名称
  let iconKey = iconName
  if (iconName.startsWith('ant-design:')) {
    iconKey = iconName.replace('ant-design:', '')
  }

  // 将 kebab-case 转换为 PascalCase
  // 例如: plus-circle-twotone -> PlusCircleTwoTone
  const pascalCase = iconKey
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join('')

  // 从 AntdIcons 中获取对应的图标组件
  return (AntdIcons as any)[pascalCase] || null
}





/**
 * 恢复上次选中的菜单状态
 */
const restoreActiveNav = () => {
  // 从 sessionStorage 恢复上次选中的菜单
  const savedActiveNav = sessionStorage.getItem('recyclerManage_activeNav')
  if (savedActiveNav) {
    activeNav.value = savedActiveNav
    console.log('恢复菜单状态:', savedActiveNav)
  } else {
    // 如果没有保存的状态，设置默认值
    activeNav.value = '/publishList/supply'
    sessionStorage.setItem('recyclerManage_activeNav', activeNav.value)
  }
}

/**
 * 清除保存的菜单状态（可选功能）
 */
const clearSavedNavState = () => {
  sessionStorage.removeItem('recyclerManage_activeNav')
  console.log('已清除保存的菜单状态')
}

// 监听 activeNav 变化，确保状态同步
watch(activeNav, (newValue) => {
  if (newValue) {
    sessionStorage.setItem('recyclerManage_activeNav', newValue)
  }
}, { immediate: false })

// 组件挂载时获取菜单数据并恢复状态
onMounted(() => {
  getUserPermissionMenu()
  restoreActiveNav()
})
</script>

<style scoped lang="scss">
.recycler-manage-container {
  display: flex;
  gap: 10px;
  padding: 20px;
  min-height: calc(100vh - 80px); // 减去header高度
  max-width: 1280px; // 限制最大宽度，避免在大屏幕上过度拉伸
  margin: 0 auto; // 居中显示
  width: 100%; // 确保在小屏幕上能够充分利用空间
}

// 左侧导航栏样式
.recycler-sidebar {
  width: 240px;
  background-color: #ffffff;
  border-radius: 10px;
  padding: 20px;
  height: fit-content;

  .sidebar-title {
    font-size: 18px;
    font-weight: 600;
    color: #333333;
    margin-bottom: 16px;
  }

  .sidebar-divider {
    height: 1px;
    background-color: #e5e5e5;
    margin-bottom: 20px;
  }

  .sidebar-nav {
    max-height: calc(100vh - 335px);
    overflow-y: auto;
    // 隐藏滚动条
    &::-webkit-scrollbar {
      display: none;
    }
    // 加载状态样式
    .loading-container {
      padding: 20px;
    }

    // 自定义el-menu样式
    .custom-menu {
      border: none;
      background-color: transparent;

      // 使用深度选择器确保样式生效
      :deep(.el-menu-item) {
        height: 48px;
        line-height: 48px;
        margin: 4px 0;
        border-radius: 8px;
        transition: all 0.3s ease;
        background-color: transparent;

        &:hover {
          background-color: rgba(0, 76, 102, 0.05) !important;
        }

        &.is-active {
          background-color: rgba(0, 76, 102, 0.1) !important;
          color: #004C66 !important;
          font-weight: 500;

          .menu-icon {
            color: #004C66 !important;
          }
        }

        .menu-icon {
          font-size: 16px;
          color: #666666;
          transition: color 0.3s ease;
        }

        .menu-title {
          font-size: 15px;
          color: #666666;
          transition: color 0.3s ease;
        }

        &:hover .menu-icon,
        &:hover .menu-title {
          color: #333333;
        }
      }

      // 子菜单样式
      :deep(.el-sub-menu) {
        .el-sub-menu__title {
          height: 48px;
          line-height: 48px;
          margin: 4px 0;
          border-radius: 8px;
          transition: all 0.3s ease;
          background-color: transparent !important;

          &:hover {
            background-color: rgba(0, 76, 102, 0.05) !important;

            .menu-icon,
            .menu-title {
              color: #333333;
            }
          }

          .menu-icon {
            margin-right: 8px;
            font-size: 16px;
            color: #666666;
            transition: color 0.3s ease;
          }

          .menu-title {
            font-size: 15px;
            color: #666666;
            font-weight: 500;
            transition: color 0.3s ease;
          }

          .el-sub-menu__icon-arrow {
            color: #666666;
            font-size: 12px;
            transition: all 0.3s ease;
          }
        }

        .el-menu {
          background-color: rgba(0, 0, 0, 0.02);
          border-radius: 8px;
          margin: 4px 0 4px 12px;
          padding: 4px 0;

          .el-menu-item {
            height: 40px;
            line-height: 40px;
            margin: 2px 8px;
            border-radius: 6px;
            background-color: transparent;

            .menu-icon {
              margin-right: 6px;
              font-size: 14px;
              color: #666666;
              transition: color 0.3s ease;
            }

            .menu-title {
              font-size: 14px;
              color: #666666;
            }

            &:hover {
              background-color: rgba(0, 76, 102, 0.08) !important;

              .menu-icon,
              .menu-title {
                color: #333333;
              }
            }

            &.is-active {
              background-color: rgba(0, 76, 102, 0.15) !important;

              .menu-icon,
              .menu-title {
                color: #004C66 !important;
                font-weight: 500;
              }
            }
          }
        }
      }
    }
  }
}

// 右侧内容区域样式
.recycler-content {
  flex: 1;
  background-color: #ffffff;
  border-radius: 10px;
  height: 680px;
  overflow: auto;

  // 动态内容样式
  .dynamic-content {
    padding: 24px;
    height: 100%;

    .content-header {
      margin-bottom: 24px;
      border-bottom: 1px solid #e5e5e5;
      padding-bottom: 16px;

      h2 {
        margin: 0 0 8px 0;
        color: #333333;
        font-size: 24px;
        font-weight: 600;
      }

      .content-path {
        margin: 0;
        color: #666666;
        font-size: 14px;
      }
    }

    .content-body {
      .placeholder-content {
        padding: 32px;
        text-align: center;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 2px dashed #dee2e6;

        p {
          margin: 12px 0;
          color: #666666;

          strong {
            color: #004C66;
          }

          &.tip {
            margin-top: 24px;
            font-style: italic;
            color: #999999;
          }
        }
      }
    }
  }

  // 默认内容样式
  .default-content {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

    .welcome-message {
      text-align: center;

      h2 {
        margin: 0 0 16px 0;
        color: #333333;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 0;
        color: #666666;
        font-size: 16px;
      }
    }
  }
}
</style>