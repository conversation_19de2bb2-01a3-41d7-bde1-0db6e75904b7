<template>
  <div class="industry-info-list-page">
    <DataTable
      :config="tableConfig"
      :search-loading="searchLoading"
      @on-button-click="handleButtonClick"
      @on-filter-change="handleFilterChange"
      @on-tab-change="handleTabChange"
      @on-page-change="handlePageChange"
      @on-sort-change="handleSortChange"
      @on-row-click="handleRowClick"
      @on-row-double-click="handleRowDoubleClick"
    />


  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import DataTable from '@/components/DataTable/index.vue'
import type { TableConfig } from '@/types/table'
import { industryInfoApi } from '@/utils/api-new'

// 加载状态
const searchLoading = ref(false)

// 表格配置
const tableConfig = ref<TableConfig>({
  // 不显示按钮栏
  buttonBar: {
    show: false,
    buttons: []
  },

  // 筛选栏配置
  filterBar: {
    show: true,
    filters: [
      {
        key: 'materialType',
        label: '物资种类',
        type: 'select',
        placeholder: '请选择物资种类',
        width: '130px',
        options: [
          { label: '废钢', value: '1' },
          { label: '废铜', value: '2' },
          { label: '废铝', value: '3' },
          { label: '废纸', value: '4' },
          { label: '废塑料', value: '5' }
        ]
      },
      {
        key: 'city',
        label: '城市',
        type: 'input',
        placeholder: '请输入城市',
        width: '130px'
      },
      {
        key: 'infoDateRange',
        label: '资讯日期',
        type: 'daterange',
        placeholder: '请选择日期范围',
        width: '210px'
      }
    ]
  },

  // 水平导航栏配置（只显示导出按钮）
  tabBar: {
    show: true,
    tabs: [], // 不显示标签页
    activeKey: '',
    rightButtons: [
      {
        key: 'export',
        label: '导出',
        type: 'default',
        icon: Download
      }
    ]
  },

  // 表格列配置（基于参考文件）
  columns: [
    {
      key: 'index',
      label: '序号',
      width: '60px',
      align: 'center'
    },
    {
      key: 'materialType_dictText',
      label: '物资种类',
      width: '120px',
      align: 'center'
    },
    {
      key: 'category',
      label: '品类/细类',
      width: '120px',
      align: 'center'
    },
    {
      key: 'province',
      label: '省份',
      width: '100px',
      align: 'center'
    },
    {
      key: 'city',
      label: '城市',
      width: '100px',
      align: 'center'
    },
    {
      key: 'district',
      label: '区县',
      width: '100px',
      align: 'center'
    },
    {
      key: 'highPrice',
      label: '高价',
      width: '100px',
      align: 'center',
      type: 'money'
    },
    {
      key: 'lowPrice',
      label: '低价',
      width: '100px',
      align: 'center',
      type: 'money'
    },
    {
      key: 'avgPrice',
      label: '均价',
      width: '100px',
      align: 'center',
      type: 'money'
    },
    {
      key: 'changeAmount',
      label: '涨跌额',
      width: '100px',
      align: 'center',
      formatter: (value) => {
        if (!value && value !== 0) return '-'
        const num = Number(value)
        if (num > 0) return `+${num}`
        return num.toString()
      }
    },
    {
      key: 'changeRate',
      label: '涨跌幅',
      width: '100px',
      align: 'center',
      formatter: (value) => {
        if (!value && value !== 0) return '-'
        const num = Number(value)
        if (num > 0) return `+${num}%`
        return `${num}%`
      }
    },
    {
      key: 'infoDate',
      label: '资讯日期',
      width: '120px',
      align: 'center',
      type: 'date'
    },
    {
      key: 'createTime',
      label: '创建时间',
      width: '160px',
      align: 'center',
      type: 'date'
    }
  ],

  // 分页配置
  pagination: {
    show: true,
    current: 1,
    pageSize: 10,
    total: 0
  },

  // 数据
  data: [],
  loading: true, // 初始状态为加载中

  // 表格样式配置
  tableStyle: {
    maxHeight: 435, // 设置表格最大高度
    border: false,   // 显示边框
    stripe: true,    // 显示斑马纹
    showSelection: false // 显示选择列
  }
})

// 查询参数
const queryParams = reactive({
  materialType: '', // 物资种类
  city: '', // 城市
  infoDateRange: [], // 资讯日期范围
  page: 1,
  pageSize: 10,
  total: 0
})



// 事件处理函数
const handleButtonClick = (key: string) => {
  console.log('按钮点击:', key)
  switch (key) {
    case 'export':
      handleExport()
      break
  }
}

const handleFilterChange = (filters: Record<string, any>) => {
  console.log('筛选变化:', filters)
  Object.assign(queryParams, filters)
  queryParams.page = 1 // 重置到第一页
  loadData(true) // 显示搜索按钮加载状态
}

const handleTabChange = (key: string) => {
  console.log('标签切换:', key)
}

const handlePageChange = (page: number, pageSize: number) => {
  console.log('分页变化:', page, pageSize)
  queryParams.page = page
  queryParams.pageSize = pageSize
  loadData()
}

const handleSortChange = (column: string, order: 'asc' | 'desc' | null) => {
  console.log('排序变化:', column, order)
}

const handleRowClick = (row: any) => {
  console.log('行点击:', row)
}

const handleRowDoubleClick = (row: any) => {
  console.log('行双击:', row)
  // 行业资讯页面不需要查看功能
}





const handleExport = async () => {
  console.log('导出行业资讯')
  try {
    searchLoading.value = true

    // 构建导出参数
    const exportParams: any = {}

    if (queryParams.materialType) {
      exportParams.materialType = queryParams.materialType
    }

    if (queryParams.city) {
      exportParams.city = queryParams.city
    }

    // 处理日期范围
    if (queryParams.infoDateRange && queryParams.infoDateRange.length === 2) {
      exportParams.infoDate_begin = queryParams.infoDateRange[0]
      exportParams.infoDate_end = queryParams.infoDateRange[1]
    }

    const blob = await industryInfoApi.exportIndustryInfo(exportParams)

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `行业资讯表_${new Date().toISOString().slice(0, 10)}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  } finally {
    searchLoading.value = false
  }
}



// 加载数据
const loadData = async (showSearchLoading = false) => {
  // 控制表格加载状态
  tableConfig.value.loading = true

  // 控制搜索按钮加载状态
  if (showSearchLoading) {
    searchLoading.value = true
  }

  try {
    // 构建API请求参数
    const apiParams: any = {
      pageNo: queryParams.page,
      pageSize: queryParams.pageSize
    }

    // 添加筛选条件
    if (queryParams.materialType) {
      apiParams.materialType = queryParams.materialType
    }

    if (queryParams.city) {
      apiParams.city = queryParams.city
    }

    // 处理日期范围
    if (queryParams.infoDateRange && queryParams.infoDateRange.length === 2) {
      apiParams.infoDate_begin = queryParams.infoDateRange[0]
      apiParams.infoDate_end = queryParams.infoDateRange[1]
    }

    // 调用API
    const response = await industryInfoApi.getIndustryInfoList(apiParams)

    if (response.success && response.result) {
      // 处理返回的数据
      const { records, total } = response.result

      // 为每条记录添加序号
      const processedData = records.map((item: any, index: number) => ({
        ...item,
        index: (queryParams.page - 1) * queryParams.pageSize + index + 1
      }))

      tableConfig.value.data = processedData

      // 更新分页信息
      if (tableConfig.value.pagination) {
        tableConfig.value.pagination.current = queryParams.page
        tableConfig.value.pagination.pageSize = queryParams.pageSize
        tableConfig.value.pagination.total = total || 0
      }

      queryParams.total = total || 0
    } else {
      ElMessage.error(response.message || '获取数据失败')
      tableConfig.value.data = []
      if (tableConfig.value.pagination) {
        tableConfig.value.pagination.total = 0
      }
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败，请稍后重试')
    tableConfig.value.data = []
    if (tableConfig.value.pagination) {
      tableConfig.value.pagination.total = 0
    }
  } finally {
    tableConfig.value.loading = false
    searchLoading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.industry-info-list-page {
  /* 行业资讯列表页面样式 */
  min-height: 100%;
}
</style>