/**
 * 图片预加载工具
 * 用于预加载关键图片资源，提升首屏加载性能
 */

// 关键图片列表 - 这些图片会在应用启动时预加载
// 已迁移到阿里云OSS，提升加载速度和稳定性
const CRITICAL_IMAGES = [
  // Header背景图片 - 多尺寸适配不同设备
  'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/recommend-bg_1754902625553.jpg',
  'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/recommend-bg-medium_1754902793261.jpg',
  'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/recommend-bg-mini_1754902847203.jpg',

  // 登录页面3D图片 - 首屏显示，预加载可提升用户体验
  'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/1_1754963030315.png',
  'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/2_1754963119472.png',
  'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/3_1754963135630.png',

  // 登录背景图片
  'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/login-bg_1754963733964.jpg',

  // 其他关键图片可以在这里添加
];

/**
 * 预加载单个图片
 * @param src 图片路径
 * @returns Promise<HTMLImageElement>
 */
function preloadImage(src: string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      // console.log(`✅ 图片预加载成功: ${src}`); 已移除
      resolve(img);
    };
    
    img.onerror = () => {
      // console.warn(`❌ 图片预加载失败: ${src}`); 已移除
      reject(new Error(`Failed to load image: ${src}`));
    };
    
    img.src = src;
  });
}

/**
 * 批量预加载图片
 * @param imagePaths 图片路径数组
 * @returns Promise<HTMLImageElement[]>
 */
export function preloadImages(imagePaths: string[]): Promise<HTMLImageElement[]> {
  // console.log(`🖼️ 开始预加载 ${imagePaths.length} 张图片...`); 已移除
  
  const preloadPromises = imagePaths.map(path => 
    preloadImage(path).catch(error => {
      // 即使某张图片加载失败，也不影响其他图片的加载
      // console.warn(`图片预加载失败但继续: ${path}`, error); 已移除
      return null;
    })
  );
  
  return Promise.all(preloadPromises).then(results => {
    const successfulImages = results.filter(img => img !== null) as HTMLImageElement[];
    // console.log(`✅ 图片预加载完成: ${successfulImages.length}/${imagePaths.length} 张成功`); 已移除
    return successfulImages;
  });
}

/**
 * 初始化图片预加载
 * 在应用启动时调用，预加载关键图片
 */
export function initImagePreload(): void {
  // console.log('🚀 初始化图片预加载...'); 已移除
  
  // 延迟预加载，避免影响首屏渲染
  setTimeout(() => {
    preloadImages(CRITICAL_IMAGES).catch(error => {
      console.error('图片预加载初始化失败:', error);
    });
  }, 100);
}

/**
 * 添加图片到预加载列表
 * @param imagePath 图片路径
 */
export function addToPreloadList(imagePath: string): void {
  if (!CRITICAL_IMAGES.includes(imagePath)) {
    CRITICAL_IMAGES.push(imagePath);
    // console.log(`📝 添加图片到预加载列表: ${imagePath}`); 已移除
  }
}

/**
 * 移除图片从预加载列表
 * @param imagePath 图片路径
 */
export function removeFromPreloadList(imagePath: string): void {
  const index = CRITICAL_IMAGES.indexOf(imagePath);
  if (index > -1) {
    CRITICAL_IMAGES.splice(index, 1);
    // console.log(`🗑️ 从预加载列表移除图片: ${imagePath}`); 已移除
  }
}

/**
 * 获取当前预加载列表
 * @returns string[] 图片路径数组
 */
export function getPreloadList(): string[] {
  return [...CRITICAL_IMAGES];
}

/**
 * 检查图片是否已缓存
 * @param src 图片路径
 * @returns boolean
 */
export function isImageCached(src: string): boolean {
  const img = new Image();
  img.src = src;
  return img.complete && img.naturalHeight !== 0;
}

/**
 * 预加载指定的图片（立即执行）
 * @param imagePaths 图片路径数组
 * @returns Promise<HTMLImageElement[]>
 */
export function preloadImagesNow(imagePaths: string[]): Promise<HTMLImageElement[]> {
  return preloadImages(imagePaths);
}