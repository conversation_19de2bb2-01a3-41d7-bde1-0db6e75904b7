<template>
  <!-- 协议弹窗 -->
  <Modal v-model="visible" :title="title" width="800px" title-icon="model-icon">
    <div class="agreement-content">
      <!-- 看货协议头部信息 -->
      <div v-if="agreementType === 'viewing' && userInfo" class="agreement-header">
        <div class="agreement-title">{{ title }}</div>
        <div class="agreement-item">
          <div class="item-label">公司名称：</div>
          <div class="item-value">{{ userInfo.qiyemingcheng || '' }}</div>
        </div>
        <div class="agreement-item">
          <div class="item-label">联系人：</div>
          <div class="item-value">{{ userInfo.cnname || '' }}</div>
        </div>
        <div class="agreement-item">
          <div class="item-label">联系电话：</div>
          <div class="item-value">{{ userInfo.mobile || '' }}</div>
        </div>
        <div class="agreement-declaration">
          鉴于我公司于<span class="text-underline">{{ today }}</span>对<span class="text-underline">{{ targetTitle }}</span> 进行了现场看货，现作出如下确认声明：
        </div>
      </div>
      
      <!-- 协议内容 -->
      <div class="agreement-text" v-html="content"></div>
      
      <!-- 倒计时提示 -->
      <div class="agreement-countdown" v-if="!canConfirm">
        请仔细阅读协议内容，{{ countdown }}秒后可确认
      </div>
    </div>
    <template #footer>
      <div class="agreement-footer">
        <button class="btn-cancel" @click="handleCancel">取消</button>
        <button
          class="btn-confirm"
          :disabled="!canConfirm"
          @click="handleConfirm"
        >
          {{
            canConfirm ? (agreementType === 'viewing' ? '我已看货' : '我同意') : `请等待 ${countdown}s`
          }}
        </button>
      </div>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, onUnmounted } from "vue";
import Modal from "@/components/Modal.vue";

// 定义组件名称
defineOptions({
  name: "AgreementModal",
});

// 定义用户信息接口
interface UserInfo {
  id: number;
  cnname: string; // 联系人
  mobile: string; // 联系电话
  qiyemingcheng: string; // 公司名称
  [key: string]: any;
}

// 定义 props
interface Props {
  modelValue: boolean; // 控制弹窗显示/隐藏
  title?: string; // 协议标题
  content?: string; // 协议内容
  countdownTime?: number; // 倒计时时间（秒）
  agreementType?: string; // 协议类型：'registration' 报名协议，'viewing' 看货协议
  userInfo?: UserInfo | null; // 用户信息
  targetTitle?: string; // 标的标题（用于看货协议）
}

const props = withDefaults(defineProps<Props>(), {
  title: "协议",
  content: "",
  countdownTime: 6,
  agreementType: "registration",
  userInfo: null,
  targetTitle: "",
});

// 定义 emits
interface Emits {
  "update:modelValue": [value: boolean]; // 更新弹窗显示状态
  confirm: []; // 确认协议事件
  cancel: []; // 取消协议事件
}

const emit = defineEmits<Emits>();

// 响应式数据
const countdown = ref<number>(props.countdownTime); // 协议倒计时
const canConfirm = ref<boolean>(false); // 是否可以确认协议
const timer = ref<number | null>(null); // 倒计时定时器

// 计算属性：控制弹窗显示
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit("update:modelValue", value),
});

/**
 * 开始倒计时
 */
const startCountdown = (): void => {
  countdown.value = props.countdownTime;
  canConfirm.value = false;
  
  timer.value = window.setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      canConfirm.value = true;
      if (timer.value) {
        clearInterval(timer.value);
        timer.value = null;
      }
    }
  }, 1000);
};

/**
 * 清除倒计时
 */
const clearCountdown = (): void => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
};

/**
 * 确认协议
 */
const handleConfirm = (): void => {
  clearCountdown();
  visible.value = false;
  emit("confirm");
};

/**
 * 取消协议
 */
const handleCancel = (): void => {
  clearCountdown();
  visible.value = false;
  emit("cancel");
};

// 监听弹窗显示状态，开始或停止倒计时
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      startCountdown();
    } else {
      clearCountdown();
    }
  },
  { immediate: true }
);

// 计算属性：获取当前日期
const today = computed(() => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  return `${year}年${month}月${day}日`;
});

// 组件卸载时清除定时器
onUnmounted(() => {
  clearCountdown();
});
</script>

<style scoped lang="scss">
/* 协议弹窗样式 */
.agreement-content {
  max-height: 500px;
  overflow-y: auto;
  padding: 20px;
}

/* 看货协议头部信息样式 */
.agreement-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e8e8e8;
}

.agreement-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  text-align: center;
}

.agreement-item {
  display: flex;
  margin-bottom: 8px;
  font-size: 14px;
}

.item-label {
  width: 80px;
  color: #666;
  font-weight: 500;
}

.item-value {
  flex: 1;
  color: #333;
  border-bottom: 1px solid #333;
  max-width: 200px;
}

.agreement-declaration {
  margin-top: 15px;
  line-height: 1.6;
  color: #333;
  font-size: 14px;
}

.text-underline {
  border-bottom: 1px solid #333;
  padding-bottom: 1px;
}

.agreement-text {
  line-height: 1.6;
  color: #333;
  margin-bottom: 20px;
}

.agreement-countdown {
  text-align: center;
  color: #ff6b35;
  font-weight: bold;
  padding: 10px;
  background: #fff3f0;
  border-radius: 4px;
  margin-top: 15px;
}

.agreement-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.agreement-footer button {
  padding: 8px 20px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.btn-cancel {
  background: #f5f5f5;
  color: #666;
}

.btn-cancel:hover {
  background: #e8e8e8;
}

.btn-confirm {
  background: #004c66;
  color: white;
  min-width: 100px;
}

.btn-confirm:hover:not(:disabled) {
  background: rgba($color: #004c66, $alpha: 0.8);
}

.btn-confirm:disabled {
  background: #ccc;
  cursor: not-allowed;
}
</style>