<template>
  <!-- 忘记密码表单 -->
  <el-form
    ref="forgotFormRef"
    :model="forgotForm"
    :rules="forgotRules"
    :validate-on-rule-change="false"
    class="login-form"
  >
    <!-- 手机号输入框 -->
    <el-form-item prop="phone">
      <el-input
        v-model="forgotForm.phone"
        placeholder="请输入手机号"
        size="large"
        class="login-input"
        maxlength="11"
      />
    </el-form-item>

    <!-- 验证码输入框 -->
    <el-form-item prop="smsCode" class="password">
      <el-input
        v-model="forgotForm.smsCode"
        placeholder="请输入验证码"
        size="large"
        class="login-input sms-input-with-button"
        maxlength="6"
      >
        <template #suffix>
          <span
            class="sms-text-button"
            :class="{ disabled: isSmsButtonDisabled() }"
            @click="handleSendSms"
          >
            {{ getSmsButtonText() }}
          </span>
        </template>
      </el-input>
    </el-form-item>

    <!-- 新密码输入框 -->
    <el-form-item prop="newPassword">
      <el-input
        v-model="forgotForm.newPassword"
        type="password"
        placeholder="请输入新密码"
        size="large"
        class="login-input"
        show-password
      />
    </el-form-item>

    <!-- 确认新密码输入框 -->
    <el-form-item prop="confirmPassword">
      <el-input
        v-model="forgotForm.confirmPassword"
        type="password"
        placeholder="请再次输入新密码"
        size="large"
        class="login-input"
        show-password
      />
    </el-form-item>

    <!-- 重置密码按钮 -->
    <el-form-item>
      <el-button
        type="primary"
        size="large"
        class="login-button"
        :loading="resetLoading"
        @click="handleResetPassword"
      >
        重置密码
      </el-button>
    </el-form-item>

    <!-- 返回登录链接 -->
    <div class="login-link">
      <span>想起密码了？</span>
      <a href="#" @click.prevent="handleBackToLogin">返回登录</a>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from "element-plus";
import type { FormInstance, FormRules } from 'element-plus'
import { authApi } from '@/utils/api'
import { useSms } from '../composables/useSms'

// 定义组件事件
const emit = defineEmits<{
  switchTab: [tab: string]
  resetSuccess: []
}>()

// 使用短信验证码组合式函数
const { sendSms, getSmsButtonText, isSmsButtonDisabled } = useSms()

// 重置密码加载状态
const resetLoading = ref(false)

// 表单引用
const forgotFormRef = ref<FormInstance>()

// 忘记密码表单数据
const forgotForm = reactive({
  phone: '', // 手机号
  smsCode: '', // 短信验证码
  newPassword: '', // 新密码
  confirmPassword: '' // 确认新密码
})

// 确认密码验证器
const validateConfirmPassword = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入新密码'))
  } else if (value !== forgotForm.newPassword) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 忘记密码验证规则
const forgotRules: FormRules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号格式',
      trigger: 'blur'
    }
  ],
  smsCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    {
      pattern: /^\d{4}$/,
      message: '验证码为4位数字',
      trigger: 'blur'
    }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20位', trigger: 'blur' },
    {
      pattern: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/,
      message: '密码必须包含字母和数字',
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

/**
 * 发送短信验证码
 */
const handleSendSms = async () => {
  // 先验证手机号
  if (!forgotFormRef.value) return

  try {
    await forgotFormRef.value.validateField('phone')
    // 调用公共的发送短信方法
    await sendSms(forgotForm.phone)
  } catch (error) {
    // 验证失败，不发送短信
    console.log('手机号验证失败')
  }
}

/**
 * 处理重置密码
 */
const handleResetPassword = async () => {
  if (!forgotFormRef.value) return

  try {
    // 表单验证
    await forgotFormRef.value.validate()

    resetLoading.value = true

    // 调用重置密码API
    const response = await authApi.resetPassword({
      mobile: forgotForm.phone,
      yzcode: forgotForm.smsCode,
      password: forgotForm.newPassword
    })

    if (response.code === 1) {
      // 重置成功
      ElMessage.success('密码重置成功，请使用新密码登录')
      emit('resetSuccess')
      // 返回密码登录页面
      emit('switchTab', 'password')
      
      // 清空表单
      resetForm()
    } else {
      ElMessage.error(response.msg || '密码重置失败，请重试')
    }
  } catch (error) {
    console.error('重置密码失败:', error)
    ElMessage.error('密码重置失败，请重试')
  } finally {
    resetLoading.value = false
  }
}

/**
 * 返回登录页面
 */
const handleBackToLogin = () => {
  emit('switchTab', 'password')
}

/**
 * 重置表单
 */
const resetForm = () => {
  if (forgotFormRef.value) {
    forgotFormRef.value.resetFields()
  }
  Object.assign(forgotForm, {
    phone: '',
    smsCode: '',
    newPassword: '',
    confirmPassword: ''
  })
}
</script>

<style lang="scss" scoped>
.login-form {
  .el-form-item {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .password {
    margin-bottom: 24px;
  }

  .login-input {
    width: 100%;
    height: 46px;
  }

  .sms-input-with-button {
    :deep(.el-input__suffix) {
      padding-right: 0px;
    }
  }

  .sms-text-button {
    color: #004c66;
    font-size: 12px;
    font-weight: 400;
    cursor: pointer;
    user-select: none;
    transition: opacity 0.3s ease;

    &:hover:not(.disabled) {
      opacity: 0.8;
    }

    &.disabled {
      color: #ccc;
      cursor: not-allowed;
    }
  }



  .login-button {
    width: 100%;
    height: 48px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;

    &:hover {
      background: rgba($color: #004c66, $alpha: 0.9);
    }
  }
}

.login-link {
  text-align: center;
  margin-top: 24px;
  color: #666;
  font-size: 14px;

  a {
    color: #004c66;
    text-decoration: none;
    margin-left: 4px;

    &:hover {
      text-decoration: underline;
      background-color: transparent;
    }
  }
}
</style>