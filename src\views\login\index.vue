<template>
  <div class="login-container">
    <!-- 左侧3D logo区域 -->
    <div class="login-left">
      <!-- 使用提供的3D SVG图标 -->
      <div class="logo-3d-wrapper">
        <img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/1_1754963030315.png" alt="" class="logo-3d-image-1">
        <img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/2_1754963119472.png" alt="" class="logo-3d-image-2">
        <img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/3_1754963135630.png" alt="" class="logo-3d-image-3">
      </div>
    </div>

    <!-- 右侧登录表单区域 -->
    <div class="login-right">
      <div class="login-form-container">
        <!-- logo图标 -->
        <div class="form-logo">
          <SvgIcon iconName="login-logo" className="logo-icon" />
        </div>

        <!-- 欢迎标题 -->
        <h2 class="welcome-title">{{ getTitle() }}</h2>

        <!-- 登录方式选项卡 -->
        <div v-if="isLoginMode" class="tab-container">
          <div
            class="tab-item"
            :class="{ active: currentTab === 'password' }"
            @click="switchTab('password')"
          >
            账号密码登录
          </div>
          <div
            class="tab-item"
            :class="{ active: currentTab === 'sms' }"
            @click="switchTab('sms')"
          >
            验证码登录
          </div>
        </div>

        <!-- 动态组件渲染 -->
        <component 
          :is="currentComponent" 
          @switch-tab="switchTab"
          @login-success="handleLoginSuccess"
          @register-success="handleRegisterSuccess"
          @reset-success="handleResetSuccess"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import SvgIcon from '@/components/SvgIcon.vue'

// 导入各个登录组件
import PasswordLogin from './components/PasswordLogin.vue'
import SmsLogin from './components/SmsLogin.vue'
import Register from './components/Register.vue'
import ForgotPassword from './components/ForgotPassword.vue'

// 路由实例
const router = useRouter()

// 当前选中的标签页
const currentTab = ref('password')

// 组件映射
const componentMap = {
  password: PasswordLogin,
  sms: SmsLogin,
  register: Register,
  forgot: ForgotPassword
}

// 当前组件
const currentComponent = computed(() => {
  return componentMap[currentTab.value as keyof typeof componentMap]
})

// 是否为登录模式（显示tab切换）
const isLoginMode = computed(() => {
  return ['password', 'sms'].includes(currentTab.value)
})

/**
 * 获取标题
 */
const getTitle = () => {
  const titleMap = {
    password: '欢迎登陆',
    sms: '欢迎登陆',
    register: '用户注册',
    forgot: '重置密码'
  }
  return titleMap[currentTab.value as keyof typeof titleMap] || '欢迎登陆'
}

/**
 * 切换标签页
 * @param tab 标签页类型
 */
const switchTab = (tab: string) => {
  currentTab.value = tab
}

/**
 * 处理登录成功
 */
const handleLoginSuccess = () => {
  // 登录成功后跳转到首页
  router.push('/')
}

/**
 * 处理注册成功
 */
const handleRegisterSuccess = () => {
  // 注册成功后切换到登录页面
  switchTab('password')
}

/**
 * 处理重置密码成功
 */
const handleResetSuccess = () => {
  // 重置密码成功后切换到登录页面
  switchTab('password')
}
</script>

<style lang="scss" scoped>
.login-container {
  display: flex;
  position: relative;
  overflow: hidden;
  height: 885px;
  // 引入login-bg.png
  background: url("https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/login-bg_1754963733964.jpg") no-repeat;
  background-size: cover;
  background-position: center;
  &::-webkit-scrollbar {
    display: none;
  }
}

// 左侧3D logo区域
.login-left {
  flex: 1.6;
  display: flex;
  align-items: center;
  justify-content: center;
  .logo-3d-wrapper {
    width: 680px;
    height: 601.18px;
    position: relative;
    color: #fff;
    .logo-3d-image-1 {
      width: 201.6px;
      height: 325.51px;
      position: absolute;
      top: 110px;
      left: 420px;
      z-index: 2;
      // 第一个图标：简单上下浮动动画，3秒周期
      animation: float3d 3s ease-in-out infinite;
    }
    .logo-3d-image-2 {
      position: absolute;
      top: -9px;
      left: 110px;
      width: 379.39px;
      height: 504.01px;
      z-index: 1;
      // 第二个图标：上下浮动动画，4秒周期，延迟1秒开始（错开效果）
      animation: floatSecond 3s ease-in-out infinite 1s;
    }
    .logo-3d-image-3 {
      position: absolute;
      bottom: 9px;
      left: -30px;
      width: 680px;
      height: 378.6px;
      // 第三个图标：圆盘旋转动画，8秒周期
      // animation: rotateCircle 8s linear infinite;
    }

    // 响应式
    @media (max-width: 768px) {
     display: none;
    }
  }
}

// 简单上下浮动动画 - 第一个图标
@keyframes float3d {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

// 简单上下浮动动画 - 第二个图标（错开时间）
@keyframes floatSecond {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

// 简单旋转动画 - 第三个图标（圆盘平面旋转）
@keyframes rotateCircle {
  0% {
    transform: rotateZ(0deg);
  }
  100% {
    transform: rotateZ(360deg);
  }
}

// 右侧登录表单区域
.login-right {
  flex: 1.16;
  display: flex;
  position: relative;
  z-index: 1;
  padding-top: 163px;

  .login-form-container {
    width: 100%;
    max-width: 445px;

    .form-logo {
      margin-bottom: 21px;
      .logo-icon {
        width: 57px;
        height: 58px;
      }
    }

    .welcome-title {
      font-size: 30px;
      font-weight: 600;
      color: #004c66;
      margin-bottom: 55px;
      line-height: 1;
    }

    .tab-container {
      display: flex;
      margin-bottom: 24px;

      .tab-item {
        text-align: center;
        cursor: pointer;
        color: #666;
        font-size: 14px;

        &:hover {
          color: #004c66;
        }

        &.active {
          color: #004c66;
          font-weight: 500;
        }

        // 给第一个元素后添加分割线
        &:not(:last-child) {
          position: relative;
          margin-right: 20px;
          padding-right: 20px;
          &::after {
            content: "";
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 2px;
            height: 19px;
            background-color: #999;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .login-left {
    padding: 40px;
  }

  .login-right {
    width: 420px;
    padding: 40px;
  }
}

@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
  }

  .login-left {
    flex: none;
    height: 300px;
    padding: 20px;
  }

  .login-right {
    width: 100%;
    padding: 30px 20px;
  }
}
</style>