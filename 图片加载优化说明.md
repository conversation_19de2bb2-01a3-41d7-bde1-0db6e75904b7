# 图片加载优化说明

## 问题描述
部署到线上后，本地图片首次进入页面加载很慢，特别是Header组件中的推荐专区背景图片。

## 优化方案

### 1. 图片预加载机制

#### 创建预加载工具 (`src/utils/imagePreloader.ts`)
- **功能**：在应用启动时预加载关键图片
- **优势**：提前加载首屏关键图片，减少用户等待时间
- **使用方式**：在 `main.ts` 中自动调用

```typescript
// 关键图片列表
const CRITICAL_IMAGES = [
  '/src/assets/images/recommend-bg.png', // Header推荐专区背景图
  '/src/assets/images/login-bg.png', // 登录背景图
  '/src/assets/images/banner-about.png', // 关于我们banner
];
```

#### 预加载功能
- `preloadImage()`: 预加载单个图片
- `preloadImages()`: 批量预加载图片
- `preloadCriticalImages()`: 预加载关键图片

### 2. 图片引用方式优化

#### 原始方式（问题）
```css
/* 使用绝对路径，Vite无法优化 */
background-image: url("/src/assets/images/recommend-bg.png");
```

#### 优化后方式
```vue
<script setup>
// 导入图片资源，让Vite进行优化处理
import recommendBgImage from "@/assets/images/recommend-bg.png";
</script>

<style>
/* 使用v-bind绑定导入的图片变量 */
background-image: url(v-bind(recommendBgImage));
</style>
```

**优势**：
- Vite可以对导入的图片进行优化处理
- 支持图片压缩、格式转换等
- 更好的缓存策略

### 3. 静态资源构建优化

#### Vite配置优化 (`vite.config.ts`)
```typescript
build: {
  // 静态资源处理
  assetsDir: 'assets',
  // 小于4kb的图片转为base64
  assetsInlineLimit: 4096,
  rollupOptions: {
    output: {
      // 静态资源分类打包
      assetFileNames: (assetInfo) => {
        // 图片资源分类
        if (/\.(png|jpe?g|gif|svg|webp|ico)$/i.test(assetInfo.name || '')) {
          return `assets/images/[name]-[hash][extname]`
        }
        // 字体资源分类
        if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name || '')) {
          return `assets/fonts/[name]-[hash][extname]`
        }
        return `assets/[exttype]/[name]-[hash][extname]`
      }
    }
  }
}
```

**优化效果**：
- 小图片自动转为base64，减少HTTP请求
- 静态资源分类打包，便于缓存管理
- 文件名添加hash，支持长期缓存

### 4. 图片懒加载机制

#### ImageLazyLoader类
```typescript
// 使用方式
import { imageLazyLoader } from '@/utils/imagePreloader'

// 观察图片元素
imageLazyLoader.observe(imgElement)
```

**功能**：
- 基于IntersectionObserver API
- 图片进入视口前50px开始加载
- 自动移除观察，避免内存泄漏

### 5. 图片优化工具

#### ImageOptimizer类
```typescript
// WebP格式支持检测
const optimizedSrc = ImageOptimizer.getOptimizedImageSrc(originalSrc)

// 高分辨率设备适配
const retinaSrc = ImageOptimizer.getRetinaImageSrc(baseSrc)
```

**功能**：
- 自动检测WebP支持，优先使用WebP格式
- 根据设备像素比提供合适分辨率的图片

## 使用指南

### 1. 添加新的关键图片
在 `src/utils/imagePreloader.ts` 中的 `CRITICAL_IMAGES` 数组添加新图片路径：

```typescript
const CRITICAL_IMAGES = [
  '/src/assets/images/recommend-bg.png',
  '/src/assets/images/your-new-image.png', // 新增图片
];
```

### 2. 在组件中使用图片
```vue
<script setup>
// 导入图片资源
import myImage from '@/assets/images/my-image.png'
</script>

<template>
  <!-- 直接使用 -->
  <img :src="myImage" alt="描述" />
</template>

<style>
/* CSS中使用 */
.my-class {
  background-image: url(v-bind(myImage));
}
</style>
```

### 3. 实现图片懒加载
```vue
<script setup>
import { onMounted, ref } from 'vue'
import { imageLazyLoader } from '@/utils/imagePreloader'

const imgRef = ref<HTMLImageElement>()

onMounted(() => {
  if (imgRef.value) {
    imageLazyLoader.observe(imgRef.value)
  }
})
</script>

<template>
  <img 
    ref="imgRef"
    data-src="/path/to/image.jpg" 
    alt="懒加载图片"
  />
</template>
```

## 性能提升效果

### 1. 首屏加载优化
- **预加载机制**：关键图片提前加载，减少首屏白屏时间
- **资源内联**：小图片转base64，减少HTTP请求数量

### 2. 缓存优化
- **文件hash**：支持长期缓存，减少重复下载
- **分类打包**：便于设置不同的缓存策略

### 3. 网络优化
- **格式优化**：自动使用WebP等现代格式
- **分辨率适配**：根据设备提供合适的图片尺寸

### 4. 用户体验优化
- **渐进加载**：图片加载过程中的过渡效果
- **懒加载**：非关键图片延迟加载，提升页面响应速度

## 注意事项

### 1. 图片格式建议
- **WebP**：现代浏览器首选，体积小质量高
- **PNG**：需要透明背景时使用
- **JPEG**：照片类图片使用
- **SVG**：图标和简单图形使用

### 2. 图片尺寸建议
- **背景图**：根据设计稿提供1x和2x版本
- **内容图片**：提供多种尺寸适配不同屏幕
- **图标**：优先使用SVG格式

### 3. 性能监控
- 使用浏览器开发者工具监控图片加载时间
- 关注Core Web Vitals指标
- 定期检查图片优化效果

## 故障排除

### 1. 图片加载失败
- 检查图片路径是否正确
- 确认图片文件是否存在
- 查看控制台错误信息

### 2. 预加载不生效
- 检查 `main.ts` 中是否正确调用预加载函数
- 确认图片路径在 `CRITICAL_IMAGES` 数组中
- 查看控制台预加载日志

### 3. 懒加载问题
- 确认使用了 `data-src` 属性
- 检查 `IntersectionObserver` 浏览器兼容性
- 验证图片元素是否正确传递给观察器

通过以上优化措施，可以显著提升图片加载性能，改善用户体验。建议根据实际项目需求调整配置参数。