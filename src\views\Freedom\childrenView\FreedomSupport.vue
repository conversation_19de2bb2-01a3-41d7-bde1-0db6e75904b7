<template>
  <div class="page">
    <ServiceSupport
      title="嗨！有什么需要帮忙的吗？"
      placeholder="搜索答案"
      :navigation-items="NavigationItems"
      :on-search="handleSearch"
      :on-get-questions="handleGetQuestions"
      :on-helpful-feedback="handleHelpfulFeedback"
    />
  </div>
</template>

<script setup lang="ts">
import ServiceSupport from '@/components/ServiceSupport.vue'

// 测试用的导航项目
const NavigationItems = [
  { icon: 'support-question', text: '常见问题' },
  { icon: 'support-freedom-account', text: '结算规则' },
  { icon: 'support-freedom-flow', text: '自由交易流程' },
  { icon: 'support-freedom-rule', text: '自由交易规则' }
]

// 测试用的问题数据
const Questions = [
  [
    {
      title: '如何注册账户？',
      answer: '您可以点击页面右上角的"注册"按钮，填写相关信息完成注册。'
    },
    {
      title: '忘记密码怎么办？',
      answer: '可以通过"忘记密码"功能重置密码，系统会发送验证码到您的手机。'
    }
  ],
  [
    {
      title: '如何完成实名认证？',
      answer: '进入个人中心，上传身份证照片并填写真实信息即可。'
    }
  ],
  [
    {
      title: '支持哪些支付方式？',
      answer: '支持支付宝、微信支付、银行卡等多种支付方式。'
    }
  ],
  [
    {
      title: '如何参与竞拍？',
      answer: '完成实名认证后，缴纳保证金即可参与竞拍。'
    }
  ]
]

// 处理搜索
const handleSearch = (keyword: string) => {
  // console.log('搜索关键词:', keyword)
  alert(`搜索: ${keyword}`)
}

// 获取问题列表
const handleGetQuestions = async (navIndex: number) => {
  console.log('获取问题列表，导航索引:', navIndex)
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  return Questions[navIndex] || []
}

// 处理反馈
const handleHelpfulFeedback = (questionIndex: number, isHelpful: boolean) => {
  console.log('反馈:', { questionIndex, isHelpful })
  const message = isHelpful ? '感谢您的反馈！' : '我们会继续改进！'
  alert(message)
}
</script>

<style scoped>
.page {
  width: 100%;
  min-height: 100vh;
}
</style>