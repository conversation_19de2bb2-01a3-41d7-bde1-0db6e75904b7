import { ref } from 'vue'
import type { AuctionItem } from '../types/auction'
import { auctionApi } from '../utils/api'

/**
 * 拍卖数据管理的组合式函数
 * 提供获取即将开始和已完成拍品的功能
 */
export function useAuctions() {
  // 即将开始的拍品数据
  const auctionItems = ref<AuctionItem[]>([])
  
  // 已完成的拍品数据
  const accomplishItems = ref<AuctionItem[]>([])
  
  // 加载状态
  const loading = ref(false)
  
  /**
   * 获取即将开始的拍品
   * @param limit 限制返回数量，默认为4
   */
  const getUpcomingAuctions = async (limit: number = 4) => {
    try {
      loading.value = true
      const response = await auctionApi.getAuctionList({
        province: "0",
        city: "0",
        keyword: "",
        page: 1,
        status: 1,
      })
      
      if (response.code == 1 && response.data.data.length > 0) {
        // 创建Promise数组，用于并行获取详细数据
        const promises = response.data.data.slice(0, limit).map(async (item: any) => {
          const obj: AuctionItem = {
            id: item.id,
            pmhId: item.pmh_id,
            bdName: item.bd_title,
            startTime: item.start_time_name,
            endTime: item.end_time_name,
            bdPic: "https://huigupaimai.oss-cn-beijing.aliyuncs.com/" + item.bd_url,
            bdQipaijia: item.bd_qipaijia,
            qpjDanwie: item.qpj_danwie,
            bdWeiguan: item.bd_weiguan,
            timeLabel: "截止报名",
            scheduleLabel: "预计开始",
            status: item.bd_status,
          }
          return obj
        })
        
        // 等待所有异步操作完成，获取实际的数据数组
        const list = await Promise.all(promises)
        
        // 更新auctionItems响应式数据
        auctionItems.value = list
      }
    } catch (error) {
      console.error("获取即将开始的拍品失败:", error)
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 获取已完成的拍品
   * @param limit 限制返回数量，默认为4
   */
  const getAccomplishAuctions = async (limit: number = 4) => {
    try {
      loading.value = true
      const response = await auctionApi.getAuctionList({
        province: "0",
        city: "0",
        keyword: "",
        page: 1,
        status: 5,
      })
      
      if (response.code == 1 && response.data.data.length > 0) {
        // 创建Promise数组，用于并行获取详细数据
        const promises = response.data.data.slice(0, limit).map(async (item: any) => {
          const obj: AuctionItem = {
            id: item.id,
            pmhId: item.pmh_id,
            bdName: item.bd_title,
            startTime: item.start_time_name,
            endTime: item.end_time_name,
            bdPic: "https://huigupaimai.oss-cn-beijing.aliyuncs.com/" + item.bd_url,
            bdQipaijia: item.bd_chengjiaojia,
            qpjDanwie: item.cjj_danwie,
            bdWeiguan: item.bd_weiguan,
            timeLabel: "截止报名",
            scheduleLabel: "预计开始",
            status: item.bd_status
          }
          return obj
        })
        
        // 等待所有异步操作完成，获取实际的数据数组
        const list = await Promise.all(promises)
        
        // 更新accomplishItems响应式数据
        accomplishItems.value = list
      }
    } catch (error) {
      console.error("获取已完成的拍品失败:", error)
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 初始化数据，同时获取即将开始和已完成的拍品
   * @param limit 限制返回数量，默认为4
   */
  const initAuctions = async (limit: number = 4) => {
    await Promise.all([
      getUpcomingAuctions(limit),
      getAccomplishAuctions(limit)
    ])
  }
  
  /**
   * 刷新所有数据
   * @param limit 限制返回数量，默认为4
   */
  const refreshAuctions = async (limit: number = 4) => {
    auctionItems.value = []
    accomplishItems.value = []
    await initAuctions(limit)
  }
  
  return {
    // 响应式数据
    auctionItems,
    accomplishItems,
    loading,
    
    // 方法
    getUpcomingAuctions,
    getAccomplishAuctions,
    initAuctions,
    refreshAuctions
  }
}