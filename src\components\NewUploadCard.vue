<template>
  <div class="new-upload-card">
    <!-- 已上传图片显示区域 -->
    <div v-if="currentImageUrl" class="image-display">
      <el-image
        ref="imageRef"
        :src="currentImageUrl"
        :preview-src-list="[currentImageUrl]"
        class="uploaded-image"
        fit="cover"
      />
      <!-- 操作按钮遮罩层 -->
      <div class="upload-overlay">
        <div class="overlay-actions">
          <!-- 重新上传区域 -->
          <div class="action-item reupload-action" @click="triggerFileInput">
            <SvgIcon iconName="upload-up" className="action-icon" />
            <span class="action-text">重新上传</span>
          </div>
          <!-- 预览图片区域 -->
          <div class="action-item preview-action" @click="previewImage">
            <SvgIcon iconName="freedom-propertyDetail-eye" className="action-icon" />
            <span class="action-text">预览图片</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 上传区域 -->
    <div v-else class="upload-area" @click="handleUpload">
      <div class="upload-placeholder" :style="backgroundStyle">
        <!-- 上传图标（固定为upload图标） -->
        <SvgIcon iconName="upload-up" className="upload-icon" />
        <!-- 上传文字说明 -->
        <span class="upload-text">{{ uploadText }}</span>
      </div>
    </div>
    
    <!-- 隐藏的文件输入框 -->
    <input
      ref="fileInputRef"
      type="file"
      accept="image/*"
      style="display: none"
      @change="handleFileChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import SvgIcon from '@/components/SvgIcon.vue'
import { systemApi } from '@/utils/api-new'

// 定义组件属性接口
interface Props {
  uploadText: string // 上传文字说明
  backgroundImage?: string // 背景图片URL（可选）
  imageUrl?: string // 已上传的图片URL（可选）
  maxSize?: number // 最大文件大小（MB），默认5MB
}

// 定义组件事件接口
interface Emits {
  (e: 'upload-success', data: { url: string; file: File }): void // 上传成功事件
  (e: 'upload-error', error: any): void // 上传失败事件
}

// 接收父组件传入的属性
const props = withDefaults(defineProps<Props>(), {
  maxSize: 5
})

// 定义组件事件
const emit = defineEmits<Emits>()

// 文件输入框引用
const fileInputRef = ref<HTMLInputElement>()

// 图片预览引用
const imageRef = ref()

// 当前显示的图片URL
const currentImageUrl = ref<string>(props.imageUrl || '')

// 上传状态
const uploading = ref(false)

// 监听props.imageUrl的变化
watch(
  () => props.imageUrl,
  (newUrl) => {
    if (newUrl) {
      // 如果是完整URL，直接使用；否则拼接OSS域名
      if (newUrl.startsWith('http')) {
        currentImageUrl.value = newUrl
      } else {
        currentImageUrl.value = `https://huigupaimai.oss-cn-beijing.aliyuncs.com/${newUrl}`
      }
    }
  },
  { immediate: true }
)

// 计算背景图片样式
const backgroundStyle = computed(() => {
  if (props.backgroundImage) {
    let imageUrl = props.backgroundImage
    // 处理@/路径
    if (imageUrl.startsWith('@/')) {
      imageUrl = imageUrl.replace('@/', '/src/')
    }
    return {
      backgroundImage: `url(${imageUrl})`,
      backgroundSize: 'contain',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat'
    }
  }
  return {}
})

/**
 * 处理上传点击事件
 */
const handleUpload = () => {
  if (uploading.value) return
  fileInputRef.value?.click()
}

/**
 * 触发文件输入
 */
const triggerFileInput = (event: Event) => {
  event.stopPropagation()
  if (uploading.value) return
  fileInputRef.value?.click()
}

/**
 * 预览图片
 */
const previewImage = (event: Event) => {
  event.stopPropagation()
  
  if (currentImageUrl.value && imageRef.value) {
    imageRef.value.showPreview()
  }
}

/**
 * 处理文件选择变化事件
 */
const handleFileChange = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (!file) return
  
  // 验证文件大小
  if (file.size > props.maxSize * 1024 * 1024) {
    ElMessage.error(`文件大小不能超过${props.maxSize}MB`)
    return
  }
  
  // 验证文件类型
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp']
  if (!allowedTypes.includes(file.type)) {
    ElMessage.error('只支持 gif、png、jpg、bmp 格式的图片')
    return
  }
  
  // 开始上传
  uploading.value = true
  
  try {
    // 创建FormData
    const formData = new FormData()
    formData.append('file', file)
    formData.append('biz', 'temp') // 业务类型
    
    // 调用上传接口
    const response = await systemApi.uploadFile(formData)
    
    if (response.success) {
      // 上传成功，更新显示的图片
      const uploadedUrl = response.message || response.result
      currentImageUrl.value = `https://huigupaimai.oss-cn-beijing.aliyuncs.com/${uploadedUrl}`
      
      // 触发成功事件
      emit('upload-success', { url: uploadedUrl, file })
      ElMessage.success('上传成功')
    } else {
      throw new Error(response.message || '上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    emit('upload-error', error)
    ElMessage.error('上传失败，请重试')
  } finally {
    uploading.value = false
  }
  
  // 清空输入框值
  target.value = ''
}
</script>

<style scoped lang="scss">
.new-upload-card {
  display: inline-block;
  
  // 已上传图片显示区域
  .image-display {
    width: 101px;
    height: 75px;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    
    .uploaded-image {
      width: 100%;
      height: 100%;
      border-radius: 10px;
    }
    
    // 重新上传遮罩层
    .upload-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
      border-radius: 10px;
    }
    
    .overlay-actions {
      display: flex;
      gap: 5px;
      align-items: center;
      justify-content: center;
      width: 100%;
    }
    
    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      cursor: pointer;
      transition: all 0.3s ease;
      padding: 8px 2px;
      border-radius: 6px;
    }
    
    .action-icon {
      width: 16px;
      height: 16px;
      color: #fff;
      transition: color 0.3s ease;
    }
    
    .action-text {
      font-size: 10px;
      color: #fff;
      text-align: center;
      line-height: 1.2;
      font-weight: 500;
    }
    
    .reupload-action:hover .action-icon {
      color: #67c23a;
    }
    
    .reupload-action:hover .action-text {
      color: #67c23a;
    }
    
    .preview-action:hover .action-icon {
      color: #409eff;
    }
    
    .preview-action:hover .action-text {
      color: #409eff;
    }
    
    &:hover .upload-overlay {
      opacity: 1;
    }
  }
  
  // 原始上传区域
  .upload-area {
    width: 101px;
    height: 75px;
    background-color: #f2f2f2;
    border-radius: 10px;
    padding: 3px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    
    &:hover {
      background-color: #e8e8e8;
    }
    
    .upload-placeholder {
      width: 100%;
      height: 100%;
      border: 2px dashed #ccc;
      border-radius: 7px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 4px;
      position: relative;
      background-position: center;
      background-repeat: no-repeat;
      
      .upload-icon {
        width: 20px;
        height: 20px;
        color: #999;
        z-index: 2;
        position: relative;
      }
      
      .upload-text {
        font-size: 12px;
        color: #999;
        text-align: center;
        line-height: 1.2;
        z-index: 2;
        position: relative;
      }
    }
    
    // 上传中状态
    &.uploading {
      pointer-events: none;
      
      .upload-placeholder {
        border-color: #409eff;
        
        .upload-icon,
        .upload-text {
          color: #409eff;
        }
      }
    }
  }
}
</style>
