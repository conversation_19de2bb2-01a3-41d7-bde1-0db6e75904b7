<template>
  <div class="tiptap-editor" :class="{ 'tiptap-editor--disabled': disabled }">
    <!-- 工具栏 -->
    <div v-if="!hideToolbar" class="tiptap-toolbar">
      <!-- 字体设置 -->
      <div class="toolbar-group">
        <el-dropdown trigger="click">
          <button type="button" class="toolbar-btn toolbar-btn--dropdown" title="字体">
            <span>{{ getCurrentFontFamily() }}</span>
            <el-icon><ArrowDown /></el-icon>
          </button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="setFontFamily('')">默认字体</el-dropdown-item>
              <el-dropdown-item divided>系统字体</el-dropdown-item>
              <el-dropdown-item @click="setFontFamily('SimSun')">宋体</el-dropdown-item>
              <el-dropdown-item @click="setFontFamily('SimHei')">黑体</el-dropdown-item>
              <el-dropdown-item @click="setFontFamily('Microsoft YaHei')">微软雅黑</el-dropdown-item>
              <el-dropdown-item @click="setFontFamily('KaiTi')">楷体</el-dropdown-item>
              <el-dropdown-item @click="setFontFamily('FangSong')">仿宋</el-dropdown-item>
              <el-dropdown-item divided>英文字体</el-dropdown-item>
              <el-dropdown-item @click="setFontFamily('Arial')">Arial</el-dropdown-item>
              <el-dropdown-item @click="setFontFamily('Times New Roman')">Times New Roman</el-dropdown-item>
              <el-dropdown-item @click="setFontFamily('Courier New')">Courier New</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <el-dropdown trigger="click">
          <button type="button" class="toolbar-btn toolbar-btn--dropdown" title="字号">
            <span>{{ getCurrentFontSize() }}</span>
            <el-icon><ArrowDown /></el-icon>
          </button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="setFontSize('12px')">12px</el-dropdown-item>
              <el-dropdown-item @click="setFontSize('14px')">14px</el-dropdown-item>
              <el-dropdown-item @click="setFontSize('16px')">16px</el-dropdown-item>
              <el-dropdown-item @click="setFontSize('18px')">18px</el-dropdown-item>
              <el-dropdown-item @click="setFontSize('20px')">20px</el-dropdown-item>
              <el-dropdown-item @click="setFontSize('24px')">24px</el-dropdown-item>
              <el-dropdown-item @click="setFontSize('28px')">28px</el-dropdown-item>
              <el-dropdown-item @click="setFontSize('32px')">32px</el-dropdown-item>
              <el-dropdown-item @click="setFontSize('36px')">36px</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <!-- 分隔符 -->
      <div class="toolbar-divider"></div>

      <!-- 基础格式化 -->
      <div class="toolbar-group">
        <button
          type="button"
          class="toolbar-btn"
          :class="{ 'is-active': editor?.isActive('bold') }"
          @click="editor?.chain().focus().toggleBold().run()"
          title="粗体"
        >
          <strong>B</strong>
        </button>
        <button
          type="button"
          class="toolbar-btn"
          :class="{ 'is-active': editor?.isActive('italic') }"
          @click="editor?.chain().focus().toggleItalic().run()"
          title="斜体"
        >
          <em>I</em>
        </button>
        <button
          type="button"
          class="toolbar-btn"
          :class="{ 'is-active': editor?.isActive('underline') }"
          @click="editor?.chain().focus().toggleUnderline().run()"
          title="下划线"
        >
          <u>U</u>
        </button>
        <button
          type="button"
          class="toolbar-btn"
          :class="{ 'is-active': editor?.isActive('strike') }"
          @click="editor?.chain().focus().toggleStrike().run()"
          title="删除线"
        >
          <s>S</s>
        </button>
      </div>

      <!-- 分隔符 -->
      <div class="toolbar-divider"></div>

      <!-- 文字颜色和背景色 -->
      <div class="toolbar-group">
        <el-dropdown trigger="click">
          <button type="button" class="toolbar-btn" title="文字颜色">
            <span>A</span>
            <div class="color-indicator" :style="{ backgroundColor: getCurrentTextColor() }"></div>
          </button>
          <template #dropdown>
            <div class="color-picker">
              <div class="color-row">
                <div
                  v-for="color in textColors"
                  :key="color"
                  class="color-item"
                  :style="{ backgroundColor: color }"
                  @click="setTextColor(color)"
                  :title="color"
                ></div>
              </div>
              <div class="color-row">
                <button class="color-clear-btn" @click="clearTextColor()">清除颜色</button>
              </div>
            </div>
          </template>
        </el-dropdown>

        <el-dropdown trigger="click">
          <button type="button" class="toolbar-btn" title="背景颜色">
            <span>H</span>
            <div class="color-indicator" :style="{ backgroundColor: getCurrentHighlightColor() }"></div>
          </button>
          <template #dropdown>
            <div class="color-picker">
              <div class="color-row">
                <div
                  v-for="color in highlightColors"
                  :key="color"
                  class="color-item"
                  :style="{ backgroundColor: color }"
                  @click="setHighlightColor(color)"
                  :title="color"
                ></div>
              </div>
              <div class="color-row">
                <button class="color-clear-btn" @click="clearHighlightColor()">清除背景</button>
              </div>
            </div>
          </template>
        </el-dropdown>
      </div>

      <!-- 分隔符 -->
      <div class="toolbar-divider"></div>

      <!-- 标题 -->
      <div class="toolbar-group">
        <el-dropdown trigger="click">
          <button type="button" class="toolbar-btn toolbar-btn--dropdown" title="标题">
            <span>{{ getHeadingText() }}</span>
            <el-icon><ArrowDown /></el-icon>
          </button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="setParagraph()">正文</el-dropdown-item>
              <el-dropdown-item @click="setHeading(1)">标题 1</el-dropdown-item>
              <el-dropdown-item @click="setHeading(2)">标题 2</el-dropdown-item>
              <el-dropdown-item @click="setHeading(3)">标题 3</el-dropdown-item>
              <el-dropdown-item @click="setHeading(4)">标题 4</el-dropdown-item>
              <el-dropdown-item @click="setHeading(5)">标题 5</el-dropdown-item>
              <el-dropdown-item @click="setHeading(6)">标题 6</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <!-- 分隔符 -->
      <div class="toolbar-divider"></div>

      <!-- 对齐 -->
      <div class="toolbar-group">
        <button
          type="button"
          class="toolbar-btn"
          :class="{ 'is-active': editor?.isActive({ textAlign: 'left' }) }"
          @click="editor?.chain().focus().setTextAlign('left').run()"
          title="左对齐"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M2 3h12v1H2V3zm0 3h8v1H2V6zm0 3h12v1H2V9zm0 3h8v1H2v-1z"/>
          </svg>
        </button>
        <button
          type="button"
          class="toolbar-btn"
          :class="{ 'is-active': editor?.isActive({ textAlign: 'center' }) }"
          @click="editor?.chain().focus().setTextAlign('center').run()"
          title="居中对齐"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M2 3h12v1H2V3zm2 3h8v1H4V6zm-2 3h12v1H2V9zm2 3h8v1H4v-1z"/>
          </svg>
        </button>
        <button
          type="button"
          class="toolbar-btn"
          :class="{ 'is-active': editor?.isActive({ textAlign: 'right' }) }"
          @click="editor?.chain().focus().setTextAlign('right').run()"
          title="右对齐"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M2 3h12v1H2V3zm4 3h8v1H6V6zm-4 3h12v1H2V9zm4 3h8v1H6v-1z"/>
          </svg>
        </button>
        <button
          type="button"
          class="toolbar-btn"
          :class="{ 'is-active': editor?.isActive({ textAlign: 'justify' }) }"
          @click="editor?.chain().focus().setTextAlign('justify').run()"
          title="两端对齐"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M2 3h12v1H2V3zm0 3h12v1H2V6zm0 3h12v1H2V9zm0 3h12v1H2v-1z"/>
          </svg>
        </button>
      </div>

      <!-- 分隔符 -->
      <div class="toolbar-divider"></div>

      <!-- 列表 -->
      <div class="toolbar-group">
        <button
          type="button"
          class="toolbar-btn"
          :class="{ 'is-active': editor?.isActive('bulletList') }"
          @click="editor?.chain().focus().toggleBulletList().run()"
          title="无序列表"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <circle cx="3" cy="4" r="1"/>
            <circle cx="3" cy="8" r="1"/>
            <circle cx="3" cy="12" r="1"/>
            <path d="M6 3.5h8v1H6v-1zm0 4h8v1H6v-1zm0 4h8v1H6v-1z"/>
          </svg>
        </button>
        <button
          type="button"
          class="toolbar-btn"
          :class="{ 'is-active': editor?.isActive('orderedList') }"
          @click="editor?.chain().focus().toggleOrderedList().run()"
          title="有序列表"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M2 3.5h1v1H2v-1zm0 4h1v1H2v-1zm0 4h1v1H2v-1z"/>
            <path d="M6 3.5h8v1H6v-1zm0 4h8v1H6v-1zm0 4h8v1H6v-1z"/>
          </svg>
        </button>
      </div>

      <!-- 分隔符 -->
      <div class="toolbar-divider"></div>

      <!-- 链接和图片 -->
      <div class="toolbar-group">
        <button type="button" class="toolbar-btn" @click="setLink" title="插入链接">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M6.879 9.934a.5.5 0 0 1-.707-.707l.707.707zm-2.172-2.828a.5.5 0 1 1-.707-.707l.707.707zm8.586 0a.5.5 0 0 1-.707.707l.707-.707zM7.293 7.5a.5.5 0 1 1 .707.707L7.293 7.5zm-2.172 2.828L7.5 7.95l-.707-.707-2.379 2.379a.5.5 0 0 0 .707.707zm8.586-8.586L11.328 4.12l.707.707 2.379-2.379a.5.5 0 0 0-.707-.707zM4.5 7a2.5 2.5 0 0 1 2.5-2.5v-1A3.5 3.5 0 0 0 3.5 7h1zm2.5 2.5A2.5 2.5 0 0 1 4.5 7h-1a3.5 3.5 0 0 0 3.5 3.5v-1zm2.5-2.5a2.5 2.5 0 0 1-2.5 2.5v1A3.5 3.5 0 0 0 12.5 7h-1zm-2.5-2.5A2.5 2.5 0 0 1 9.5 7h1A3.5 3.5 0 0 0 7 3.5v1z"/>
          </svg>
        </button>
        <el-upload
          :show-file-list="false"
          accept="image/*"
          :auto-upload="false"
          @change="handleImageUpload"
          class="toolbar-upload"
        >
          <button type="button" class="toolbar-btn" title="插入图片">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M6.002 5.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
              <path d="M2.002 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2h-12zm12 1a1 1 0 0 1 1 1v6.5l-3.777-1.947a.5.5 0 0 0-.577.093l-3.71 3.71-2.66-1.772a.5.5 0 0 0-.63.062L1.002 12V3a1 1 0 0 1 1-1h12z"/>
            </svg>
          </button>
        </el-upload>
      </div>

      <!-- 分隔符 -->
      <div class="toolbar-divider"></div>

      <!-- 撤销重做 -->
      <div class="toolbar-group">
        <button
          type="button"
          class="toolbar-btn"
          @click="editor?.chain().focus().undo().run()"
          :disabled="!editor?.can().undo()"
          title="撤销"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M8 3a5 5 0 1 1-4.546 2.914.5.5 0 0 0-.908-.417A6 6 0 1 0 8 2v1z"/>
            <path d="M8 4.466V.534a.25.25 0 0 0-.41-.192L5.23 2.308a.25.25 0 0 0 0 .384l2.36 1.966A.25.25 0 0 0 8 4.466z"/>
          </svg>
        </button>
        <button
          type="button"
          class="toolbar-btn"
          @click="editor?.chain().focus().redo().run()"
          :disabled="!editor?.can().redo()"
          title="重做"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"/>
            <path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966a.25.25 0 0 1 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- 编辑器内容区域 -->
    <div class="tiptap-content" :style="{ height: height }">
      <EditorContent :editor="editor" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import { Editor, EditorContent } from '@tiptap/vue-3'
import { ArrowDown } from '@element-plus/icons-vue'
import type { UploadFile } from 'element-plus'
import StarterKit from '@tiptap/starter-kit'
import Image from '@tiptap/extension-image'
import Link from '@tiptap/extension-link'
import TextAlign from '@tiptap/extension-text-align'
import Underline from '@tiptap/extension-underline'
import {TextStyle} from '@tiptap/extension-text-style'
import Color from '@tiptap/extension-color'
import FontFamily from '@tiptap/extension-font-family'
import Highlight from '@tiptap/extension-highlight'
import FontSize from './extensions/FontSize'

interface Props {
  modelValue?: string
  placeholder?: string
  height?: string
  disabled?: boolean
  hideToolbar?: boolean
  autoFocus?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请输入内容...',
  height: '300px',
  disabled: false,
  hideToolbar: false,
  autoFocus: false
})

const emit = defineEmits<Emits>()

const editor = ref<Editor>()

// 文字颜色选项
const textColors = [
  '#000000', '#333333', '#666666', '#999999', '#cccccc', '#ffffff',
  '#ff0000', '#ff6600', '#ffcc00', '#00ff00', '#0066ff', '#6600ff',
  '#ff3366', '#ff9900', '#ffff00', '#66ff66', '#3399ff', '#9966ff',
  '#cc0000', '#cc6600', '#cccc00', '#00cc00', '#0066cc', '#6600cc'
]

// 背景颜色选项
const highlightColors = [
  '#ffff00', '#00ff00', '#00ffff', '#ff00ff', '#ff0000', '#0000ff',
  '#ffcc00', '#00cc00', '#00cccc', '#cc00cc', '#cc0000', '#0000cc',
  '#fff2cc', '#d5e8d4', '#dae8fc', '#f8cecc', '#e1d5e7', '#ffd966'
]

// 获取当前字体
const getCurrentFontFamily = () => {
  if (!editor.value) return '默认字体'
  const fontFamily = editor.value.getAttributes('textStyle').fontFamily
  return fontFamily || '默认字体'
}

// 获取当前字号
const getCurrentFontSize = () => {
  if (!editor.value) return '14px'
  const fontSize = editor.value.getAttributes('textStyle').fontSize
  return fontSize || '14px'
}

// 获取当前文字颜色
const getCurrentTextColor = () => {
  if (!editor.value) return '#000000'
  const color = editor.value.getAttributes('textStyle').color
  return color || '#000000'
}

// 获取当前背景颜色
const getCurrentHighlightColor = () => {
  if (!editor.value) return 'transparent'
  const highlight = editor.value.getAttributes('highlight').color
  return highlight || 'transparent'
}

// 获取当前标题文本
const getHeadingText = () => {
  if (!editor.value) return '正文'

  for (let level = 1; level <= 6; level++) {
    if (editor.value.isActive('heading', { level })) {
      return `标题 ${level}`
    }
  }
  return '正文'
}

// 设置字体
const setFontFamily = (fontFamily: string) => {
  if (editor.value) {
    if (fontFamily) {
      editor.value.chain().focus().setFontFamily(fontFamily).run()
    } else {
      editor.value.chain().focus().unsetFontFamily().run()
    }
  }
}

// 设置字号
const setFontSize = (fontSize: string) => {
  if (editor.value) {
    editor.value.chain().focus().setFontSize(fontSize).run()
  }
}

// 设置文字颜色
const setTextColor = (color: string) => {
  if (editor.value) {
    editor.value.chain().focus().setColor(color).run()
  }
}

// 清除文字颜色
const clearTextColor = () => {
  if (editor.value) {
    editor.value.chain().focus().unsetColor().run()
  }
}

// 设置背景颜色
const setHighlightColor = (color: string) => {
  if (editor.value) {
    editor.value.chain().focus().setHighlight({ color }).run()
  }
}

// 清除背景颜色
const clearHighlightColor = () => {
  if (editor.value) {
    editor.value.chain().focus().unsetHighlight().run()
  }
}

// 设置标题
const setHeading = (level: number) => {
  if (editor.value) {
    editor.value.chain().focus().toggleHeading({ level: level as 1 | 2 | 3 | 4 | 5 | 6 }).run()
  }
}

// 设置段落
const setParagraph = () => {
  if (editor.value) {
    editor.value.chain().focus().setParagraph().run()
  }
}

// 设置链接
const setLink = () => {
  if (!editor.value) return

  const previousUrl = editor.value.getAttributes('link').href
  const url = window.prompt('请输入链接地址:', previousUrl)

  if (url === null) {
    return
  }

  if (url === '') {
    editor.value.chain().focus().extendMarkRange('link').unsetLink().run()
    return
  }

  editor.value.chain().focus().extendMarkRange('link').setLink({ href: url }).run()
}

// 处理图片上传
const handleImageUpload = (file: UploadFile) => {
  if (!editor.value) return

  const reader = new FileReader()
  reader.onload = (e) => {
    const src = e.target?.result as string
    if (src) {
      editor.value?.chain().focus().setImage({ src }).run()
    }
  }

  if (file.raw) {
    reader.readAsDataURL(file.raw)
  }
}

onMounted(() => {
  editor.value = new Editor({
    extensions: [
      StarterKit.configure({
        bulletList: {
          HTMLAttributes: {
            class: 'my-custom-bullet-list',
          },
        },
        orderedList: {
          HTMLAttributes: {
            class: 'my-custom-ordered-list',
          },
        },
      }),
      Image,
      Link.configure({
        openOnClick: false,
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Underline,
      TextStyle,
      Color.configure({
        types: [TextStyle.name]
      }),
      FontFamily.configure({
        types: [TextStyle.name],
      }),
      FontSize.configure({
        types: [TextStyle.name],
      }),
      Highlight.configure({
        multicolor: true
      }),
    ],
    content: props.modelValue,
    autofocus: props.autoFocus,
    editable: !props.disabled,
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
        'data-placeholder': props.placeholder
      }
    },
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()
      emit('update:modelValue', html)
      emit('change', html)
    }
  })
})

onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
  }
})

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (editor.value && editor.value.getHTML() !== newValue) {
    editor.value.commands.setContent(newValue, { emitUpdate: false })
  }
})

// 监听禁用状态
watch(() => props.disabled, (disabled) => {
  if (editor.value) {
    editor.value.setEditable(!disabled)
  }
})
</script>

<style scoped lang="scss">
.tiptap-editor {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  background: #ffffff;

  &:focus-within {
    border-color: #004C66;
    box-shadow: 0 0 0 2px rgba(0, 76, 102, 0.1);
  }

  &--disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}

.tiptap-toolbar {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  flex-wrap: wrap;

  .toolbar-group {
    display: flex;
    align-items: center;
    gap: 2px;
  }

  .toolbar-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    color: #666666;
    transition: all 0.2s ease;
    position: relative;

    &:hover:not(:disabled) {
      background: #e6f7ff;
      color: #004C66;
    }

    &:disabled {
      opacity: 0.4;
      cursor: not-allowed;
    }

    &.is-active {
      background: #004C66;
      color: #ffffff;
    }

    &--dropdown {
      width: auto;
      padding: 0 8px;
      gap: 4px;

      span {
        font-size: 14px;
      }

      .el-icon {
        font-size: 12px;
      }
    }
  }

  .toolbar-divider {
    width: 1px;
    height: 20px;
    background: #d9d9d9;
    margin: 0 8px;
  }

  .toolbar-upload {
    display: inline-block;
  }

  .color-indicator {
    position: absolute;
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    width: 16px;
    height: 3px;
    border-radius: 1px;
  }
}

.color-picker {
  padding: 12px;
  width: 240px;

  .color-row {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .color-item {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    border: 1px solid #e5e5e5;
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.1);
      border-color: #004C66;
    }
  }

  .color-clear-btn {
    padding: 4px 8px;
    border: none;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    color: #666666;
    transition: all 0.2s ease;

    &:hover {
      background: #f0f0f0;
      color: #004C66;
    }
  }
}

.tiptap-content {
  overflow-y: auto;

  :deep(.ProseMirror) {
    padding: 16px;
    outline: none;
    font-size: 14px;
    line-height: 1.6;
    color: #333333;

    &[data-placeholder]:empty::before {
      content: attr(data-placeholder);
      color: #999999;
      pointer-events: none;
      position: absolute;
    }

    p {
      margin: 0 0 12px 0;

      &:last-child {
        margin-bottom: 0;
      }
    }

    h1, h2, h3, h4, h5, h6 {
      margin: 16px 0 12px 0;
      font-weight: 600;

      &:first-child {
        margin-top: 0;
      }
    }

    h1 { font-size: 2em; }
    h2 { font-size: 1.5em; }
    h3 { font-size: 1.25em; }
    h4 { font-size: 1.1em; }
    h5 { font-size: 1em; }
    h6 { font-size: 0.9em; }

    ul, ol {
      margin: 12px 0;
      padding-left: 24px;

      li {
        margin: 6px 0;
        line-height: 1.6;
      }
    }

    strong {
      font-weight: 600;
    }

    em {
      font-style: italic;
    }

    u {
      text-decoration: underline;
    }

    s {
      text-decoration: line-through;
    }

    a {
      color: #004C66;
      text-decoration: underline;
      cursor: pointer;

      &:hover {
        color: #003d52;
      }
    }

    img {
      max-width: 100%;
      height: auto;
      border-radius: 4px;
      margin: 8px 0;
    }

    mark {
      padding: 2px 4px;
      border-radius: 2px;
    }

    // 文本对齐样式
    &[style*="text-align: left"] {
      text-align: left;
    }

    &[style*="text-align: center"] {
      text-align: center;
    }

    &[style*="text-align: right"] {
      text-align: right;
    }

    &[style*="text-align: justify"] {
      text-align: justify;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .tiptap-toolbar {
    padding: 8px 12px;
    gap: 6px;

    .toolbar-btn {
      padding: 4px 6px;
      min-width: 28px;
      height: 28px;
      font-size: 12px;
    }
  }

  .color-picker {
    width: 200px;
    padding: 8px;

    .color-item {
      width: 20px;
      height: 20px;
    }
  }

  .tiptap-content {
    :deep(.ProseMirror) {
      padding: 12px;
      font-size: 13px;
    }
  }
}
</style>
