/**
 * 阿里云OSS图片优化工具
 * 提供图片格式转换、尺寸调整、质量压缩等功能
 */

// OSS图片处理参数
interface OSSImageParams {
  // 图片格式
  format?: 'jpg' | 'png' | 'webp' | 'avif'
  // 图片质量 (1-100)
  quality?: number
  // 宽度
  width?: number
  // 高度  
  height?: number
  // 缩放模式
  mode?: 'lfit' | 'mfit' | 'fill' | 'pad' | 'fixed'
  // 是否渐进式JPEG
  progressive?: boolean
  // 是否自动旋转
  autoOrient?: boolean
}

/**
 * 生成OSS图片处理URL
 * @param baseUrl 原始图片URL
 * @param params 处理参数
 * @returns 处理后的图片URL
 */
export function generateOSSImageUrl(baseUrl: string, params: OSSImageParams = {}): string {
  if (!baseUrl.includes('aliyuncs.com')) {
    // 如果不是阿里云OSS链接，直接返回原URL
    return baseUrl
  }

  const {
    format,
    quality = 80,
    width,
    height,
    mode = 'lfit',
    progressive = true,
    autoOrient = true
  } = params

  const processParams: string[] = []

  // 自动旋转
  if (autoOrient) {
    processParams.push('auto-orient,1')
  }

  // 尺寸调整
  if (width || height) {
    let resizeParam = `resize,m_${mode}`
    if (width) resizeParam += `,w_${width}`
    if (height) resizeParam += `,h_${height}`
    processParams.push(resizeParam)
  }

  // 格式转换
  if (format) {
    processParams.push(`format,${format}`)
  }

  // 质量压缩
  if (quality < 100) {
    processParams.push(`quality,q_${quality}`)
  }

  // 渐进式JPEG
  if (progressive && (format === 'jpg' || !format)) {
    processParams.push('interlace,1')
  }

  if (processParams.length === 0) {
    return baseUrl
  }

  const processString = processParams.join('/')
  const separator = baseUrl.includes('?') ? '&' : '?'
  
  return `${baseUrl}${separator}x-oss-process=image/${processString}`
}

/**
 * 根据设备像素比获取合适的图片
 * @param baseUrl 原始图片URL
 * @param options 配置选项
 * @returns 优化后的图片URL
 */
export function getResponsiveImageUrl(
  baseUrl: string, 
  options: {
    baseWidth: number
    baseHeight?: number
    format?: 'jpg' | 'png' | 'webp' | 'avif'
    quality?: number
  }
): string {
  const dpr = window.devicePixelRatio || 1
  const { baseWidth, baseHeight, format, quality } = options
  
  // 根据设备像素比调整尺寸
  const width = Math.round(baseWidth * Math.min(dpr, 2)) // 最大2倍图
  const height = baseHeight ? Math.round(baseHeight * Math.min(dpr, 2)) : undefined

  return generateOSSImageUrl(baseUrl, {
    width,
    height,
    format: format || (supportsWebP() ? 'webp' : 'jpg'),
    quality: quality || 85,
    mode: 'lfit'
  })
}

/**
 * 检测浏览器是否支持WebP格式
 * @returns 是否支持WebP
 */
export function supportsWebP(): boolean {
  if (typeof window === 'undefined') return false
  
  // 检查是否已经缓存了结果
  if ('webpSupport' in window) {
    return (window as any).webpSupport
  }

  const canvas = document.createElement('canvas')
  canvas.width = 1
  canvas.height = 1
  
  const support = canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0
  ;(window as any).webpSupport = support
  
  return support
}

/**
 * 检测浏览器是否支持AVIF格式
 * @returns 是否支持AVIF
 */
export function supportsAVIF(): boolean {
  if (typeof window === 'undefined') return false
  
  // 检查是否已经缓存了结果
  if ('avifSupport' in window) {
    return (window as any).avifSupport
  }

  const canvas = document.createElement('canvas')
  canvas.width = 1
  canvas.height = 1
  
  try {
    const support = canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0
    ;(window as any).avifSupport = support
    return support
  } catch {
    ;(window as any).avifSupport = false
    return false
  }
}

/**
 * 获取最佳图片格式
 * @returns 最佳支持的图片格式
 */
export function getBestImageFormat(): 'avif' | 'webp' | 'jpg' {
  if (supportsAVIF()) return 'avif'
  if (supportsWebP()) return 'webp'
  return 'jpg'
}

/**
 * 预设的图片尺寸配置
 */
export const IMAGE_PRESETS = {
  // 缩略图
  thumbnail: { width: 150, height: 150, quality: 75 },
  // 小图
  small: { width: 300, height: 200, quality: 80 },
  // 中图
  medium: { width: 600, height: 400, quality: 85 },
  // 大图
  large: { width: 1200, height: 800, quality: 90 },
  // 超大图
  xlarge: { width: 1920, height: 1080, quality: 95 }
} as const

/**
 * 根据预设获取图片URL
 * @param baseUrl 原始图片URL
 * @param preset 预设名称
 * @param format 图片格式
 * @returns 处理后的图片URL
 */
export function getPresetImageUrl(
  baseUrl: string, 
  preset: keyof typeof IMAGE_PRESETS,
  format?: 'jpg' | 'png' | 'webp' | 'avif'
): string {
  const config = IMAGE_PRESETS[preset]
  return generateOSSImageUrl(baseUrl, {
    ...config,
    format: format || getBestImageFormat()
  })
}

/**
 * 生成响应式图片的srcset
 * @param baseUrl 原始图片URL
 * @param sizes 尺寸配置数组
 * @returns srcset字符串
 */
export function generateSrcSet(
  baseUrl: string,
  sizes: Array<{ width: number; descriptor?: string }>
): string {
  return sizes
    .map(({ width, descriptor }) => {
      const url = generateOSSImageUrl(baseUrl, {
        width,
        format: getBestImageFormat(),
        quality: 85
      })
      return `${url} ${descriptor || `${width}w`}`
    })
    .join(', ')
}

// 导出常用的图片处理函数
export default {
  generateOSSImageUrl,
  getResponsiveImageUrl,
  getPresetImageUrl,
  generateSrcSet,
  getBestImageFormat,
  supportsWebP,
  supportsAVIF,
  IMAGE_PRESETS
}
