<template>
  <div class="bidding">
    <!-- <header class="header">
      <FreedomHeader />
    </header> -->
    <main class="bidding-main">
      <router-view />
    </main>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive } from "vue";
import FreedomHeader from "./components/FreedomHeader.vue";
</script>
<style lang="scss" scoped>
/* .header {
  margin-bottom: 20px;
} */
.bidding-main {
  width: 100%;
  height: 100%;
}
</style>
