<template>
  <div class="detail-view-test-page">
    <div class="test-header">
      <h2>详情查看功能测试</h2>
      <p>测试供应和求购信息的详情查看弹窗</p>
    </div>
    
    <div class="test-controls">
      <el-button @click="testSupplyDetail">测试供应详情</el-button>
      <el-button @click="testDemandDetail">测试求购详情</el-button>
      <el-input 
        v-model="testRecordId" 
        placeholder="请输入记录ID" 
        style="width: 200px; margin-left: 12px;"
      />
    </div>
    
    <div class="feature-info">
      <h3>功能特点</h3>
      <ul>
        <li>✅ <strong>弹窗展示</strong>：基于自定义Modal组件</li>
        <li>✅ <strong>详细信息</strong>：显示完整的供应/求购信息</li>
        <li>✅ <strong>基本信息</strong>：ID、标题、物资类型、联系人等</li>
        <li>✅ <strong>详细描述</strong>：品牌、型号、数量、价格、地址等</li>
        <li>✅ <strong>状态显示</strong>：当前审核状态和审核意见</li>
        <li>✅ <strong>附件支持</strong>：显示附件列表，支持预览和下载</li>
        <li>✅ <strong>响应式设计</strong>：适配不同屏幕尺寸</li>
        <li>✅ <strong>加载状态</strong>：数据加载时显示loading</li>
      </ul>
    </div>
    
    <div class="api-info">
      <h3>API接口</h3>
      <ul>
        <li><strong>获取详情</strong>：supplyDemandApi.querySupplyDemandById</li>
        <li><strong>请求参数</strong>：{ id: string }</li>
        <li><strong>返回数据</strong>：包含完整的供应/求购信息</li>
        <li><strong>服务类型</strong>：4-供应，5-求购</li>
      </ul>
    </div>

    <!-- 详情查看弹窗 -->
    <SupplyDemandDetailModal
      v-model="detailModalVisible"
      :record-id="currentRecordId"
      :service-type="currentServiceType"
      @close="handleDetailClose"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SupplyDemandDetailModal from '@/components/SupplyDemandDetailModal/index.vue'

// 测试相关
const testRecordId = ref('1')
const detailModalVisible = ref(false)
const currentRecordId = ref<string | null>(null)
const currentServiceType = ref(4) // 4-供应，5-求购

// 测试供应详情
const testSupplyDetail = () => {
  if (!testRecordId.value) {
    return
  }
  currentRecordId.value = testRecordId.value
  currentServiceType.value = 4
  detailModalVisible.value = true
}

// 测试求购详情
const testDemandDetail = () => {
  if (!testRecordId.value) {
    return
  }
  currentRecordId.value = testRecordId.value
  currentServiceType.value = 5
  detailModalVisible.value = true
}

// 关闭详情弹窗
const handleDetailClose = () => {
  detailModalVisible.value = false
  currentRecordId.value = null
}
</script>

<style scoped lang="scss">
.detail-view-test-page {
  padding: 20px;
  
  .test-header {
    margin-bottom: 20px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    border-bottom: 1px solid #e5e5e5;
    
    h2 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 24px;
      font-weight: 600;
    }
    
    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }
  
  .test-controls {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    display: flex;
    align-items: center;
    
    .el-button {
      margin-right: 12px;
    }
  }
  
  .feature-info,
  .api-info {
    margin-bottom: 20px;
    padding: 20px;
    background: #f0f8ff;
    border-radius: 8px;
    border-left: 4px solid #004C66;
    
    h3 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 18px;
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        color: #555;
        font-size: 14px;
        
        strong {
          color: #004C66;
        }
      }
    }
  }
}
</style>
