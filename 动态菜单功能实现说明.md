# 动态菜单功能实现说明

## 功能概述

在 `src/views/RecyclerManage/index.vue` 中实现了基于 Element Plus `el-menu` 组件的动态导航菜单功能，通过调用 `/sys/permission/getUserPermissionByToken` 接口获取用户权限菜单数据，并采用现代化的UI设计风格。

## 实现内容

### 1. API接口封装

在 `src/utils/api-new.ts` 中新增了 `systemApi`：

```typescript
// 系统相关API
export const systemApi = {
  /**
   * 根据Token获取用户权限菜单
   * @returns {Promise<ApiResponse<UserPermissionResponse>>} 用户权限数据
   */
  getUserPermissionByToken(): Promise<ApiResponse<UserPermissionResponse>> {
    return httpNew.get('/sys/permission/getUserPermissionByToken').then((res) => res.data)
  }
};
```

### 2. 类型定义

定义了完整的菜单数据类型：

```typescript
// 定义菜单项的类型
interface MenuMeta {
  hideMenu?: boolean;
  keepAlive?: boolean;
  internalOrExternal?: boolean;
  hideTab?: boolean;
  icon?: string;
  componentName?: string;
  title: string;
}

interface MenuItem {
  redirect?: string | null;
  path: string;
  component: string;
  route: string;
  children?: MenuItem[];
  hidden?: boolean;
  meta: MenuMeta;
  name: string;
  id: string;
  orderedSupplier?: any;
  defaultSupplier?: any;
  arraySupplier?: any;
}

// 定义用户权限响应类型
interface UserPermissionResponse {
  menu: MenuItem[];
  auth: string[];
  allAuth: string[];
}
```

### 3. 组件功能

#### 响应式数据
- `menuItems`: 存储动态获取的菜单数据
- `loading`: 加载状态
- `activeNav`: 当前激活的菜单项

#### 核心功能
- `getUserPermissionMenu()`: 获取用户权限菜单数据
- `handleMenuSelect()`: 处理el-menu的选择事件
- `getIconComponent()`: 根据图标名称获取对应的Element Plus图标组件
- `getCurrentMenuTitle()`: 获取当前选中菜单的标题

### 4. 菜单交互逻辑

- **有children的菜单**: 使用el-sub-menu组件，自动支持展开/收起，显示箭头图标
- **无children的菜单**: 点击时触发`handleMenuSelect`事件，设置activeNav为菜单的path
- **子菜单**: 点击时触发`handleMenuSelect`事件，设置activeNav为菜单的path

### 5. 样式特性

- **基于Element Plus**: 使用el-menu组件，保证UI一致性和可访问性
- **自定义主题**: 采用项目主色调#004C66作为激活色
- **现代化设计**: 圆角、阴影、过渡动画等现代UI元素
- **图标支持**: 集成Element Plus图标库，支持多种图标映射
- **响应式布局**: 适配不同屏幕尺寸
- **加载状态**: 使用el-skeleton组件显示加载动画

### 6. 组件模式

采用组件模式而非路由模式：
- 使用el-menu的`@select`事件处理菜单选择
- 点击菜单项时设置`activeNav`为菜单的`path`值
- 根据`activeNav`的值显示对应的组件
- 提供`getCurrentMenuTitle()`函数获取当前菜单标题
- 支持动态内容区域显示

### 7. 图标系统

支持多种图标映射：
- Element Plus内置图标
- Ant Design图标名称映射
- 自定义图标映射
- 默认图标回退机制

## 使用方法

### 1. 在其他组件中使用systemApi

```typescript
import { systemApi } from '@/utils/api-new'

// 获取用户权限菜单
const getMenu = async () => {
  try {
    const response = await systemApi.getUserPermissionByToken()
    if (response.success !== false && response.result?.menu) {
      // 处理菜单数据
      console.log('菜单数据:', response.result.menu)
    }
  } catch (error) {
    console.error('获取菜单失败:', error)
  }
}
```

### 2. 组件切换示例

在模板中根据activeNav显示不同组件：

```vue
<template>
  <div class="content-area">
    <!-- 根据activeNav显示对应组件 -->
    <ComponentA v-if="activeNav === '/path/to/componentA'" />
    <ComponentB v-if="activeNav === '/path/to/componentB'" />
    <ComponentC v-if="activeNav === '/path/to/componentC'" />

    <!-- 或者使用动态组件 -->
    <component :is="getCurrentComponent()" />
  </div>
</template>

<script setup>
// 根据activeNav获取对应组件
const getCurrentComponent = () => {
  const componentMap = {
    '/path/to/componentA': ComponentA,
    '/path/to/componentB': ComponentB,
    '/path/to/componentC': ComponentC,
  }
  return componentMap[activeNav.value] || DefaultComponent
}
</script>
```

### 3. 菜单样式自定义

可以通过修改CSS变量来自定义菜单样式：

```scss
.sidebar-nav {
  // 自定义一级菜单缩进
  .nav-item {
    padding: 12px 8px; // 调整内边距
  }

  // 自定义子菜单缩进
  .submenu-container {
    margin-left: 8px; // 调整子菜单容器缩进

    .submenu-item {
      padding: 8px 12px 8px 32px; // 调整子菜单项缩进
    }
  }

  // 自定义箭头样式
  .expand-arrow {
    font-size: 12px; // 调整箭头大小
    transition: all 0.3s ease; // 调整动画时间
  }
}
```

## 接口数据格式

接口返回的菜单数据结构示例：

```json
{
  "success": true,
  "result": {
    "menu": [
      {
        "redirect": null,
        "path": "/hgy",
        "component": "layouts/default/index",
        "route": "1",
        "children": [
          {
            "path": "/hgy/entrustService/hgyEntrustOrderList",
            "component": "hgy/entrustService/HgyEntrustOrderList",
            "route": "1",
            "hidden": true,
            "meta": {
              "hideMenu": true,
              "keepAlive": false,
              "internalOrExternal": false,
              "hideTab": true,
              "icon": "ant-design:fullscreen-exit-outlined",
              "componentName": "HgyEntrustOrderList",
              "title": "委托单"
            },
            "name": "hgy-entrustService-hgyEntrustOrderList",
            "id": "2025063003262400040"
          }
        ],
        "meta": {
          "keepAlive": false,
          "internalOrExternal": false,
          "icon": "ant-design:plus-circle-twotone",
          "componentName": "index",
          "title": "委托服务"
        },
        "name": "hgy",
        "id": "1938529564217679874"
      }
    ]
  }
}
```

## 注意事项

1. 接口需要在请求头中携带有效的token
2. 菜单数据可能在 `response.result.menu` 或 `response.data.menu` 中
3. 组件会在挂载时自动获取菜单数据
4. 如果获取失败，会显示错误信息并使用默认菜单
5. 菜单的图标名称需要在项目的图标库中存在
