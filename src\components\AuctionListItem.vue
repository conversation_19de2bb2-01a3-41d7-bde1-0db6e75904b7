<template>
  <div class="auction-list-item" @click="handleClick">
    <!-- 左侧：图片展示区域 -->
    <div class="item-image">
      <img :src="productImage" :alt="productName" />
    </div>

    <!-- 右侧：标的说明区域 -->
    <div class="item-info">
      <!-- 上部分：标题和时间 -->
      <div class="info-header">
        <span class="item-title">{{ productName }}</span>
        <div class="item-time">
          <span>{{ timeValue }}</span>
        </div>
      </div>

      <!-- 中间部分：标的说明 -->
      <div class="item-description">
        <span>
          {{ description }}
        </span>
      </div>

      <!-- 下部分：企业名称和点赞评论查看 -->
      <div class="info-footer">
        <div class="company-info">
          <SvgIcon iconName="company" class="company-icon"></SvgIcon>
          <span class="company-name">{{ companyName }}</span>
        </div>
        <div class="social-actions">
          <div class="action-item">
            <SvgIcon iconName="auction-like" class="action-icon"></SvgIcon>
            <span>{{ likeCount }}</span>
          </div>
          <div class="action-item">
            <SvgIcon iconName="auction-comment" class="action-icon"></SvgIcon>
            <span>{{ commentCount }}</span>
          </div>
          <div class="action-item">
            <SvgIcon iconName="auction-view" class="action-icon"></SvgIcon>
            <span>{{ viewCount }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

import type { ListItem } from "@/types/auction";

interface Props extends ListItem {}

const props = withDefaults(defineProps<Props>(), {
  likeCount: 0,
  commentCount: 0,
  viewCount: 0,
});

const emit = defineEmits<{
  (e: "click", productId: string): void;
}>();

// 方法
const handleClick = () => {
  emit("click", props.productId);
};
</script>

<style lang="scss" scoped>
.auction-list-item {
  display: flex;
  width: 100%;
  // border-bottom: 1px solid #EEEEEE;
  background-color: #ffffff;
  cursor: pointer;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  // transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    /* transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); */
    // 移除整个列表项的放大效果
    // transform: scale(1.2);
  }

  // 左侧图片区域
  .item-image {
    flex-shrink: 0;
    width: 213px;
    height: 143px;
    margin-right: 16px;
    overflow: hidden;
    border-radius: 10px;
    position: relative; // 添加相对定位

    // 添加光亮扫过效果的伪元素
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -150%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.3) 50%,
        rgba(255, 255, 255, 0) 100%
      );
      transform: skewX(-25deg);
      z-index: 1;
      transition: none;
      pointer-events: none; // 确保不影响鼠标事件
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }
  }

  // 右侧信息区域
  .item-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    // 上部分：标题和时间
    .info-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-top: 12px;
      margin-bottom: 10px;
      color: #333333;

      .item-title {
        flex: 1;
        font-size: 18px;
        font-family: "PingFang Bold";
        margin: 0;
        line-height: 1.3;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }

      .item-time {
        font-family: "PingFang Regular";
        font-size: 12px;
        color: #999999;
        white-space: nowrap;
        margin-left: 12px;
        margin-top: 2px;
      }
    }

    // 中间部分：标的说明
    .item-description {
      flex: 1;
      font-size: 16px;
      color: #666666;
      font-family: "PingFang Regular";
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      position: relative; // 添加相对定位以支持伪元素

      // 添加底部渐变虚线分割线
      &::after {
        content: "";
        position: absolute;
        bottom: 5px; // 距离文本底部5px
        left: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(
          to right,
          rgba(238, 238, 238, 1) 0%,
          rgba(221, 221, 221, 0) 100%
        );
        // 创建虚线效果
        background-image: repeating-linear-gradient(
          to right,
          transparent 0px,
          transparent 3px,
          rgba(238, 238, 238, 1) 3px,
          rgba(238, 238, 238, 1) 6px
        );

        // 应用渐变遮罩来实现从实色到透明的效果
        mask: linear-gradient(
          to right,
          rgba(0, 0, 0, 1) 0%,
          rgba(0, 0, 0, 0) 100%
        );
        -webkit-mask: linear-gradient(
          to right,
          rgba(0, 0, 0, 1) 0%,
          rgba(0, 0, 0, 0) 100%
        );
      }
    }

    // 下部分：企业名称和点赞评论查看
    .info-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 38px;

      .company-info {
        display: flex;
        align-items: center;

        .company-icon {
          width: 16px;
          height: 16px;
          margin-right: 6px;
          color: #999999;
        }

        .company-name {
          font-size: 14px;
          color: #999999;
          max-width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .social-actions {
        display: flex;
        align-items: center;

        .action-item {
          display: flex;
          align-items: center;
          margin-left: 16px;
          font-size: 14px;
          color: #999999;

          .action-icon {
            width: 16px;
            height: 16px;
            margin-right: 4px;
          }
        }
      }
    }
  }

  // 当鼠标悬停在列表项上时，图片放大并触发光亮扫过动画
  &:hover {
    .item-image img {
      transform: scale(1.06);
    }

    .item-image::before {
      // animation: shine 1s ease-in-out;
    }

    .item-title {
      color: #004c66;
    }
  }

  // 定义光亮扫过动画
  @keyframes shine {
    0% {
      left: -150%;
    }
    100% {
      left: 150%;
    }
  }
}
</style>
