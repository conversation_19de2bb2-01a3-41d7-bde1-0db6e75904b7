<script setup lang="ts">
import { RouterLink, RouterView } from 'vue-router'
import {reactive, computed} from 'vue'
import {useHead} from '@vueuse/head'

const siteData = reactive({
      title: '灰谷网',
      description: '灰谷网应用描述',
      image: '/favicon.ico'
    })

    useHead({
      title: computed(() => siteData.title),
      meta: [
        { name: 'description', content: computed(() => siteData.description) },
        // Open Graph
        { property: 'og:title', content: computed(() => siteData.title) },
        { property: 'og:description', content: computed(() => siteData.description) },
        { property: 'og:image', content: computed(() => siteData.image) },
        { property: 'og:type', content: 'website' },
        // Twitter Card
        { name: 'twitter:card', content: 'summary_large_image' },
        { name: 'twitter:title', content: computed(() => siteData.title) },
        { name: 'twitter:description', content: computed(() => siteData.description) },
        { name: 'twitter:image', content: computed(() => siteData.image) },
      ],
      link: [
        { rel: 'canonical', href: computed(() => window.location.href) }
      ]
    })
</script>

<template>
  <div class="app">
    <RouterView />
  </div>
</template>

<style scoped>
.app {
  width: 100%;
  height: 100%;
}
</style>

<style>
.el-select-dropdown__list{
  padding: 0;
}
</style>
