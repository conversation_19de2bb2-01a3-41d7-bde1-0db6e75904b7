你是一名精通vue的前端开发，你现在需要根据项目的需求和规范，来编写项目的代码。

## 核心规则 你需要编写清晰、简洁且技术性强的回答
- 不要有冗余无用的代码
- 保持页面的响应式
- 代码可复用性强
- 代码结构清晰
- 代码注释详细
- 代码错误处理完善
- 代码性能优化
- 代码的可维护性高
- 代码的可读性高
- 代码的兼容性好
- 注释要详细，每个函数、每个变量、每个组件都要注释
- 代码要符合vue的规范

## 项目规范
- 当前项目使用的是vue3.5.13版本 + ts
- 项目中用到的组件库为ElementPlus，一些组件库中有的组件不需要自己再单独创建，根据组件库的组件进行二次封装即可，如果组件库中没有的组件，需要根据组件的功能进行封装
- 项目中用到的路由框架为vue-router
- 项目中用到的状态管理为pinia
- 项目中用到的css预处理器为scss

## 设计规范
- 设计稿为1920px，项目中的所有元素都要根据设计稿进行布局，不能有偏差
- 项目是拍卖网站，要考虑各种屏幕的响应式设计，但是移动端暂时不需要单独考虑，主要考虑大屏和电脑显示器的响应式设计
- 项目的设计稿为Figma设计稿，需要完全还原我给你提供的设计截图或者设计链接，不要有自己的想法

## 图标规则
- 项目中的图标都放在assets/icons文件下，图标展示需要使用已经封装好的SvgIcon组件，在components文件下
- 图标使用规则为图标所在文件名-图标名，例：aboutUs-banner，icons目录下的图标直接使用图标名
- SvgIcon组件调用时的两个属性名分别为iconName和className
- 图标文件格式为svg