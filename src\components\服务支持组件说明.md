# 服务支持组件 (ServiceSupport)

这是一个可复用的服务支持页面组件，适用于竞价交易、自由交易、企业专区等不同页面的服务支持功能。

## 功能特性

- 🎨 **美观的UI设计** - 包含背景图片的搜索区域和清晰的问题列表布局
- 🔍 **搜索功能** - 支持关键词搜索，可自定义搜索接口
- 📱 **响应式设计** - 适配不同屏幕尺寸，提供良好的用户体验
- 🗂️ **分类导航** - 左侧导航栏支持多个分类，图标可自定义
- ❓ **折叠问答** - 问题列表默认折叠，点击展开查看答案
- 👍 **反馈功能** - 每个答案下方提供"是否有帮助"的反馈按钮
- 📜 **滚动优化** - 问题列表区域可滚动，避免页面过长

## 组件结构

### 上半部分 - 搜索区域
- 宽度：100%屏幕宽度
- 高度：340px
- 背景图：`assets/icons/support/bg.svg`
- 包含标题和搜索框
- 搜索按钮位于搜索框内部右侧

### 下半部分 - 内容区域
- 最大宽度：1280px，居中显示
- 左右布局：导航栏 + 问题列表
- 背景色：导航栏和问题列表均为白色 (#FFF)
- 答案区域背景色：#F8F8F8
- 圆角：所有区域均为10px

## 使用方法

### 1. 基本用法

```vue
<template>
  <ServiceSupport
    :title="'嗨！有什么需要帮忙的吗？'"
    :placeholder="'请输入您要搜索的问题关键词'"
    :navigation-items="navigationItems"
    :on-search="handleSearch"
    :on-get-questions="handleGetQuestions"
    :on-helpful-feedback="handleHelpfulFeedback"
  />
</template>

<script setup>
import ServiceSupport from '@/views/components/ServiceSupport.vue'

// 配置导航项目
const navigationItems = [
  { icon: 'common-problem', text: '常见问题' },
  { icon: 'account-help', text: '账户帮助' },
  { icon: 'payment-help', text: '支付帮助' }
]

// 处理搜索
const handleSearch = (keyword) => {
  // 调用搜索API
  console.log('搜索:', keyword)
}

// 获取问题列表
const handleGetQuestions = async (navIndex) => {
  // 调用获取问题列表的API
  const response = await getQuestionsAPI(navIndex)
  return response.data
}

// 处理反馈
const handleHelpfulFeedback = (questionIndex, isHelpful) => {
  // 提交反馈到后端
  submitFeedbackAPI(questionIndex, isHelpful)
}
</script>
```

### 2. 属性说明

| 属性名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| title | string | 否 | '嗨！有什么需要帮忙的吗？' | 搜索区域的标题 |
| placeholder | string | 否 | '搜索关键词' | 搜索框的占位符文本 |
| navigationItems | NavigationItem[] | 是 | - | 导航栏项目配置 |
| onSearch | function | 否 | - | 搜索回调函数 |
| onGetQuestions | function | 否 | - | 获取问题列表的回调函数 |
| onHelpfulFeedback | function | 否 | - | 帮助反馈回调函数 |

### 3. 接口定义

```typescript
// 导航项目接口
interface NavigationItem {
  icon: string    // 图标名称（对应 assets/icons/support/ 目录下的svg文件）
  text: string    // 显示文本
}

// 问题接口
interface Question {
  title: string   // 问题标题
  answer: string  // 问题答案
}

// 搜索回调函数
type OnSearch = (keyword: string) => void

// 获取问题列表回调函数
type OnGetQuestions = (navIndex: number) => Promise<Question[]>

// 帮助反馈回调函数
type OnHelpfulFeedback = (questionIndex: number, isHelpful: boolean) => void
```

## 图标要求

所有图标文件需要放置在 `assets/icons/support/` 目录下，格式为SVG。

### 必需图标
- `bg.svg` - 搜索区域背景图
- `arrow-up.svg` - 问题展开时的向上箭头
- `arrow-down.svg` - 问题折叠时的向下箭头

### 导航图标
根据不同页面的需求，在 `navigationItems` 中指定对应的图标名称。例如：
- `common-problem.svg` - 常见问题图标
- `account-help.svg` - 账户帮助图标
- `payment-help.svg` - 支付帮助图标
- `auction-help.svg` - 竞拍帮助图标
- `contact-us.svg` - 联系我们图标

## 样式定制

组件使用了CSS变量，可以通过修改CSS变量来定制样式：

```css
:root {
  --el-color-primary: #004c66;  /* 主色调 */
  --el-color-primary-light-9: #ecf5f9;  /* 浅色背景 */
}
```

## 响应式设计

- **桌面端 (>1024px)**: 左右布局，导航栏固定宽度280px
- **平板端 (≤1024px)**: 上下布局，导航栏变为水平滚动
- **移动端 (≤768px)**: 优化间距和字体大小，搜索区域高度调整

## 注意事项

1. **图标文件**: 确保所有引用的图标文件都存在于指定目录
2. **API接口**: `onGetQuestions` 函数需要返回Promise，确保异步数据加载正常
3. **错误处理**: 建议在回调函数中添加适当的错误处理逻辑
4. **性能优化**: 问题列表支持滚动，避免一次性加载过多数据
5. **用户体验**: 搜索和数据加载时可以添加loading状态提示

## 示例文件

参考 `ServiceSupportExample.vue` 文件查看完整的使用示例，包含模拟数据和API调用的实现方式。