<template>
  <div class="about-us">
    <!-- 顶部Banner图片区域 -->
    <div class="banner-section">
      <!-- <SvgIcon iconName="aboutUs-banner" className="banner-image" /> -->
    </div>

    <!-- 公司简介区域 -->
    <div class="company-intro-section">
      <div class="container">
        <!-- 公司简介标题和内容 - 只有这部分参与动画 -->
        <div class="intro-text-content">
          <!-- 公司简介标题 - 先出现 -->
          <div
            class="section-title"
            :class="{ 'animate-in': animationStates.companyIntroTitle }"
          >
            <span>公司简介</span>
          </div>

          <!-- 公司简介内容 - 后出现 -->
          <div
            class="intro-content"
            :class="{ 'animate-in': animationStates.companyIntroContent }"
          >
            <p>
              河南灰谷科技有限公司成立于2024年8月，公司坐落在全国交通枢纽、经济发展成熟、产业集群完善的郑州市。公司注册资本1000万元，是一家专业为企业处置废旧资产的拍卖服务机构，主要从事闲置资产、二手设备、废旧物资拍卖、整厂拆除等，专注于服务基建工程、钢铁、电力、化工、通信、汽车制造、机械制造行業。
            </p>
            <p>
              “灰谷网”系河南灰谷科技有限公司旗下自主研发平台，由专业技术团队历时一年的研发、内测及不断优化于2024年8月网站正式投入使用并得到客户的一致好评，独特的代码方便国企单位的端口对接、数据优化。“灰谷网”全网全入口，五位一体：PC、H5、微信公众号、微信小程序、安卓APP、苹果APP,
              满足客户不同需求，方便客户任何环境的使用，保证物资处置及时、客户操作的便捷。
            </p>
            <p>
              “灰谷网”打破线下传统拍卖模式，秉承“公开、公平、公正、诚实信用”原则，利用新型互联网思维，实现线下预展与线上拍卖相结合的在线交易方式，让拍卖物权委托方安全、快速、高效实现资产价值最大化。
              其“公平、公开、公正”的线上拍卖流程、规范的操作模式，使企业在资产处置上在合规合法的同时达到价值最大化，为广大企业提供一站式资产处置服务。
            </p>
            <p>
              公司成立以来，充分发挥管理优势、资金优势、人才优势和技术优势，带动地方经济发展，增加就业岗位，增加财政收入，为地方经济的发展做出了较大的贡献。公司将“绿色、循环、低碳、环保”等理念融入废旧产业，以专业化的运营及负责任的态度赢得了客户的广泛好评。公司拥有专业的招商团队，庞大的下游客户群体，打破地域优势，目前拥有40000多家专业从事各行业的优质回收商资源。
            </p>
          </div>
        </div>
      </div>

      <!-- 数据展示区域 -->
      <div
        class="stats-section"
        :class="{ 'animate-in': animationStates.statsSection }"
      >
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">
              {{ animatedStats.stat1 }}<span class="unit">亿+</span>
            </div>
            <div class="stat-label">处置金额</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">
              {{ animatedStats.stat2 }}<span class="unit">+</span>
            </div>
            <div class="stat-label">委托企业</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">
              {{ animatedStats.stat3 }}<span class="unit">万+</span>
            </div>
            <div class="stat-label">回收商</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">
              {{ animatedStats.stat4 }}<span class="unit">万+</span>
            </div>
            <div class="stat-label">hghello.com访问量</div>
          </div>
        </div>
      </div>

      <!-- 三个公司介绍小板块 -->
      <div
        class="company-features-section"
        :class="{ 'animate-in': animationStates.featuresSection }"
      >
        <div class="container">
          <div class="features-grid">
            <!-- 企业信誉 -->
            <div class="feature-card card-bg1">
              <div class="card-content">
                <span>企业信誉</span>
                <p class="card-text">
                  让地球资源循环利用 促环保事业持续发展 还世界碧海蓝天
                </p>
              </div>
            </div>

            <!-- 核心价值观 -->
            <div class="feature-card card-bg2">
              <div class="card-content">
                <span>核心价值观</span>
                <p class="card-text">
                  服务客户 精准求实<br />
                  诚信共享 求进创新
                </p>
              </div>
            </div>

            <!-- 企业愿景 -->
            <div class="feature-card card-bg3">
              <div class="card-content">
                <span>企业愿景</span>
                <p class="card-text">
                  致力于企业物资服务 以数字化赋能资产价值
                  最大化推动可持续的产业生态
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 战略合作区域 -->
    <div
      class="strategic-cooperation-section"
      :class="{ 'animate-in': animationStates.cooperationSection }"
    >
      <div class="container">
        <!-- 战略合作标题 -->
        <div class="section-title">
          <span>战略合作</span>
        </div>

        <!-- 合作伙伴网格 -->
        <div class="cooperation-grid">
          <!-- 更专业 -->
          <div class="cooperation-card">
            <div class="card-icon">
              <SvgIcon iconName="aboutUs-logo1" className="cooperation-icon" />
              <span
                :class="{ 'divider-animate': animationStates.dividerAnimation }"
                >更专业</span
              >
            </div>
            <div class="show">
              <SvgIcon iconName="aboutUs-show" className="show-icon" />
            </div>
            <div class="card-content">
              <p class="card-title">资产增值保值</p>
              <div class="card-description">
                <p>
                  统一管理，统一处置，招商打破区域限制，拥有大量独家活跃客户，使企业废旧资产获得最大化的保值增值。
                </p>
              </div>
            </div>
          </div>

          <!-- 更放心 -->
          <div class="cooperation-card">
            <div class="card-icon">
              <SvgIcon iconName="aboutUs-logo2" className="cooperation-icon" />
              <span
                :class="{ 'divider-animate': animationStates.dividerAnimation }"
                >更放心</span
              >
            </div>
            <div class="show">
              <SvgIcon iconName="aboutUs-show" className="show-icon" />
            </div>
            <div class="card-content">
              <p class="card-title">高效处置</p>
              <div class="card-description">
                <p>
                  废旧资产集中处置，
                  我方提供一系列服务，减少贵公司人力、物力、财力成本，废旧资产处置更高效。
                </p>
              </div>
            </div>
          </div>

          <!-- 更系统 -->
          <div class="cooperation-card">
            <div class="card-icon">
              <SvgIcon iconName="aboutUs-logo3" className="cooperation-icon" />
              <span
                :class="{ 'divider-animate': animationStates.dividerAnimation }"
                >更系统</span
              >
            </div>
            <div class="show">
              <SvgIcon iconName="aboutUs-show" className="show-icon" />
            </div>
            <div class="card-content">
              <p class="card-title">公开监管</p>
              <div class="card-description">
                <p>
                  公开、公平、公正的拍卖平台，各环节透明化，便于企业监督管理，数字化系统提供服务。
                </p>
              </div>
            </div>
          </div>

          <!-- 更主动 -->
          <div class="cooperation-card">
            <div class="card-icon">
              <SvgIcon iconName="aboutUs-logo4" className="cooperation-icon" />
              <span
                :class="{ 'divider-animate': animationStates.dividerAnimation }"
                >更主动</span
              >
            </div>
            <div class="show">
              <SvgIcon iconName="aboutUs-show" className="show-icon" />
            </div>
            <div class="card-content">
              <p class="card-title">优质服务</p>
              <div class="card-description">
                <p>
                  处置过程中，全程跟踪，个性化定制便捷服务，实时做好企业与竞买人之间桥梁，真正做到客户第一。
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 社会责任区域 -->
    <div class="social-responsibility-section">
      <div class="responsibility-background">
        <!-- <SvgIcon iconName="aboutUs-bg" className="background-image" /> -->
        <div class="responsibility-content">
          <!-- 社会责任文字内容 - 标题和内容分别参与动画 -->
          <div class="responsibility-text">
            <!-- 标题部分 - 先出现 -->
            <div
              class="title responsibility-title"
              :class="{ 'animate-in': animationStates.responsibilityTitle }"
            >
              <span>社会责任</span>
            </div>
            <!-- 内容部分 - 后出现 -->
            <div
              class="responsibility-content-text"
              :class="{ 'animate-in': animationStates.responsibilityContent }"
            >
              <p>
                公司成立以来，充分发挥管理优势、资金优势、人才优势和技术优势，
              </p>
              <p>
                带动地方经济发展，增加就业岗位，增加财政收入，为地方经济的发展做出了较大的贡献。
              </p>
              <p>
                公司将"绿色、循环、低碳、环保"等理念融入废旧产业，以专业化的运营及负责任的态度赢得了客户的广泛好评。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from "vue";
import SvgIcon from "@/components/SvgIcon.vue";

// 动画状态管理
const animationStates = reactive({
  companyIntroTitle: false, // 公司简介标题
  companyIntroContent: false, // 公司简介内容
  statsSection: false, // 数据展示区域
  featuresSection: false, // 公司特色区域
  cooperationSection: false, // 战略合作区域
  responsibilitySection: false, // 社会责任区域（保留用于触发检测）
  responsibilityTitle: false, // 社会责任标题动画
  responsibilityContent: false, // 社会责任内容动画
  dividerAnimation: false, // 分割线动画状态
});

// 动画数据的响应式对象
const animatedStats = reactive({
  stat1: 0, // 交易金额 50亿+
  stat2: 0, // 客户企业 300万+
  stat3: 0, // 回收商 300万+
  stat4: 0, // Ignite.com用户 300万+
});

// 目标数值
const targetStats = {
  stat1: 50,
  stat2: 300,
  stat3: 300,
  stat4: 300,
};

// 数字动画函数
const animateNumber = (
  key: keyof typeof animatedStats,
  target: number,
  duration: number = 2000
) => {
  const start = 0;
  const startTime = Date.now();

  // 添加数字脉冲动画类
  const addPulseAnimation = () => {
    const statElements = document.querySelectorAll(".stat-number");
    statElements.forEach((element, index) => {
      if (index === Object.keys(animatedStats).indexOf(key)) {
        element.classList.add("number-animate");
        element.classList.remove("number-animate");
      }
    });
  };

  const animate = () => {
    const elapsed = Date.now() - startTime;
    const progress = Math.min(elapsed / duration, 1);

    // 使用线性动画，确保匀速执行，避免最后停顿
    const currentValue = Math.floor(start + (target - start) * progress);

    // 当数字发生变化时添加脉冲效果
    if (currentValue !== animatedStats[key] && currentValue > 0) {
      animatedStats[key] = currentValue;
      if (currentValue % Math.ceil(target / 10) === 0) {
        addPulseAnimation();
      }
    }

    if (progress < 1) {
      requestAnimationFrame(animate);
    } else {
      animatedStats[key] = target;
      addPulseAnimation(); // 最终值时也添加脉冲效果
    }
  };

  requestAnimationFrame(animate);
};

// 启动所有数字动画
const startAnimations = () => {
  // 同时启动所有数字动画，无延迟
  animateNumber("stat1", targetStats.stat1, 2000);
  animateNumber("stat2", targetStats.stat2, 2000);
  animateNumber("stat3", targetStats.stat3, 2000);
  animateNumber("stat4", targetStats.stat4, 2000);
};

// 滚动监听函数
const handleScroll = () => {
  const sections = [
    {
      element: ".company-intro-section",
      state: "companyIntroTitle",
      delay: 500,
    }, // 公司简介标题延迟300ms
    {
      element: ".company-intro-section",
      state: "companyIntroContent",
      delay: 1000,
    }, // 公司简介内容延迟800ms（标题后500ms出现）
    { element: ".stats-section", state: "statsSection", delay: 120 }, // 数据展示延迟1200ms
    {
      element: ".company-features-section",
      state: "featuresSection",
      delay: 180,
    }, // 公司特色延迟1800ms
    {
      element: ".strategic-cooperation-section",
      state: "cooperationSection",
      delay: 240,
    }, // 战略合作延迟2400ms
    {
      element: ".social-responsibility-section",
      state: "responsibilitySection",
      delay: 300,
    }, // 社会责任延迟3000ms
  ];

  sections.forEach((section) => {
    const element = document.querySelector(section.element);
    if (
      element &&
      !animationStates[section.state as keyof typeof animationStates]
    ) {
      const rect = element.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // 当元素进入视口时触发动画
      // 对于战略合作区域，需要更严格的触发条件，确保卡片能完整显示时再执行动画
      const triggerThreshold =
        section.state === "cooperationSection" ? 0.5 : 0.8;
      if (rect.top < windowHeight * triggerThreshold) {
        // 使用指定的延迟时间执行动画
        setTimeout(() => {
          animationStates[section.state as keyof typeof animationStates] = true;

          // 如果是数据展示区域，启动数字动画
          if (section.state === "statsSection") {
            setTimeout(startAnimations, 300);
          }

          // 如果是战略合作区域，延迟启动分割线动画
          if (section.state === "cooperationSection") {
            setTimeout(() => {
              animationStates.dividerAnimation = true;
            }, 500); // 战略合作动画开始后0.5秒启动分割线动画
          }

          // 如果是社会责任区域，分别启动标题和内容动画
          if (section.state === "responsibilitySection") {
            // 标题先出现
            setTimeout(() => {
              animationStates.responsibilityTitle = true;
            }, 200); // 区域触发后0.2秒显示标题

            // 内容后出现
            setTimeout(() => {
              animationStates.responsibilityContent = true;
            }, 800); // 区域触发后0.8秒显示内容（标题后0.6秒）
          }
        }, section.delay);
      }
    }
  });
};

// 组件挂载后设置滚动监听
onMounted(() => {
  // 初始检查一次
  nextTick(() => {
    handleScroll();
  });

  // 延迟1000ms后再次检查，确保所有内容加载完成
  setTimeout(() => {
    handleScroll();
  }, 501);

  // 添加滚动监听
  window.addEventListener("scroll", handleScroll);

  // 组件卸载时移除监听
  return () => {
    window.removeEventListener("scroll", handleScroll);
  };
});
</script>

<style lang="scss" scoped>
.about-us {
  width: 100%;

  // 通用动画效果
  .animate-in {
    animation: fadeInUp 0.8s ease-out forwards;
  }

  // 动画关键帧定义
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(50px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  // 数字脉冲动画
  @keyframes numberPulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
      color: #004c66;
    }
    100% {
      transform: scale(1);
      color: #333;
    }
  }

  // 初始状态：所有需要动画的区域默认隐藏
  .section-title,
  .intro-content,
  .stats-section,
  .company-features-section,
  .responsibility-title,
  .responsibility-content-text {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease-out;
  }

  // 战略合作区域背景不需要动画，只有标题和卡片需要动画
  .strategic-cooperation-section {
    // 背景区域保持可见，不需要动画
    opacity: 1;
    transform: none;

    // 卡片初始状态隐藏
    .cooperation-grid .cooperation-card {
      opacity: 0;
      transform: translateY(30px);
      // transition已在基础样式中定义，这里添加transform的transition
      transition: transform 0.6s ease-out, box-shadow 0.3s ease,
        opacity 0.6s ease-out;
    }

    // 当区域动画激活时，标题和卡片依次出现
    &.animate-in {
      // 标题动画：使用通用的fadeInUp动画
      .section-title {
        animation: fadeInUp 0.8s ease-out forwards;
      }

      .cooperation-grid .cooperation-card {
        opacity: 1;
        transform: translateY(0);

        // 为每个卡片设置不同的延迟，实现依次出现的效果
        &:nth-child(1) {
          transition-delay: 0.1s;
        }
        &:nth-child(2) {
          transition-delay: 0.2s;
        }
        &:nth-child(3) {
          transition-delay: 0.3s;
        }
        &:nth-child(4) {
          transition-delay: 0.4s;
        }
      }
    }
  }

  // 通用容器样式
  .container {
    max-width: 1280px;
    margin: 0 auto;
    color: #333;
  }

  // 通用标题样式
  .section-title {
    text-align: center;
    margin-bottom: 30px;
    font-family: "PingFang Bold";
    font-size: 30px;

    h1,
    h2 {
      font-size: 30px;
      font-weight: 600;
      margin: 0;
      display: inline-block;
    }
  }

  // 顶部Banner区域
  .banner-section {
    width: 100%;
    height: 400px;
    overflow: hidden;
    background: url("https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/banner-about_1754963381765.jpg");
    background-size: cover;

    /* .banner-image {
      width: 100%;
      height: 400px;
    } */
  }

  // 公司简介区域
  .company-intro-section {
    padding: 60px 0 136px 0;
    background: #fff;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    .container {
      max-width: 1280px;
    }

    .intro-content {
      margin-bottom: 60px;

      p {
        font-size: 18px;
        line-height: 36px;
        color: #333;
        text-align: justify;
        text-indent: 2em;
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    // 数据展示区域（现在在公司简介内部）
    .stats-section {
      max-width: 1280px;
      background: #fff;
      margin: 0 auto;

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 186px;
        padding: 0 40px;

        .stat-item {
          text-align: center;

          .stat-number {
            font-size: 60px;
            font-family: "DIN Regular";
            color: #333;
            margin-bottom: 5px;
            transition: all 0.3s ease;
            transform: scale(1);

            // 数字动画效果
            &.number-animate {
              animation: numberPulse 0.3s ease-out;
            }

            .unit {
              font-size: 18px;
              font-weight: normal;
            }
          }

          .stat-label {
            font-size: 16px;
            color: #666;
          }
        }
      }
    }
  }

  // 公司特色区域
  .company-features-section {
    position: absolute;
    bottom: -70px;
    min-width: 1280px;
    z-index: 10;

    .features-grid {
      display: flex;
      gap: 19px;

      .feature-card {
        width: 414px;
        height: 160px;
        border-radius: 12px;
        color: white;
        transition: transform 0.3s ease;

        &:hover {
          transform: translateY(-5px);

          // 鼠标悬停时分割线从左向右展开
          .card-content span::after {
            transform: scaleX(1); // 展开到完整宽度
          }
        }

        .card-content {
          display: flex;
          flex-direction: column;
          justify-content: center;
          padding: 30px 20px;

          span {
            font-family: "PingFang Bold";
            font-size: 18px;
            margin-top: 12px;
            &::after {
              content: "";
              display: block;
              width: 268px;
              height: 1px;
              // 由深到浅渐变
              background: linear-gradient(
                90deg,
                rgba(255, 255, 255, 0.6) 0%,
                rgba(255, 255, 255, 0) 100%
              );
              margin: 19px 0 10px 0;
              // 分割线初始状态：缩放为0，从左侧开始展开
              transform: scaleX(0);
              transform-origin: left; // 设置变换原点为左侧，从左端向右展开
              transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); // 2秒动画时长，使用与战略合作相同的缓动函数
            }
          }

          p {
            font-size: 14px;
            line-height: 1.6;
            opacity: 0.9;
            margin: 0;
          }
        }

        .card-text {
          max-width: 271px;
          font-size: 14px;
        }
      }

      .card-bg1 {
        background-color: #004c66;
        background: url("@/assets/icons/aboutUs/card1.svg");
      }
      .card-bg2 {
        background-color: #159fae;
        background: url("@/assets/icons/aboutUs/card2.svg");
      }
      .card-bg3 {
        background-color: #009678;
        background: url("@/assets/icons/aboutUs/card3.svg");
      }
    }
  }

  // 战略合作区域
  .strategic-cooperation-section {
    padding: 132px 0 80px 0;
    background: rgba(164, 189, 197, 0.2);

    .cooperation-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 30px;

      .cooperation-card {
        background: white;
        border-radius: 10px;
        padding: 54px 0;
        text-align: center;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        justify-content: center; // 默认状态下内容垂直居中
        height: 431px; // 固定高度，不变化
        overflow: hidden; // 隐藏溢出内容

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
          // justify-content: flex-start; // 悬停时内容顶部对齐

          // 悬停时图标向上移动并减少底部间距
          .card-icon {
            margin-bottom: 19px; // 减少底部间距，从40px减少到20px
          }

          // 悬停时标题向上移动
          .card-content {
            span {
              margin-bottom: 19px; // 减少底部间距，从19px减少到10px
            }
          }

          // 显示副标题，从左侧飞入
          .card-content > p {
            opacity: 1;
            transform: translateX(0); // 重置位置
            transition-delay: 0.1s; // 悬停时延迟0.1s开始动画
          }

          // 显示描述文字，从右侧飞入
          .card-content .card-description {
            opacity: 1;
            max-height: 100px; // 设置足够的高度容纳文字
            margin-top: 18px; // 与上方元素的间距
            transition-delay: 0.2s; // 悬停时延迟0.1s开始动画
          }

          .show {
            opacity: 0;
            // 悬停时箭头变淡但不消失，保持动画同步
            transition: opacity 0.3s ease;
            margin-top: 0;
            width: 0;
            height: 0;
            // 高度变少的动画
            transition: height 0.8s ease;
            transition: margin-top 0.8s ease;
            .show-icon {
            width: 0;
            height: 0;
          }
          }
        }

        // 查看更多箭头样式
        .show {
          margin-top: 20px;
          animation: bounce-in 2s infinite;
          .show-icon {
            width: 20px;
            height: 10px;
          }
        }

        // 箭头动画，重复执行
        @keyframes bounce-in {
          0% {
            transform: translateY(0);
          }
          50% {
            transform: translateY(-10px);
          }
          100% {
            transform: translateY(0);
          }
        }

        .card-icon {
          // 为图标添加平滑的过渡动画，包括位置变化的过渡

          .cooperation-icon {
            width: 87px;
            height: 87px;
            color: #667eea;
            margin-bottom: 40px;
          }

          span {
            font-size: 24px;
            color: #004c66;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: "FZZongYi-M05S";
            // 使用伪类给标题前添加分割线
            &::before {
              content: "";
              display: block;
              width: 100px;
              height: 1px;
              background: linear-gradient(
                90deg,
                rgba(0, 76, 102, 0) 0%,
                rgba(0, 76, 102, 0.6) 100%
              );
              margin-right: 10px;
              // 左侧分割线初始状态
              transform: scaleX(0); // 初始缩放为0
              transform-origin: right; // 设置变换原点为右侧，从右端向左展开
              opacity: 0;
            }
            // 使用伪类给标题后添加分割线
            &::after {
              content: "";
              display: block;
              width: 100px;
              height: 1px;
              // 由深到浅渐变
              background: linear-gradient(
                90deg,
                rgba(0, 76, 102, 0.6) 0%,
                rgba(0, 76, 102, 0) 100%
              );
              margin-left: 10px;
              // 右侧分割线初始状态
              transform: scaleX(0); // 初始缩放为0
              transform-origin: left; // 设置变换原点为左侧，从左端向右展开
              opacity: 0;
            }

            // 分割线动画激活状态
            &.divider-animate {
              &::before {
                animation: expandLeftDividerScale 2s
                  cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
              }

              &::after {
                animation: expandRightDividerScale 2s
                  cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
              }
            }

            // 分割线展开动画定义
            // 左侧分割线从右至左展开动画（大屏幕）- 使用scaleX实现
            @keyframes expandLeftDividerScale {
              0% {
                transform: scaleX(0);
                opacity: 0;
              }
              20% {
                opacity: 1;
              }
              100% {
                transform: scaleX(1);
                opacity: 1;
              }
            }

            // 右侧分割线从左至右展开动画（大屏幕）- 使用scaleX实现
            @keyframes expandRightDividerScale {
              0% {
                transform: scaleX(0);
                opacity: 0;
              }
              20% {
                opacity: 1;
              }
              100% {
                transform: scaleX(1);
                opacity: 1;
              }
            }
          }
        }

        .card-content {
          // 为整个内容区域添加平滑的过渡动画
          transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

          .card-title {
            font-family: "PingFang Bold";
            font-size: 18px;
          }

          > p {
            font-size: 18px;
            color: #333;
            margin-bottom: 18px;
            // 默认状态下隐藏副标题
            opacity: 0;
            transform: translateX(-30px); // 从左侧飞入
            transition: opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.6s cubic-bezier(0.4, 0, 0.2, 1); // 使用更流畅的缓动函数，移除延迟
          }

          .card-description {
            // 默认状态下隐藏描述文字
            opacity: 0;
            max-height: 0;
            overflow: hidden;
            transform: translateX(50px); // 从右侧飞入
            transition: opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1),
              max-height 0.6s cubic-bezier(0.4, 0, 0.2, 1),
              margin-top 0.6s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.6s cubic-bezier(0.4, 0, 0.2, 1); // 使用更流畅的缓动函数，移除延迟
            margin-top: 0;

            p {
              font-size: 16px;
              color: #666;
              line-height: 1.5;
              max-width: 205px;
              text-align: center;
            }
          }
        }
      }
    }
  }

  // 社会责任区域
  .social-responsibility-section {
    .responsibility-background {
      width: 100%;
      height: 800px;
      overflow: hidden;
      background: url("https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/social-bg_1754960302870.png");
      background-size: cover;

      .responsibility-content {
        z-index: 2;
        height: 100%;
        padding: 187px 338px;
        background: rgba(0, 76, 102, 0.2);

        .responsibility-text {
          // 标题样式
          .responsibility-title {
            text-align: left;
            margin-bottom: 40px;
            color: #fff;
            font-size: 40px;
          }

          // 内容区域样式
          .responsibility-content-text {
            p {
              font-size: 16px;
              line-height: 1.2;
              color: white;
              margin-bottom: 20px;
              text-align: left;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 1024px) {
    .company-intro-section {
      .stats-section {
        .stats-grid {
          grid-template-columns: repeat(2, 1fr);
          gap: 30px;
          padding: 0 20px;
        }
      }
    }

    .company-features-section {
      .features-grid {
        grid-template-columns: 1fr;
        gap: 30px;
      }
    }

    .strategic-cooperation-section {
      .cooperation-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
      }
    }
  }

  @media (max-width: 768px) {
    .banner-section {
      height: 300px;
    }

    .company-intro-section {
      padding: 60px 0;

      .intro-content {
        margin-bottom: 40px;

        p {
          font-size: 16px;
          line-height: 32px;
        }
      }

      .stats-section {
        .stats-grid {
          grid-template-columns: 1fr;
          gap: 20px;
          padding: 0 20px;

          .stat-item {
            .stat-number {
              font-size: 36px;
            }
          }
        }
      }
    }

    .company-features-section,
    .strategic-cooperation-section {
      padding: 60px 0;
    }

    .section-title {
      margin-bottom: 40px;

      h1,
      h2 {
        font-size: 28px;
      }
    }

    .strategic-cooperation-section {
      .cooperation-grid {
        grid-template-columns: 1fr;
      }
    }

    .social-responsibility-section {
      .responsibility-background {
        height: 600px;
      }
    }
  }

  @media (max-width: 480px) {
    .container {
      padding: 0 15px;
    }

    .banner-section {
      height: 250px;
    }

    .company-intro-section {
      padding: 40px 0;

      .intro-content {
        margin-bottom: 30px;

        p {
          font-size: 14px;
          line-height: 28px;
          text-indent: 1.5em;
        }
      }

      .stats-section {
        padding: 30px 0;

        .stats-grid {
          gap: 15px;
          padding: 0 15px;

          .stat-item {
            .stat-number {
              font-size: 28px;

              .unit {
                font-size: 18px;
              }
            }

            .stat-label {
              font-size: 14px;
            }
          }
        }
      }
    }

    .company-features-section,
    .strategic-cooperation-section {
      padding: 40px 0;
    }

    .section-title {
      h1,
      h2 {
        font-size: 24px;
      }
    }

    .social-responsibility-section {
      .responsibility-background {
        height: 500px;

        .responsibility-text {
          p {
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style>
