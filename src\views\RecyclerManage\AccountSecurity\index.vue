<template>
  <div class="account-security" v-loading="loading" element-loading-text="加载中...">
    <!-- 用户信息顶部 -->
    <div class="user-header">
      <div class="user-avatar">
        <div class="avatar-bg" v-if="!userInfo.avatar">
          <SvgIcon iconName="user" className="avatar-icon" />
        </div>
        <img v-else :src="userInfo.avatar" alt="头像" class="avatar-image" />
        <div class="certification-info">
          <div class="phone-display">
            {{ formatPhone(userInfo.phone) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 安全设置列表 -->
    <div class="security-list">
      <!-- 登录密码 -->
      <div class="security-item">
        <div class="security-content">
          <div class="label">
            <span class="label-text">登录密码</span>
            <span class="tips">(互联网账号存在被盗风险，建议您定期更改密码以保护账户安全)</span>
          </div>
          <div class="value">
            已设置登录密码，建议您定期更改密码以保护账户安全
          </div>
        </div>
        <button class="edit-btn" @click="handleEdit('password')">编辑</button>
      </div>

      <!-- 联系方式 -->
      <div class="security-item">
        <div class="security-content">
          <div class="label">
            <span class="label-text">联系方式</span>
            <span class="tips">(建议您提供有效联系方式，随时随地获得商机)</span>
          </div>
          <div class="value">{{ userInfo.phone || "未绑定手机号" }}</div>
        </div>
      </div>
    </div>

    <!-- 修改登录密码弹窗 -->
    <Modal
      v-model="showPasswordModal"
      title="修改登录密码"
      width="658px"
      :confirm-loading="passwordLoading"
      confirm-button-text="确定"
      cancel-button-text="取消"
      @confirm="handlePasswordConfirm"
      @cancel="handlePasswordCancel"
    >
      <div class="password-form">
        <div class="form-item">
          <label class="form-label">当前密码</label>
          <el-input
            v-model="passwordForm.currentPassword"
            type="password"
            placeholder="输入当前的密码"
            show-password
            clearable
          />
        </div>

        <div class="form-item">
          <label class="form-label">新密码</label>
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入8位以上的新密码，需包含大小写字母、数字及特殊字符"
            show-password
            clearable
          />
        </div>

        <div class="form-item">
          <label class="form-label">确认新密码</label>
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次确认新密码"
            show-password
            clearable
          />
        </div>
      </div>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import SvgIcon from "@/components/SvgIcon.vue";
import Modal from "@/components/Modal.vue";
import { ElMessage } from "element-plus";
import { systemApi } from "@/utils/api-new";

// 定义用户信息接口
interface UserInfo {
  birthday?: string;
  relTenantIds?: string;
  bpmStatus?: any;
  sex_dictText?: string;
  smsCode?: any;
  activitiSync?: number;
  userIdentity?: number;
  status_dictText?: string;
  delFlag?: number;
  reviewStatus_dictText?: any;
  workNo?: string;
  post?: any;
  updateBy?: string;
  orgCode?: string;
  izBindThird?: boolean;
  roleCode?: any;
  id?: string;
  email?: string;
  post_dictText?: any;
  clientId?: any;
  roleCode_dictText?: any;
  sex?: number;
  homePath?: any;
  departIds_dictText?: any;
  telephone?: any;
  updateTime?: string;
  departIds?: string;
  avatar?: string;
  realname?: string;
  createBy?: any;
  postText?: any;
  phone?: string;
  createTime?: string;
  orgCodeTxt?: any;
  reviewStatus?: any;
  loginTenantId?: number;
  loginSource?: any;
  username?: string;
  status?: number;
}

// 用户信息数据
const userInfo = ref<UserInfo>({});
const loading = ref(false);

// 弹窗状态管理
const showPasswordModal = ref(false); // 修改密码弹窗
const passwordLoading = ref(false); // 修改密码加载状态

// 修改密码表单数据
const passwordForm = ref({
  currentPassword: "", // 当前密码
  newPassword: "", // 新密码
  confirmPassword: "", // 确认新密码
});

/**
 * 格式化手机号显示
 * @param phone 手机号
 * @returns 格式化后的手机号
 */
const formatPhone = (phone?: string): string => {
  if (!phone) return '暂未绑定手机号';

  // 如果手机号长度为11位，进行脱敏处理
  if (phone.length === 11) {
    return `${phone.slice(0, 3)} ****${phone.slice(-4)}`;
  }

  return phone;
};

/**
 * 获取用户数据
 */
const getUserData = async () => {
  loading.value = true;

  try {
    const response = await systemApi.getUserData();

    // 根据API响应格式处理数据
    if (response && response.success && response.result) {
      userInfo.value = response.result;
      console.log('获取到的用户数据:', response.result);
    } else if (response) {
      // 如果直接返回用户数据
      userInfo.value = response as any;
      console.log('获取到的用户数据:', response);
    } else {
      ElMessage.error('获取用户数据失败');
    }
  } catch (error) {
    console.error('获取用户数据失败:', error);
    ElMessage.error('获取用户数据失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

/**
 * 处理编辑操作
 * @param type 编辑类型
 */
const handleEdit = (type: string) => {
  if (type === "password") {
    showPasswordModal.value = true;
    // 重置表单
    passwordForm.value = {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    };
  }
};

/**
 * 修改密码确认
 */
const handlePasswordConfirm = async () => {
  // 验证表单
  if (!passwordForm.value.currentPassword) {
    ElMessage.error("请输入当前密码");
    return;
  }

  if (!passwordForm.value.newPassword) {
    ElMessage.error("请输入新密码");
    return;
  }

  if (!passwordForm.value.confirmPassword) {
    ElMessage.error("请确认新密码");
    return;
  }

  // 验证新密码是否一致
  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    ElMessage.error("两次输入的新密码不一致");
    return;
  }

  // 验证密码强度
  if (passwordForm.value.newPassword.length < 6) {
    ElMessage.error("密码长度至少6位");
    return;
  }

  if (!userInfo.value.username) {
    ElMessage.error("用户信息不完整");
    return;
  }

  passwordLoading.value = true;

  try {
    const res = await systemApi.updatePassword({
      username: userInfo.value.username,
      oldpassword: passwordForm.value.currentPassword,
      password: passwordForm.value.newPassword,
      confirmpassword: passwordForm.value.confirmPassword,
    });

    if(res.success === false){
      ElMessage.error(res.message);
      return;
    }

    showPasswordModal.value = false;
    ElMessage.success(res.message);
  } catch (error) {
    console.error("密码修改失败:", error);
    ElMessage.error("密码修改失败，请检查当前密码是否正确");
  } finally {
    passwordLoading.value = false;
  }
};

/**
 * 修改密码取消
 */
const handlePasswordCancel = () => {
  showPasswordModal.value = false;
  // 重置表单
  passwordForm.value = {
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  };
};

// 组件挂载时获取用户数据
onMounted(() => {
  getUserData();
});
</script>

<style lang="scss" scoped>
.account-security {
  margin: 30px 20px 20px 20px;
  min-height: 400px; // 确保加载状态有足够的高度
}

// 用户信息顶部样式
.user-header {
  margin-bottom: 32px;

  .user-avatar {
    display: flex;
    align-items: center;
    gap: 9px;

    .avatar-bg {
      background-color: #004c66;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;

      .avatar-icon {
        width: 24px;
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .avatar-image {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid #004c66;
    }

    .certification-info {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .phone-display {
        background-color: #e6eef0;
        border: 1px solid #004c66;
        padding: 5px 10px;
        border-radius: 6px;
        font-size: 12px;
        color: #004c66;
      }
    }
  }
}

// 安全设置列表样式
.security-list {
  .security-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid;
    // 渐变
    border-image: linear-gradient(
      to right,
      rgba(221, 221, 221, 1),
      rgba(255, 255, 255, 1)
    );
    border-image-slice: 1;

    &.no-border {
      border-bottom: none;
    }

    .security-content {
      flex: 1;

      .label {
        font-size: 18px;
        color: #333;
        margin-bottom: 10px;

        .label-text {
          font-family: "PingFang Bold";
        }

        .tips {
          font-size: 12px;
          color: #999;
          margin-left: 10px;
        }
      }

      .value {
        font-size: 14px;
        color: #999;
        line-height: 1.5;
      }
    }

    .edit-btn {
      padding: 8px 32px;
      background: #fff;
      border: 1px solid #ddd;
      border-radius: 6px;
      color: #333;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #004c66;
        color: #004c66;
        background-color: rgba(0, 76, 102, 0.2);
      }
    }
  }
}

/* 弹窗表单样式 */
.password-form {
  padding: 20px;
}

.form-item {
  margin-bottom: 24px;

  .el-input {
    height: 46px;
  }
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 16px;
  color: #333;
  font-family: "PingFang Bold";
}

/* Element Plus 输入框样式覆盖 */
.password-form .el-input {
  --el-input-border-radius: 6px;
  --el-input-border-color: #e0e0e0;
  --el-input-focus-border-color: #004c66;
}

.password-form .el-input__wrapper {
  padding: 12px 16px;
  box-shadow: 0 0 0 1px var(--el-input-border-color) inset;
}

.password-form .el-input__wrapper:hover {
  box-shadow: 0 0 0 1px var(--el-input-hover-border-color) inset;
}

.password-form .el-input.is-focus .el-input__wrapper {
  box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset;
}
</style>