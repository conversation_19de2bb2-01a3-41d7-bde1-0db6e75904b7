<template>
  <div class="step-panel">
    <!-- 使用 ElementPlus 表单组件包裹整个表单 -->
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-position="left"
      label-width="80px"
      :scroll-to-error="true"
    >
      <!-- 信息有效性 -->
      <div class="form-section">
        <h3 class="section-title">信息有效性</h3>
        <div class="form-row">
          <el-form-item label="时间信息" prop="validity.validDate" class="form-item-third">
            <el-date-picker
              v-model="formData.validity.validDate"
              type="datetime"
              placeholder="请选择有效期时间"
              style="width: 100%"
              size="large"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled-date="disabledDate"
            />
          </el-form-item>
          <div class="form-item-third"></div>
          <div class="form-item-third"></div>
        </div>
      </div>

      <!-- 联系人信息 -->
      <div class="form-section">
        <h3 class="section-title">联系人信息</h3>
        <div class="form-row">
          <el-form-item label="联系姓名" prop="contactInfo.contactName" class="form-item-third">
            <el-input
              v-model="formData.contactInfo.contactName"
              placeholder="请输入联系姓名"
              size="large"
            />
          </el-form-item>
          <el-form-item label="联系电话" prop="contactInfo.contactPhone" class="form-item-third">
            <el-input
              v-model="formData.contactInfo.contactPhone"
              placeholder="请输入联系电话"
              maxlength="11"
              size="large"
            />
          </el-form-item>
          <div class="form-item-third"></div>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type { FormInstance } from 'element-plus'

// 定义 Props
interface Props {
  modelValue: {
    validity: {
      validDate: string
    }
    contactInfo: {
      contactName: string
      contactPhone: string
    }
  }
}

const props = defineProps<Props>()

// 定义 Emits
interface Emits {
  (e: 'update:modelValue', value: Props['modelValue']): void
}

const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInstance>()

// 计算属性：表单数据
const formData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单校验规则
const formRules = {
  'validity.validDate': [
    { required: true, message: '请选择有效期时间', trigger: 'change' }
  ],
  'contactInfo.contactName': [
    { required: true, message: '请输入联系姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '联系姓名长度应在2-20个字符之间', trigger: 'blur' }
  ],
  'contactInfo.contactPhone': [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 禁用过去的日期
const disabledDate = (time: Date) => {
  // 不能选择今天之前的日期
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

// 表单验证方法
const validateForm = async (): Promise<boolean> => {
  try {
    await formRef.value?.validate()
    return true
  } catch (error) {
    console.error('表单验证失败:', error)
    return false
  }
}

// 清除表单验证
const clearValidate = () => {
  formRef.value?.clearValidate()
}

// 暴露方法给父组件
defineExpose({
  validateForm,
  clearValidate
})
</script>

<style scoped lang="scss">
.step-panel {
  background: #fff;
  border-radius: 8px;
}

.form-section {
  margin-bottom: 32px;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
  padding-bottom: 8px;

  &::before {
    content: '';
    display: block;
    width: 4px;
    height: 18px;
    margin-right: 8px;
    background-color: #004c66;
  }
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 16px;
}

.form-item-third {
  flex: 1;
  min-width: calc(33.333% - 14px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 16px;
  }

  .form-item-third {
    width: 100%;
    min-width: auto;
  }
}
</style>