# 图片列表自动滚动功能实现说明

## 功能概述
为拍卖详情页面的图片列表容器添加了鼠标移动时的自动滚动功能。当用户鼠标移动到**滚动区域的左边缘或右边缘**时，会自动向前或向后滚动一张图片的距离。

## 实现的功能特性

### 1. 智能边缘检测
- 检测鼠标在滚动区域的位置
- 左边缘80px区域触发向左滚动
- 右边缘80px区域触发向右滚动
- 使用 `getBoundingClientRect()` 精确计算鼠标位置

### 2. 自动滚动机制
- **向左滚动**：当鼠标移动到滚动区域左边缘80px区域时触发
- **向右滚动**：当鼠标移动到滚动区域右边缘80px区域时触发
- **单次滚动**：每次只滚动一张图片的距离（120px）
- **平滑滚动**：使用 `smooth` 行为，提供流畅的用户体验

### 3. 用户体验优化
- **快速响应**：鼠标移动到边缘后100ms开始滚动
- **即时停止**：鼠标移出滚动区域时立即停止自动滚动
- **防重复**：滚动过程中阻止新的滚动操作
- **视觉提示**：边缘区域有淡色渐变提示，鼠标悬停时显示

## 核心方法说明

### `isMouseAtScrollEdge(mouseX: number)`
检查鼠标是否在滚动区域的边缘位置
- 返回 `{ isLeftEdge: boolean; isRightEdge: boolean }`
- 通过鼠标的clientX坐标和容器位置计算
- 边缘触发区域宽度为80px

### `autoScrollLeft()` 和 `autoScrollRight()`
执行自动滚动操作
- 每次滚动120px（一张图片的宽度）
- 只滚动一次，避免过度滚动
- 滚动完成后400ms重置状态

### `handleScrollAreaMouseMove(event: MouseEvent)`
处理滚动区域的鼠标移动事件
- 检测鼠标是否在滚动区域边缘
- 根据位置和滚动能力决定是否启动自动滚动
- 100ms延迟启动滚动，避免频繁触发

### `handleScrollAreaMouseLeave()`
处理滚动区域的鼠标移出事件
- 立即停止所有自动滚动操作
- 清理定时器

## 技术实现细节

### 1. 状态管理
```typescript
const autoScrollTimer = ref<number | null>(null);  // 自动滚动定时器
const isAutoScrolling = ref<boolean>(false);       // 滚动状态标记
```

### 2. 事件绑定
在滚动容器上添加了鼠标事件：
```vue
@mousemove="handleScrollAreaMouseMove"
@mouseleave="handleScrollAreaMouseLeave"
```

### 3. 生命周期管理
在组件卸载时清理定时器，防止内存泄漏：
```typescript
onUnmounted(() => {
  stopAutoScroll();
});
```

## 使用场景
- 当图片列表较长，超出容器可视范围时
- 用户想要快速浏览更多图片时
- 解决了图片未完全显示时无法触发滚动的问题
- 提供更好的图片浏览体验

## 兼容性
- 支持现代浏览器
- 使用标准的DOM API
- 平滑滚动需要浏览器支持 `scroll-behavior: smooth`

## 注意事项
1. 只有在可以滚动的情况下才会触发自动滚动
2. 滚动过程中会阻止新的滚动操作
3. 鼠标移出滚动区域后会立即停止滚动
4. 每次只滚动一张图片的距离，避免滚动过度
5. 边缘触发区域为80px，可根据需要调整
6. 鼠标悬停时会显示边缘区域的视觉提示
