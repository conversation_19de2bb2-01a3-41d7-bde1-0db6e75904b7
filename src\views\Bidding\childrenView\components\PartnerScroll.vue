<template>
  <div class="partner-scroll">
    <!-- 第一行：从左向右滚动 -->
    <div class="scroll-row">
      <div class="scroll-content scroll-left-to-right">
        <!-- 原始内容 -->
        <div
          v-for="(partner, index) in firstRowPartners"
          :key="`first-${index}`"
          class="partner-card"
        >
          <el-image :src="partner.icon" class="partner-logo" />
          <!-- <span class="partner-name">{{ partner.name }}</span> -->
        </div>
      </div>
    </div>

    <!-- 第二行：从右向左滚动 -->
    <div class="scroll-row">
      <div class="scroll-content scroll-right-to-left">
        <!-- 原始内容 -->
        <div
          v-for="(partner, index) in secondRowPartners"
          :key="`second-${index}`"
          class="partner-card"
        >
          <el-image :src="partner.icon" class="partner-logo" />
          <!-- <span class="partner-name">{{ partner.name }}</span> -->
        </div>
      </div>
    </div>

    <!-- 第三行：从左向右滚动 -->
    <div class="scroll-row">
      <div class="scroll-content scroll-left-to-right">
        <!-- 原始内容 -->
        <div
          v-for="(partner, index) in thirdRowPartners"
          :key="`third-${index}`"
          class="partner-card"
        >
          <el-image :src="partner.icon" class="partner-logo" />
          <!-- <span class="partner-name">{{ partner.name }}</span> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from "@/components/SvgIcon.vue";
import xugang from "@/assets/images/enterpriseCooperation/partner/xugang.png";
import weihua from "@/assets/images/enterpriseCooperation/partner/weihua.png";
import tianrui from "@/assets/images/enterpriseCooperation/partner/tianrui.png";
import youse from "@/assets/images/enterpriseCooperation/partner/youse.png";
import xugong from "@/assets/images/enterpriseCooperation/partner/xugong.png";
import zgbingqi from "@/assets/images/enterpriseCooperation/partner/zgbingqi.png";
import xinhuayejin from "@/assets/images/enterpriseCooperation/partner/xinhuayejin.png";
import zhongtie from "@/assets/images/enterpriseCooperation/partner/zhongtie.png";
import dalianshuini from "@/assets/images/enterpriseCooperation/partner/dalianshuini.png";
import guotai from "@/assets/images/enterpriseCooperation/partner/guotai.png";
import yutong from "@/assets/images/enterpriseCooperation/partner/yutong.png";

// 合作伙伴数据接口
interface Partner {
  name: string;
  icon: string;
}

// 合作伙伴数据
const partners: Partner[] = [
  {
    name: "大连水泥集团有限公司",
    icon: dalianshuini,
  },
  { name: "中国中铁", icon: zhongtie },
  { name: "中国有色金属集团", icon: youse },
  { name: "徐钢集团", icon: xugang },
  {
    name: "大连水泥集团有限公司",
    icon: dalianshuini,
  },
  { name: "中国中铁", icon: zhongtie },
  { name: "中国有色金属集团", icon: youse },
  { name: "徐钢集团", icon: xugang },
  {
    name: "大连水泥集团有限公司",
    icon: dalianshuini,
  },
  { name: "中国中铁", icon: zhongtie },
  { name: "中国有色金属集团", icon: youse },
  { name: "徐钢集团", icon: xugang },
  { name: "宇通集团", icon: yutong },
  { name: "新华冶金", icon: xinhuayejin },
  { name: "天瑞集团", icon: tianrui },
  { name: "国泰", icon: guotai },
  { name: "宇通集团", icon: yutong },
  { name: "新华冶金", icon: xinhuayejin },
  { name: "天瑞集团", icon: tianrui },
  { name: "国泰", icon: guotai },
  { name: "宇通集团", icon: yutong },
  { name: "新华冶金", icon: xinhuayejin },
  { name: "天瑞集团", icon: tianrui },
  { name: "国泰", icon: guotai },
  { name: "天瑞集团", icon: tianrui },
  { name: "国泰", icon: guotai },
  { name: "天瑞集团", icon: tianrui },
  { name: "国泰", icon: guotai },
  { name: "天瑞集团", icon: tianrui },
  { name: "国泰", icon: guotai },
  { name: "天瑞集团", icon: tianrui },
  { name: "天瑞集团", icon: tianrui },
  { name: "国泰", icon: guotai },
  { name: "天瑞集团", icon: tianrui },
  { name: "国泰", icon: guotai },
  { name: "天瑞集团", icon: tianrui },
  { name: "国泰", icon: guotai },
  { name: "天瑞集团", icon: tianrui },
  { name: "国泰", icon: guotai },
];

// 将合作伙伴分配到三行
const firstRowPartners = partners.slice(0, 11);
const secondRowPartners = partners.slice(11, 22);
const thirdRowPartners = partners.slice(22, 33);
</script>

<style scoped lang="scss">
.partner-scroll {
  background-color: #f2f2f2;
  padding: 0 0 60px 0;
  overflow: hidden;
  width: calc(100vw - 20px);
}

.scroll-row {
  margin-bottom: 30px;
  overflow: hidden;
  width: calc(100vw - 20px);

  &:last-child {
    margin-bottom: 0;
  }
}

.scroll-content {
  display: flex;
  gap: 30px;
  width: fit-content;
  animation-duration: 50s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  will-change: transform;
  white-space: nowrap;
  transform: translateZ(0); // 启用硬件加速
  backface-visibility: hidden; // 优化渲染性能
  
  &.scroll-left-to-right {
    animation-name: scrollLeftToRight;
  }
  
  &.scroll-right-to-left {
    animation-name: scrollRightToLeft;
  }
  
  &:hover {
    animation-play-state: paused;
  }
}

// 从左到右滚动动画 - 真正无缝循环
@keyframes scrollLeftToRight {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-33.333333%);
  }
}

// 从右到左滚动动画 - 真正无缝循环
@keyframes scrollRightToLeft {
  from {
    transform: translateX(-33.333333%);
  }
  to {
    transform: translateX(0);
  }
}

.partner-card {
  background-color: #ffffff;
  border-radius: 10px;
  height: 137px;
  min-width: 280px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transform: scale(1.05);
  }
}

.partner-logo {
  width: 300px;
  height: 137px;
  color: #004c66;
}

.partner-name {
  font-size: 14px;
  font-weight: 500;
  color: #333333;
  text-align: center;
  line-height: 1.4;
}

// 响应式设计
@media (max-width: 1440px) {
  .partner-card {
    min-width: 260px;
    height: 120px;
    padding: 18px;
  }

  .partner-logo {
    width: 50px;
    height: 50px;
    margin-bottom: 10px;
  }

  .partner-name {
    font-size: 13px;
  }
}

@media (max-width: 1200px) {
  .partner-scroll {
    padding: 40px 0;
  }

  .scroll-row {
    margin-bottom: 20px;
  }

  .scroll-content {
    gap: 20px;
  }

  .partner-card {
    min-width: 240px;
    height: 110px;
    padding: 15px;
  }

  .partner-logo {
    width: 45px;
    height: 45px;
    margin-bottom: 8px;
  }

  .partner-name {
    font-size: 12px;
  }
}
</style>
