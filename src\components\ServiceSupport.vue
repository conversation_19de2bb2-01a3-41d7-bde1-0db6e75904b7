<template>
  <div class="service-support">
    <!-- 上半部分：搜索区域 -->
    <div class="search-section">
      <div class="search-content">
        <p class="search-title">{{ title || "嗨！有什么需要帮忙的吗？" }}</p>
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            :placeholder="placeholder || '搜索关键词'"
            class="search-input"
            @keyup.enter="handleSearch"
          >
            <template #suffix>
              <el-button class="search-btn" @click="handleSearch">
                <template #default>
                  <SvgIcon iconName="support-search" className="search-icon" />
                </template>
              </el-button>
            </template>
          </el-input>
        </div>
      </div>
    </div>

    <!-- 下半部分：导航和问题列表 -->
    <div class="content-section">
      <div class="content-container">
        <!-- 左侧导航栏 -->
        <div class="navigation">
          <div
            class="nav-item"
            v-for="(item, index) in navigationItems"
            :key="index"
            :class="{ active: activeNavIndex === index }"
            @click="handleNavClick(index)"
          >
            <SvgIcon :iconName="item.icon" className="nav-icon" />
            <span class="nav-text">{{ item.text }}</span>
          </div>
        </div>

        <!-- 右侧问题列表 -->
        <div class="question-list">
          <div class="question-scroll-area">
            <div
              class="question-item"
              v-for="(question, index) in currentQuestions"
              :key="index"
            >
              <div class="question-header" @click="toggleQuestion(index)">
                <SvgIcon
                  :iconName="question.expanded ? 'support-close' : 'support-open'"
                  className="expand-icon"
                />
                <span class="question-title">{{ question.title }}</span>
              </div>
              <div class="question-answer" v-show="question.expanded">
                <div class="answer-content">
                  {{ question.answer }}
                </div>
                <div class="helpful-section">
                  <span class="helpful-text">对您是否有帮助？</span>
                  <div class="helpful-buttons">
                    <el-button
                      size="small"
                      type="primary"
                      plain
                      @click="handleHelpful(index, true)"
                    >
                      是
                    </el-button>
                    <el-button
                      size="small"
                      plain
                      @click="handleHelpful(index, false)"
                    >
                      否
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
// ElementPlus 组件由 unplugin-element-plus 自动引入
import SvgIcon from "@/components/SvgIcon.vue";

// 定义组件属性接口
interface NavigationItem {
  icon: string;
  text: string;
}

interface Question {
  title: string;
  answer: string;
  expanded?: boolean;
}

interface Props {
  title?: string; // 搜索区域标题
  placeholder?: string; // 搜索框占位符
  navigationItems: NavigationItem[]; // 导航栏项目
  onSearch?: (keyword: string) => void; // 搜索回调函数
  onGetQuestions?: (navIndex: number) => Promise<Question[]>; // 获取问题列表的回调函数
  onHelpfulFeedback?: (questionIndex: number, isHelpful: boolean) => void; // 帮助反馈回调
}

// 定义组件属性
const props = withDefaults(defineProps<Props>(), {
  title: "嗨！有什么需要帮忙的吗？",
  placeholder: "搜索关键词",
});

// 响应式数据
const searchKeyword = ref(""); // 搜索关键词
const activeNavIndex = ref(0); // 当前激活的导航索引
const questionsList = ref<Question[][]>([]); // 所有导航对应的问题列表
const loading = ref(false); // 加载状态

// 计算当前显示的问题列表
const currentQuestions = computed(() => {
  return questionsList.value[activeNavIndex.value] || [];
});

// 处理搜索
const handleSearch = () => {
  if (props.onSearch && searchKeyword.value.trim()) {
    props.onSearch(searchKeyword.value.trim());
  }
};

// 处理导航点击
const handleNavClick = async (index: number) => {
  activeNavIndex.value = index;

  // 如果该导航的问题列表还未加载，则加载
  if (!questionsList.value[index] && props.onGetQuestions) {
    loading.value = true;
    try {
      const questions = await props.onGetQuestions(index);
      // 为每个问题添加展开状态
      const questionsWithState = questions.map((q) => ({
        ...q,
        expanded: false,
      }));
      questionsList.value[index] = questionsWithState;
    } catch (error) {
      console.error("获取问题列表失败:", error);
      questionsList.value[index] = [];
    } finally {
      loading.value = false;
    }
  }
};

// 切换问题展开/折叠状态
const toggleQuestion = (index: number) => {
  const questions = questionsList.value[activeNavIndex.value];
  if (questions && questions[index]) {
    questions[index].expanded = !questions[index].expanded;
  }
};

// 处理帮助反馈
const handleHelpful = (questionIndex: number, isHelpful: boolean) => {
  if (props.onHelpfulFeedback) {
    props.onHelpfulFeedback(questionIndex, isHelpful);
  }
};

// 组件挂载时加载第一个导航的问题列表
onMounted(() => {
  if (props.navigationItems.length > 0) {
    handleNavClick(0);
  }
});
</script>

<style scoped lang="scss">
.service-support {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
}

// 上半部分：搜索区域
.search-section {
  width: 100%;
  height: 340px;
  background-image: url("https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/support-banner_1754963644959.jpg");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  position: relative;
  padding: 140px 0 0 17vw;

  .search-content {
    color: white;

    .search-title {
      font-size: 36px;
      margin-bottom: 42px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .search-box {
      width: 600px;
      max-width: 90vw;

      .search-input {
        :deep(.el-input__wrapper) {
          border-radius: 10px;
          padding: 0 2px 0 20px;
          height: 50px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 10px;
        }

        :deep(.is-focus){
          border: 1px solid #ffffff;
          box-shadow: 0 0 0 rgba(0, 0, 0, 0.15);
        }

        :deep(.el-input__inner) {
          font-size: 16px;
          padding-right: 50px;
          color: white;

          &::placeholder {
            font-size: 16px;
            color: #fff;
          }
        }

        .search-btn {
          border-radius: 10px;
          width: 70px;
          height: 44px;
          padding: 0;
          border: none;
          box-shadow: 0 2px 8px rgba(0, 76, 102, 0.3);
          .search-icon {
            width: 17px;
            height: 18px;
          }
        }
      }
    }
  }
}

// 下半部分：内容区域
.content-section {
  padding: 20px 20px 30px 20px;
  min-height: calc(100vh - 340px);

  .content-container {
    max-width: 1280px;
    margin: 0 auto;
    display: flex;
    gap: 10px;
    align-items: flex-start;
  }
}

// 左侧导航栏
.navigation {
  width: 280px;
  background: white;
  border-radius: 10px;
  padding: 20px 0;
  flex-shrink: 0;
  height: 575px;

  .nav-item {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 2px 20px;

    &:hover {
      background-color: rgba(0, 76, 102, 0.1);
      color: var(--el-color-primary);
      border-radius: 10px;
    }

    &.active {
      background-color: rgba(0, 76, 102, 0.1);
      color: var(--el-color-primary);
      border-radius: 10px;

      .nav-icon {
        color: var(--el-color-primary);
      }
    }

    .nav-icon {
      width: 20px;
      height: 20px;
      margin-right: 12px;
      color: #666;
      transition: color 0.3s ease;
    }

    .nav-text {
      font-size: 15px;
      font-weight: 500;
      color: #333;
    }
  }
}

// 右侧问题列表
.question-list {
  flex: 1;
  background: white;
  border-radius: 10px;
  overflow: hidden;
  height: 575px;

  .question-scroll-area {
    max-height: 600px;
    overflow-y: auto;
    padding: 0 20px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  .question-item {
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .question-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 0;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        color: var(--el-color-primary);
      }

      .question-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        flex: 1;
      }

      .expand-icon {
        width: 16px;
        height: 16px;
        color: #999;
        transition: transform 0.3s ease;
        margin-right: 2px;
      }
    }

    .question-answer {
      padding-bottom: 20px;
      animation: slideDown 0.3s ease;

      .answer-content {
        background-color: #f8f8f8;
        padding: 20px;
        border-radius: 10px;
        font-size: 14px;
        line-height: 1.6;
        color: #666;
        margin-bottom: 16px;
      }

      .helpful-section {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-left: 21px;

        .helpful-text {
          font-size: 12px;
          color: #999;
        }

        .helpful-buttons {
          display: flex;

          .el-button {
            padding: 6px 16px;
            font-size: 12px;
          }
        }
      }
    }
  }
}

// 动画效果
@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 200px;
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .content-container {
    flex-direction: column;
    gap: 20px;
  }

  .navigation {
    width: 100%;
    display: flex;
    overflow-x: auto;
    padding: 15px 0;
    height: 100%;

    .nav-item {
      white-space: nowrap;
      min-width: 120px;
      justify-content: center;
      border-left: none;
      border-bottom: 3px solid transparent;

      &.active {
        border-left: none;
      }
    }
  }

  .search-content {
    .search-title {
      font-size: 24px;
      margin-bottom: 30px;
    }

    .search-box {
      width: 500px;
    }
  }

  .question-list {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .search-section {
    height: 280px;
    padding: 0 20px;
  }

  .search-content {
    .search-title {
      font-size: 20px;
      margin-bottom: 24px;
    }

    .search-box {
      width: 100%;

      .search-input {
        :deep(.el-input__wrapper) {
          height: 44px;
        }
      }
    }
  }

  .content-section {
    padding: 40px 15px;
  }

  .question-scroll-area {
    max-height: 400px;
    padding: 15px;
  }
}
</style>
