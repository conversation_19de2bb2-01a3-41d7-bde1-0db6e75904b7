/**
 * 自动化构建和修复脚本
 * 1. 执行构建
 * 2. 修复HTML文件中的路径问题
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

function log(message, type = 'info') {
  const timestamp = new Date().toLocaleTimeString();
  const prefix = {
    info: '📋',
    success: '✅',
    error: '❌',
    warning: '⚠️'
  }[type] || '📋';
  
  console.log(`[${timestamp}] ${prefix} ${message}`);
}

function fixHtmlPaths(filePath) {
  if (!fs.existsSync(filePath)) {
    log(`文件不存在: ${filePath}`, 'warning');
    return;
  }

  log(`修复文件: ${filePath}`);

  let content = fs.readFileSync(filePath, 'utf8');

  // 修复 JavaScript 文件路径
  content = content.replace(/src="\/assets\//g, 'src="./assets/');

  // 修复 CSS 文件路径
  content = content.replace(/href="\/assets\//g, 'href="./assets/');

  // 修复 favicon 路径
  content = content.replace(/href="\/favicon\.ico"/g, 'href="./favicon.ico"');

  // 修复动态添加的localhost:8000链接
  content = content.replace(/http:\/\/localhost:8000\//g, './');

  // 修复canonical链接
  content = content.replace(/href="http:\/\/localhost:8000\/"/g, 'href="./"');

  // 修复其他绝对路径
  content = content.replace(/href="\//g, 'href="./');
  content = content.replace(/src="\//g, 'src="./');

  // 添加base标签来确保所有相对路径都正确解析
  if (!content.includes('<base')) {
    content = content.replace(
      /<head>/,
      '<head>\n    <base href="./">'
    );
  }

  fs.writeFileSync(filePath, content, 'utf8');
  log(`修复完成: ${filePath}`, 'success');
}

function main() {
  try {
    log('🚀 开始自动化构建流程...\n');
    
    // 1. 执行构建
    log('📦 开始构建项目...');
    execSync('npm run build', { stdio: 'inherit' });
    log('构建完成！', 'success');
    
    // 2. 修复HTML文件路径
    log('\n🔧 开始修复HTML文件路径...');
    const htmlFiles = [
      'hgw_ui/index.html',
      'hgw_ui/about/index.html'
    ];
    
    htmlFiles.forEach(fixHtmlPaths);
    
    log('\n✨ 构建和修复流程完成！', 'success');
    log('📁 构建输出目录: hgw_ui/');
    log('🌐 可以部署到任何静态文件服务器');
    log('🧪 本地测试: npx serve . -p 8080 然后访问 http://localhost:8080/hgw_ui/');
    
  } catch (error) {
    log(`构建失败: ${error.message}`, 'error');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixHtmlPaths };
