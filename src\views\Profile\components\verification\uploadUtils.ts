import { uploadApi } from "@/utils/api";

/**
 * 上传文件到OSS的通用函数
 * @param file 要上传的文件
 * @returns Promise<string> 返回上传后的文件URL
 */
export const uploadToOSS = async (file: File): Promise<string> => {
  try {
    // 获取OSS签名信息
    const ossPolicy = await uploadApi.getOssPolicy();

    if (ossPolicy.code !== 1) {
      throw new Error("获取OSS签名失败");
    }

    const { policy, signature, accessid, dir, host, expire } = ossPolicy.data;

    // 生成文件名
    const fileName = `${dir}${Date.now()}_${file.name}`;

    // 创建FormData
    const formData = new FormData();
    formData.append("key", fileName);
    formData.append("policy", policy);
    formData.append("OSSAccessKeyId", accessid);
    formData.append("host", host);
    formData.append("expire", expire);
    formData.append("signature", signature);
    formData.append("file", file);

    // 上传到OSS
    const response = await fetch(
      "https://huigupaimai.oss-cn-beijing.aliyuncs.com/",
      {
        method: "POST",
        body: formData,
      }
    );

    if (response.ok) {
      return `${fileName}`;
    } else {
      throw new Error("文件上传失败");
    }
  } catch (error) {
    console.error("上传失败:", error);
    throw error;
  }
};

/**
 * 验证文件类型和大小
 * @param file 要验证的文件
 * @param maxSize 最大文件大小（字节），默认5MB
 * @param allowedTypes 允许的文件类型，默认为图片类型
 * @returns boolean 验证是否通过
 */
export const validateFile = (
  file: File,
  maxSize: number = 5 * 1024 * 1024, // 5MB
  allowedTypes: string[] = ['image/gif', 'image/png', 'image/jpeg', 'image/jpg', 'image/bmp']
): boolean => {
  // 检查文件大小
  if (file.size > maxSize) {
    throw new Error(`文件大小不能超过${Math.round(maxSize / 1024 / 1024)}MB`);
  }

  // 检查文件类型
  if (!allowedTypes.includes(file.type)) {
    throw new Error('文件类型不支持，请上传gif、png、jpg、bmp格式的图片');
  }

  return true;
};