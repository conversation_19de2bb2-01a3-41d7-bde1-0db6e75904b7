/// <reference types="vite/client" />

/**
 * 环境变量类型定义
 * 为Vite环境变量提供TypeScript类型支持
 */
interface ImportMetaEnv {
  // 应用基本信息
  readonly VITE_APP_NAME: string
  readonly VITE_APP_VERSION: string
  
  // API配置
  readonly VITE_OLD_API_BASE_URL: string
  readonly VITE_NEW_API_BASE_URL: string
  readonly VITE_API_PREFIX: string
  readonly VITE_NEW_API_PREFIX: string
  
  // 服务器配置
  readonly VITE_HOST: string
  readonly VITE_PORT: string
  
  // 环境标识
  readonly NODE_ENV: 'development' | 'production' | 'test'
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// 全局常量类型定义（在vite.config.ts中定义的全局常量）
declare const __OLD_API_BASE_URL__: string
declare const __NEW_API_BASE_URL__: string
declare const __IS_DEVELOPMENT__: boolean