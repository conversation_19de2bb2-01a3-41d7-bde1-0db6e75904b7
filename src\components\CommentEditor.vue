<template>
  <div class="comment-editor">
    <!-- 富文本编辑器 -->
    <div class="editor-container">
      <div 
        ref="editorRef" 
        class="editor-content"
        :class="{ 'editor-focused': isFocused }"
      ></div>
      
      <!-- 工具栏 -->
      <div class="editor-toolbar">
        <div class="toolbar-left">
          <!-- 上传图片按钮 -->
          <input
            ref="fileInputRef"
            type="file"
            accept="image/*"
            multiple
            style="display: none"
            @change="handleImageUpload"
          />
          <button
            type="button"
            class="toolbar-btn"
            :disabled="uploading"
            @click="triggerImageUpload"
            :title="uploading ? '上传中...' : '上传图片'"
          >
            <span v-if="uploading">⏳</span>
            <span v-else>📷</span>
          </button>
        </div>
        
        <div class="toolbar-right">
          <button
            type="button"
            class="send-btn"
            :disabled="!canSend"
            @click="handleSend"
          >
            发送
          </button>
        </div>
      </div>
    </div>
    
    <!-- 图片预览区域 -->
    <div v-if="uploadedImages.length > 0" class="image-preview-container">
      <div class="image-list">
        <div
          v-for="(image, index) in uploadedImages"
          :key="index"
          class="image-item"
        >
          <el-image
            :src="image.url"
            :alt="`图片${index + 1}`"
            fit="cover"
            :preview-src-list="[image.url]"
            :initial-index="0"
            preview-teleported
            class="preview-image"
          />
          <button
            type="button"
            class="remove-btn"
            @click="removeImage(index)"
            title="删除图片"
          >
            ×
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import { Editor } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import { ElMessage } from 'element-plus'
import SvgIcon from '@/components/SvgIcon.vue'
import { newApi } from '@/utils/api-new'

interface UploadedImage {
  url: string
  file: File
  fileName: string
  filePath: string
  fileSize: number
}

const props = defineProps<{
  placeholder?: string
  maxLength?: number
}>()

const emit = defineEmits<{
  (e: 'send', content: { text: string; images: UploadedImage[]; attachmentList: any[] }): void
}>()

// 编辑器相关
const editorRef = ref<HTMLElement>()
const editor = ref<Editor>()
const isFocused = ref(false)

// 图片上传相关
const fileInputRef = ref<HTMLInputElement>()
const uploadedImages = ref<UploadedImage[]>([])
const uploading = ref(false)

// 计算属性
const canSend = computed(() => {
  if (!editor.value) return false
  const text = editor.value.getText().trim()
  return (text.length > 0 || uploadedImages.value.length > 0) && !uploading.value
})

// 初始化编辑器
const initEditor = () => {
  if (!editorRef.value) return

  editor.value = new Editor({
    element: editorRef.value,
    extensions: [
      StarterKit.configure({
        // 只保留基本功能
        heading: false,
        blockquote: false,
        codeBlock: false,
        horizontalRule: false,
      }),
    ],
    content: '',
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
        'data-placeholder': props.placeholder || '写下你的留言...',
      },
    },
    onFocus: () => {
      isFocused.value = true
    },
    onBlur: () => {
      isFocused.value = false
    },
  })
}

// 触发图片上传
const triggerImageUpload = () => {
  fileInputRef.value?.click()
}

// 处理图片上传
const handleImageUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files

  if (!files) return

  uploading.value = true

  try {
    for (const file of Array.from(files)) {
      if (file.type.startsWith('image/')) {
        // 检查文件大小（限制为5MB）
        if (file.size > 5 * 1024 * 1024) {
          ElMessage.error(`图片 ${file.name} 大小超过5MB，请选择更小的图片`)
          continue
        }

        // 创建FormData
        const formData = new FormData()
        formData.append('file', file)

        try {
          // 调用上传接口
          const response = await newApi.uploadImage(formData)

          if (response && response.success && response.message) {
            // 上传成功，添加到图片列表
            uploadedImages.value.push({
              url: response.message,
              file: file,
              fileName: file.name,
              filePath: response.message,
              fileSize: file.size
            })
          } else {
            ElMessage.error(`图片 ${file.name} 上传失败: ${response?.message || '未知错误'}`)
          }
        } catch (error) {
          console.error('图片上传失败:', error)
          ElMessage.error(`图片 ${file.name} 上传失败`)
        }
      } else {
        ElMessage.error(`文件 ${file.name} 不是有效的图片格式`)
      }
    }
  } finally {
    uploading.value = false
    // 清空input
    target.value = ''
  }
}

// 移除图片
const removeImage = (index: number) => {
  uploadedImages.value.splice(index, 1)
}

// 根据文件扩展名判断文件类型
const getFileTypeByExtension = (filePath: string): string => {
  if (!filePath) return 'other'

  const extension = filePath.toLowerCase().split('.').pop()

  switch (extension) {
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'bmp':
    case 'webp':
      return 'image'
    case 'mp4':
    case 'avi':
    case 'mov':
    case 'wmv':
    case 'flv':
    case 'webm':
      return 'video'
    case 'mp3':
    case 'wav':
    case 'flac':
    case 'aac':
      return 'mp3'
    case 'pdf':
      return 'pdf'
    case 'doc':
    case 'docx':
      return 'word'
    case 'xls':
    case 'xlsx':
      return 'excel'
    case 'ppt':
    case 'pptx':
      return 'ppt'
    case 'zip':
    case 'rar':
    case '7z':
      return 'zip'
    default:
      return 'other'
  }
}

// 发送留言
const handleSend = () => {
  if (!editor.value || !canSend.value) return

  const text = editor.value.getHTML()
  const images = [...uploadedImages.value]

  // 构造附件列表
  const attachmentList = images.map(image => ({
    bizType: 'RECEIVE', // 留言业务类型
    fileName: image.fileName,
    filePath: image.filePath,
    fileSize: image.fileSize,
    fileType: getFileTypeByExtension(image.filePath)
  }))

  emit('send', { text, images, attachmentList })

  // 清空编辑器和图片
  editor.value.commands.clearContent()
  uploadedImages.value = []
}

// 清空内容
const clearContent = () => {
  if (editor.value) {
    editor.value.commands.clearContent()
  }
  uploadedImages.value = []
}

// 暴露方法
defineExpose({
  clearContent
})

onMounted(() => {
  initEditor()
})

onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
  }
})
</script>

<style scoped lang="scss">
.comment-editor {
  width: 100%;

  .editor-container {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fff;
    transition: border-color 0.3s ease;
    
    &:hover {
      border-color: #c0c0c0;
    }
  }
  
  .editor-content {
    min-height: 80px;
    max-height: 200px;
    overflow-y: auto;
    padding: 12px 16px;
    
    &.editor-focused {
      .editor-container {
        border-color: #1890ff;
      }
    }
    
    :deep(.ProseMirror) {
      outline: none;
      font-size: 14px;
      line-height: 1.5;
      color: #333;
      
      p {
        margin: 0;
        
        &.is-editor-empty:first-child::before {
          content: attr(data-placeholder);
          float: left;
          color: #aaa;
          pointer-events: none;
          height: 0;
        }
      }
    }
  }
  
  .editor-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
    border-radius: 0 0 8px 8px;
    
    .toolbar-left {
      display: flex;
      gap: 8px;
    }
    
    .toolbar-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border: none;
      background: transparent;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover:not(:disabled) {
        background: #e6f7ff;
      }

      &:disabled {
        cursor: not-allowed;
        opacity: 0.6;
      }

      .btn-icon {
        width: 16px;
        height: 16px;
        color: #666;
      }
    }
    
    .send-btn {
      padding: 6px 16px;
      background: #1890ff;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      transition: background-color 0.2s ease;
      
      &:hover:not(:disabled) {
        background: #40a9ff;
      }
      
      &:disabled {
        background: #d9d9d9;
        cursor: not-allowed;
      }
    }
  }
  
  .image-preview-container {
    margin-top: 12px;
    
    .image-list {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
    
    .image-item {
      position: relative;
      width: 80px;
      height: 80px;
      border-radius: 4px;
      overflow: hidden;
      border: 1px solid #e0e0e0;

      .preview-image {
        width: 100%;
        height: 100%;
        cursor: pointer;
        transition: transform 0.2s ease;

        &:hover {
          transform: scale(1.05);
        }

        :deep(.el-image__inner) {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      
      .remove-btn {
        position: absolute;
        top: -4px;
        right: -4px;
        width: 20px;
        height: 20px;
        background: #ff4d4f;
        color: white;
        border: none;
        border-radius: 50%;
        font-size: 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &:hover {
          background: #ff7875;
        }
      }
    }
  }
}
</style>
