<template>
  <div class="step-panel">
    <!-- 使用ElementPlus表单布局 -->
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-position="left"
      label-width="80px"
      :scroll-to-error="true"
    >
      <!-- 求购物资类型 -->
      <div class="form-section">
        <h3 class="section-title">求购物资类型</h3>
        <div class="form-row">
          <el-form-item prop="materialType" class="material-type-item no-label">
            <el-cascader
              v-model="formData.materialType"
              :options="materialTypeOptions"
              :props="cascaderProps"
              placeholder="请选择物资类型"
              size="large"
              filterable
              clearable
              class="material-type-cascader"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 基本求购信息 -->
      <div class="form-section">
        <h3 class="section-title">基本求购信息</h3>

        <!-- 信息标题独占一行 -->
        <div class="form-row">
          <el-form-item
            label="信息标题"
            prop="basicInfo.infoTitle"
            class="info-title-item"
          >
            <el-input
              v-model="formData.basicInfo.infoTitle"
              placeholder="格式建议:地区+求购+设备名称"
              size="large"
            />
          </el-form-item>
        </div>

        <!-- 物资品牌、物资型号、新旧程度三项在一行 -->
        <div class="form-row basic-three-row">
          <el-form-item
            label="物资品牌"
            prop="basicInfo.brand"
            class="basic-three-item"
          >
            <el-input
              v-model="formData.basicInfo.brand"
              placeholder="请输入物资品牌"
              size="large"
            />
          </el-form-item>
          <el-form-item
            label="物资型号"
            prop="basicInfo.model"
            class="basic-three-item"
          >
            <el-input
              v-model="formData.basicInfo.model"
              placeholder="请输入物资型号"
              size="large"
            />
          </el-form-item>
          <el-form-item
            label="新旧程度"
            prop="basicInfo.depreciationDegree"
            class="basic-three-item"
          >
            <el-select
              v-model="formData.basicInfo.depreciationDegree"
              placeholder="请选择新旧程度"
              size="large"
            >
              <el-option :value="1" label="一成新" />
              <el-option :value="2" label="二成新" />
              <el-option :value="3" label="三成新" />
              <el-option :value="4" label="四成新" />
              <el-option :value="5" label="五成新" />
              <el-option :value="6" label="六成新" />
              <el-option :value="7" label="七成新" />
              <el-option :value="8" label="八成新" />
              <el-option :value="9" label="九成新" />
            </el-select>
          </el-form-item>
        </div>
      </div>

      <!-- 规格与存放 -->
      <div class="form-section">
        <h3 class="section-title">规格与存放</h3>

        <!-- 省市区选择框第一行 -->
        <div class="form-row location-row">
          <el-form-item
            prop="specification.province"
            class="location-item no-label"
          >
            <el-select
              v-model="formData.specification.province"
              placeholder="请选择省份"
              size="large"
              @change="handleProvinceChange"
            >
              <el-option
                v-for="province in provinceOptions"
                :key="province.code"
                :value="province.code"
                :label="province.name"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            prop="specification.city"
            class="location-item no-label"
          >
            <el-select
              v-model="formData.specification.city"
              placeholder="请选择城市"
              size="large"
              @change="handleCityChange"
            >
              <el-option
                v-for="city in cityOptions"
                :key="city.code"
                :value="city.code"
                :label="city.name"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            prop="specification.area"
            class="location-item no-label"
          >
            <el-select
              v-model="formData.specification.area"
              placeholder="请选择区县"
              size="large"
            >
              <el-option
                v-for="area in areaOptions"
                :key="area.code"
                :value="area.code"
                :label="area.name"
              />
            </el-select>
          </el-form-item>
        </div>

        <!-- 第二行：存放方式和物资数量 -->
        <div class="form-row specification-second-row">
          <!-- 存放方式 -->
          <el-form-item
            label="存放方式"
            prop="specification.storageMethod"
            class="specification-item"
          >
            <el-select
              v-model="formData.specification.storageMethod"
              placeholder="请选择存放方式"
              size="large"
            >
              <el-option :value="1" label="仓库" />
              <el-option :value="2" label="露天堆放" />
            </el-select>
          </el-form-item>

          <!-- 物资数量 -->
          <el-form-item
            label="物资数量"
            prop="specification.quantity"
            class="specification-item"
          >
            <el-input-number
              v-model="formData.specification.quantity"
              placeholder="请输入数量"
              style="width: 100%"
              size="large"
              :min="1"
            />
          </el-form-item>
        </div>

        <!-- 第三行：物资单位和物资价格 -->
        <div class="form-row specification-third-row">
          <!-- 物资单位 -->
          <el-form-item
            label="物资单位"
            prop="specification.unit"
            class="specification-item"
          >
            <!-- <el-select
              v-model="formData.specification.unit"
              placeholder="请选择单位"
              size="large"
            >
              <el-option value="台" label="台" />
              <el-option value="辆" label="辆" />
              <el-option value="套" label="套" />
              <el-option value="个" label="个" />
              <el-option value="件" label="件" />
              <el-option value="批" label="批" />
              <el-option value="米" label="米" />
              <el-option value="吨" label="吨" />
              <el-option value="公斤" label="公斤" />
            </el-select> -->
            <el-input
              v-model="formData.specification.unit"
              placeholder="请输入单位"
              size="large"
            />
          </el-form-item>

          <!-- 物资价格 -->
          <el-form-item
            label="物资价格"
            prop="specification.price"
            class="specification-item"
          >
            <el-input-number
              v-model="formData.specification.price"
              placeholder="请输入价格"
              style="width: 100%"
              size="large"
              :min="0"
              :precision="2"
              controls-position="right"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 亮点与展示 -->
      <div class="form-section">
        <h3 class="section-title">亮点与展示</h3>

        <!-- 物资照片上传 -->
        <div class="form-row">
          <el-form-item
            label="物资照片"
            prop="display.images"
            class="upload-item"
          >
            <FileUpload
              v-model="imageFileList"
              type="image"
              :limit="15"
              tip="建议尺寸800*800像素，最多上传15张"
              @change="handleImageChange"
            />
          </el-form-item>
        </div>

        <!-- 物资视频上传 -->
        <div class="form-row">
          <el-form-item
            label="物资视频"
            prop="display.videos"
            class="upload-item"
          >
            <FileUpload
              v-model="videoFileList"
              type="video"
              :limit="1"
              tip="建议视频宽高比16:9，突出商品核心卖点，时长9~30秒"
              @change="handleVideoChange"
            />
          </el-form-item>
        </div>

        <!-- 求购亮点 -->
        <div class="form-row">
          <el-form-item
            label="求购亮点"
            prop="display.highlights"
            class="highlights-item"
          >
            <el-input
              v-model="formData.display.highlights"
              placeholder="请输入求购亮点"
              size="large"
            />
          </el-form-item>
        </div>

        <!-- 物资详细描述 -->
        <div class="form-row">
          <el-form-item
            label="详细描述"
            prop="display.materialDesc"
            class="material-desc-item"
          >
            <div class="rich-text-container">
              <TiptapEditor
                v-model="formData.display.materialDesc"
                placeholder="请详细描述物资信息..."
                :auto-focus="false"
              />
            </div>
          </el-form-item>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch } from "vue";
import type { FormInstance, UploadFile } from "element-plus";
import TiptapEditor from "@/components/TiptapEditor/index.vue";
import FileUpload from "@/components/FileUpload/index.vue";
import { convertJsonToFiles } from "@/components/FileUpload/utils";
import { supplyDemandApi } from "@/utils/api-new";
import type { MaterialTypeNode } from "@/utils/api-new";
import areaData from "@/assets/js/area.js";

// 定义 Props
interface Props {
  modelValue: {
    materialType: string;
    basicInfo: {
      infoTitle: string;
      brand: string;
      model: string;
      depreciationDegree: number;
    };
    specification: {
      province: string;
      city: string;
      area: string;
      storageMethod: number;
      quantity: number | null;
      unit: string;
      price: number | null;
    };
    display: {
      images: string;
      videos: string;
      highlights: string;
      materialDesc: string;
    };
    attachmentList: any[];
  };
  locationLoading?: boolean;
  isEditMode?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  locationLoading: false,
  isEditMode: false,
});

// 定义 Emits
interface Emits {
  (e: "update:modelValue", value: Props["modelValue"]): void;
  (e: "area-change", value: any): void;
  (e: "get-current-location"): void;
}

const emit = defineEmits<Emits>();

// 表单引用
const formRef = ref<FormInstance>();

// 物资类型选项
const materialTypeOptions = ref<MaterialTypeNode[]>([]);

// 省市区选项
const provinceOptions = ref<any[]>([]);
const cityOptions = ref<any[]>([]);
const areaOptions = ref<any[]>([]);

// 文件列表
const imageFileList = ref<UploadFile[]>([]);
const videoFileList = ref<UploadFile[]>([]);

// 标志变量，防止循环更新
const isUpdatingFromFileChange = ref(false);

// 计算属性：表单数据
const formData = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

// 级联选择器配置
const cascaderProps = {
  value: "id",
  label: "name",
  children: "children",
  checkStrictly: false,
  emitPath: false,
};

// 表单校验规则
const formRules = {
  materialType: [
    { required: true, message: "请选择物资类型", trigger: "change" },
  ],
  "basicInfo.infoTitle": [
    { required: true, message: "请输入信息标题", trigger: "blur" },
    {
      min: 2,
      max: 100,
      message: "信息标题长度应在2-100个字符之间",
      trigger: "blur",
    },
  ],
  "basicInfo.depreciationDegree": [
    { required: true, message: "请选择新旧程度", trigger: "change" },
  ],
  "specification.province": [
    { required: true, message: "请选择省份", trigger: "change" },
  ],
  "specification.storageMethod": [
    { required: true, message: "请选择存放方式", trigger: "change" },
  ],
  "specification.quantity": [
    { required: true, message: "请输入物资数量", trigger: "blur" },
  ],
  "specification.unit": [
    { required: true, message: "请选择物资单位", trigger: "change" },
  ],
  "specification.price": [
    { required: true, message: "请输入物资价格", trigger: "blur" },
  ],
};

// 获取物资类型树形数据
const fetchMaterialTypeTree = async () => {
  try {
    const result = await supplyDemandApi.getMaterialTree();
    console.log("获取物资类型树形数据:", result);
    if (result && result.success && Array.isArray(result.result)) {
      materialTypeOptions.value = result.result;
    }
  } catch (error) {
    console.error("获取物资类型失败:", error);
  }
};

// 处理省份变化
const handleProvinceChange = (provinceCode: string) => {
  // 清空城市和区县
  formData.value.specification.city = "";
  formData.value.specification.area = "";
  cityOptions.value = [];
  areaOptions.value = [];

  // 根据省份代码获取城市数据
  const cities = [];
  const cityList = areaData.area.city_list;

  for (const [cityCode, cityName] of Object.entries(cityList)) {
    // 城市代码前4位与省份代码前4位相同
    if (cityCode.substring(0, 2) === provinceCode.substring(0, 2)) {
      cities.push({
        code: cityCode,
        name: cityName,
      });
    }
  }

  cityOptions.value = cities;
};

// 处理城市变化
const handleCityChange = (cityCode: string) => {
  // 清空区县
  formData.value.specification.area = "";
  areaOptions.value = [];

  // 根据城市代码获取区县数据
  const areas = [];
  const areaList = areaData.area.county_list;

  for (const [areaCode, areaName] of Object.entries(areaList)) {
    // 区县代码前4位与城市代码前4位相同
    if (areaCode.substring(0, 4) === cityCode.substring(0, 4)) {
      areas.push({
        code: areaCode,
        name: areaName,
      });
    }
  }

  areaOptions.value = areas;
};





// 图片文件变化处理
const handleImageChange = (files: UploadFile[]) => {
  console.log('图片文件变化:', files);

  isUpdatingFromFileChange.value = true;

  const imageData = files
    .filter(file => file.url && !file.url.startsWith('blob:')) // 过滤掉blob URL，只保留服务器返回的URL
    .map(file => ({
      fileName: file.name,
      filePath: file.url!,
      fileSize: file.size || 0,
    }));

  console.log('处理后的图片数据:', imageData);
  formData.value.display.images = JSON.stringify(imageData);

  // 延迟重置标志，确保watch不会立即触发
  setTimeout(() => {
    isUpdatingFromFileChange.value = false;
  }, 100);
};

// 视频文件变化处理
const handleVideoChange = (files: UploadFile[]) => {
  console.log('视频文件变化:', files);

  isUpdatingFromFileChange.value = true;

  const videoData = files
    .filter(file => file.url && !file.url.startsWith('blob:')) // 过滤掉blob URL，只保留服务器返回的URL
    .map(file => ({
      fileName: file.name,
      filePath: file.url!,
      fileSize: file.size || 0,
    }));

  console.log('处理后的视频数据:', videoData);
  formData.value.display.videos = JSON.stringify(videoData);

  // 延迟重置标志，确保watch不会立即触发
  setTimeout(() => {
    isUpdatingFromFileChange.value = false;
  }, 100);
};

// 表单验证方法
const validateForm = async (): Promise<boolean> => {
  try {
    await formRef.value?.validate();
    return true;
  } catch (error) {
    console.error("表单验证失败:", error);
    return false;
  }
};

// 清除表单验证
const clearValidate = () => {
  formRef.value?.clearValidate();
};

// 组件挂载时获取物资类型数据
onMounted(() => {
  fetchMaterialTypeTree();

  // 初始化省份数据
  const provinces = [];
  const provinceList = areaData.area.province_list;

  for (const [provinceCode, provinceName] of Object.entries(provinceList)) {
    provinces.push({
      code: provinceCode,
      name: provinceName,
    });
  }

  provinceOptions.value = provinces;
});

// 初始化文件列表
const initializeFileLists = () => {
  // 初始化图片列表
  if (props.modelValue.display.images) {
    try {
      const imageFiles = convertJsonToFiles(props.modelValue.display.images);
      imageFileList.value = imageFiles;
      console.log('初始化图片列表:', imageFiles);
    } catch (error) {
      console.error('解析图片数据失败:', error);
      imageFileList.value = [];
    }
  } else {
    imageFileList.value = [];
  }

  // 初始化视频列表
  if (props.modelValue.display.videos) {
    try {
      const videoFiles = convertJsonToFiles(props.modelValue.display.videos);
      videoFileList.value = videoFiles;
      console.log('初始化视频列表:', videoFiles);
    } catch (error) {
      console.error('解析视频数据失败:', error);
      videoFileList.value = [];
    }
  } else {
    videoFileList.value = [];
  }
};

// 监听表单数据变化，用于数据回显
watch(() => [props.modelValue.display.images, props.modelValue.display.videos], () => {
  if (!isUpdatingFromFileChange.value) {
    initializeFileLists();
  }
}, { immediate: true });

// 暴露方法给父组件
defineExpose({
  validateForm,
  clearValidate,
});
</script>

<style scoped lang="scss">
.step-panel {
  background: #fff;
  border-radius: 8px;
}

.form-section {
  margin-bottom: 32px;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
  padding-bottom: 8px;

  &::before {
    content: "";
    display: block;
    width: 4px;
    height: 18px;
    margin-right: 8px;
    background-color: #004c66;
  }
}

/* 表单行布局 */
.form-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16px;
  gap: 20px;
}

/* 没有label的表单项 */
.no-label {
  :deep(.el-form-item__label) {
    display: none !important;
  }

  :deep(.el-form-item__content) {
    margin-left: 0 !important;
  }
}

/* 物资类型选择 - 与省份选择器一样宽度 */
.material-type-item {
  flex: 1;
  min-width: calc(33.333% - 14px);
  :deep(.material-type-cascader){
    width: calc(33.333% - 14px);
  }
}

/* 信息标题 - 占满一行 */
.info-title-item {
  width: 100%;
}

/* 基本信息三项在一行 */
.basic-three-row {
  gap: 20px;
}

.basic-three-item {
  flex: 1;
  min-width: calc(33.333% - 14px);
}

/* 省市区选择行 */
.location-row {
  gap: 20px;
  align-items: flex-start;
}

.location-item {
  flex: 1;
  min-width: calc(33.333% - 14px);
}

/* 规格信息第二行和第三行 */
.specification-second-row,
.specification-third-row {
  gap: 20px;
}

.specification-item {
  flex: 1;
  min-width: calc(50% - 10px);
}

/* 上传相关样式 */
.upload-item {
  width: 100%;
}

.upload-container {
  width: 100%;

  .upload-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #999;
    line-height: 1.4;
  }
}

/* 求购亮点 */
.highlights-item {
  width: 100%;
}

/* 物资详细描述 */
.material-desc-item {
  width: 100%;
}

.rich-text-container {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .basic-three-item,
  .specification-item,
  .location-item {
    min-width: calc(50% - 10px);
  }
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 16px;
  }

  .basic-three-item,
  .specification-item,
  .material-type-item,
  .location-item {
    width: 100%;
    min-width: auto;
  }
}
</style>
