/**
 * 加密解密工具函数
 * 用于安全地存储敏感信息到本地存储
 */

/**
 * 简单的Base64编码加密（仅用于演示，生产环境建议使用更强的加密算法）
 * @param text 要加密的文本
 * @returns 加密后的字符串
 */
export function encryptPassword(text: string): string {
  try {
    // 使用Base64编码 + 简单的字符偏移
    const encoded = btoa(unescape(encodeURIComponent(text)))
    // 添加简单的字符偏移混淆
    const shifted = encoded.split('').map(char => {
      const code = char.charCodeAt(0)
      return String.fromCharCode(code + 3)
    }).join('')
    
    return shifted
  } catch (error) {
    console.error('密码加密失败:', error)
    return text // 加密失败时返回原文本
  }
}

/**
 * 简单的Base64解码解密
 * @param encryptedText 加密的文本
 * @returns 解密后的字符串
 */
export function decryptPassword(encryptedText: string): string {
  try {
    // 还原字符偏移
    const restored = encryptedText.split('').map(char => {
      const code = char.charCodeAt(0)
      return String.fromCharCode(code - 3)
    }).join('')
    
    // Base64解码
    const decoded = decodeURIComponent(escape(atob(restored)))
    return decoded
  } catch (error) {
    console.error('密码解密失败:', error)
    return '' // 解密失败时返回空字符串
  }
}

/**
 * 安全地保存密码到本地存储
 * @param key 存储键名
 * @param password 密码
 */
export function saveEncryptedPassword(key: string, password: string): void {
  try {
    const encrypted = encryptPassword(password)
    localStorage.setItem(key, encrypted)
  } catch (error) {
    console.error('保存加密密码失败:', error)
  }
}

/**
 * 安全地从本地存储读取密码
 * @param key 存储键名
 * @returns 解密后的密码
 */
export function getDecryptedPassword(key: string): string {
  try {
    const encrypted = localStorage.getItem(key)
    if (!encrypted) return ''
    
    return decryptPassword(encrypted)
  } catch (error) {
    console.error('读取加密密码失败:', error)
    return ''
  }
}

/**
 * 清除存储的加密密码
 * @param key 存储键名
 */
export function removeEncryptedPassword(key: string): void {
  try {
    localStorage.removeItem(key)
  } catch (error) {
    console.error('清除加密密码失败:', error)
  }
}