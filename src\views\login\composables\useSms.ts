import { ref } from 'vue'
import { ElMessage } from "element-plus";
import { authApi } from '@/utils/api'

/**
 * 短信验证码相关的组合式函数
 * 提供发送验证码和倒计时功能
 */
export function useSms() {
  // 短信验证码倒计时
  const smsCountdown = ref(0)
  
  // 倒计时定时器
  let countdownTimer: number | null = null

  /**
   * 发送短信验证码
   * @param phone 手机号
   * @returns Promise<boolean> 发送是否成功
   */
  const sendSms = async (phone: string): Promise<boolean> => {
    // 验证手机号格式
    if (!phone) {
      ElMessage.warning('请先输入手机号')
      return false
    }

    if (!/^1[3-9]\d{9}$/.test(phone)) {
      ElMessage.warning('请输入正确的手机号')
      return false
    }

    // 检查是否在倒计时中
    if (smsCountdown.value > 0) {
      ElMessage.warning('请等待倒计时结束后再发送')
      return false
    }

    try {
      // 调用发送短信验证码API
      const response = await authApi.sendSms({ mobile: phone })
      
      if (response.code === 1) {
        // 发送成功，开始倒计时
        startCountdown()
        ElMessage.success('验证码已发送')
        return true
      } else {
        ElMessage.error(response.msg || '发送验证码失败')
        return false
      }
    } catch (error) {
      console.error('发送验证码失败:', error)
      ElMessage.error('发送验证码失败，请重试')
      return false
    }
  }

  /**
   * 开始倒计时
   * @param seconds 倒计时秒数，默认60秒
   */
  const startCountdown = (seconds: number = 60) => {
    // 清除之前的定时器
    if (countdownTimer) {
      clearInterval(countdownTimer)
    }

    smsCountdown.value = seconds
    countdownTimer = setInterval(() => {
      smsCountdown.value--
      if (smsCountdown.value <= 0) {
        clearInterval(countdownTimer!)
        countdownTimer = null
      }
    }, 1000)
  }

  /**
   * 清除倒计时
   */
  const clearCountdown = () => {
    if (countdownTimer) {
      clearInterval(countdownTimer)
      countdownTimer = null
    }
    smsCountdown.value = 0
  }

  /**
   * 获取验证码按钮文本
   */
  const getSmsButtonText = () => {
    return smsCountdown.value > 0 ? `${smsCountdown.value}s后重发` : '获取验证码'
  }

  /**
   * 验证码按钮是否禁用
   */
  const isSmsButtonDisabled = () => {
    return smsCountdown.value > 0
  }

  return {
    smsCountdown,
    sendSms,
    startCountdown,
    clearCountdown,
    getSmsButtonText,
    isSmsButtonDisabled
  }
}