import type { UploadFile } from 'element-plus'
import type { FileData } from './types'

/**
 * 将UploadFile数组转换为FileData数组
 * @param files UploadFile数组
 * @returns FileData数组
 */
export const convertFilesToData = (files: UploadFile[]): FileData[] => {
  return files
    .filter(file => file.url) // 只处理有URL的文件
    .map(file => ({
      fileName: file.name,
      filePath: file.url!,
      fileSize: file.size || 0,
    }))
}

/**
 * 将FileData数组转换为JSON字符串
 * @param files UploadFile数组
 * @returns JSON字符串
 */
export const convertFilesToJson = (files: UploadFile[]): string => {
  const fileData = convertFilesToData(files)
  return JSON.stringify(fileData)
}

/**
 * 将JSON字符串转换为UploadFile数组
 * @param jsonStr JSON字符串
 * @returns UploadFile数组
 */
export const convertJsonToFiles = (jsonStr: string): UploadFile[] => {
  try {
    if (!jsonStr) return []
    
    const fileData: FileData[] = JSON.parse(jsonStr)
    return fileData.map((data, index) => ({
      name: data.fileName,
      url: data.filePath,
      size: data.fileSize,
      status: 'success' as const,
      uid: Date.now() + index
    }))
  } catch (error) {
    console.error('解析文件JSON失败:', error)
    return []
  }
}

/**
 * 验证文件类型
 * @param file 文件对象
 * @param allowedTypes 允许的文件类型数组
 * @returns 是否通过验证
 */
export const validateFileType = (file: File, allowedTypes: string[]): boolean => {
  return allowedTypes.includes(file.type)
}

/**
 * 验证文件大小
 * @param file 文件对象
 * @param maxSize 最大文件大小（字节）
 * @returns 是否通过验证
 */
export const validateFileSize = (file: File, maxSize: number): boolean => {
  return file.size <= maxSize
}

/**
 * 格式化文件大小
 * @param size 文件大小（字节）
 * @returns 格式化后的文件大小字符串
 */
export const formatFileSize = (size: number): string => {
  if (size === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(size) / Math.log(k))
  
  return parseFloat((size / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 获取文件扩展名
 * @param fileName 文件名
 * @returns 文件扩展名
 */
export const getFileExtension = (fileName: string): string => {
  return fileName.slice((fileName.lastIndexOf('.') - 1 >>> 0) + 2)
}

/**
 * 根据文件类型获取默认配置
 * @param type 文件上传类型
 * @returns 默认配置对象
 */
export const getDefaultConfig = (type: 'image' | 'video' | 'other') => {
  const configs = {
    image: {
      accept: 'image/*',
      limit: 15,
      tip: '建议尺寸800*800像素，支持jpg、png、gif、bmp格式',
      maxSize: 10 * 1024 * 1024 // 10MB
    },
    video: {
      accept: 'video/*',
      limit: 1,
      tip: '建议视频宽高比16:9，突出商品核心卖点，时长9~30秒',
      maxSize: 100 * 1024 * 1024 // 100MB
    },
    other: {
      accept: '*',
      limit: 10,
      tip: '支持常见文件格式，单个文件不超过50MB',
      maxSize: 50 * 1024 * 1024 // 50MB
    }
  }
  
  return configs[type]
}
