<template>
  <div class="outbox-container">
    <DataTable
      :config="tableConfig"
      :search-loading="searchLoading"
      @on-filter-change="handleFilterChange"
      @on-page-change="handlePageChange"
      @on-action-click="handleActionClick"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import DataTable from '@/components/DataTable/index.vue'
import type { TableConfig } from '@/types/table'
import { newApi } from '@/utils/api-new'

// 表格配置
const tableConfig = ref<TableConfig>({
  // 不显示按钮筛选栏
  buttonBar: {
    show: false,
    buttons: []
  },

  // 筛选栏配置
  filterBar: {
    show: true,
    filters: [
      {
        key: 'keywords',
        label: '关键词',
        type: 'input',
        placeholder: '请输入关键词搜索',
        width: '200px'
      }
    ]
  },

  // 不显示水平导航栏
  tabBar: {
    show: false,
    activeKey: '',
    tabs: []
  },

  // 表格列配置
  columns: [
    {
      key: 'index',
      label: '序号',
      width: '60px',
      align: 'center'
    },
    {
      key: 'receUserName',
      label: '接收者',
      width: '120px',
      align: 'center'
    },
    {
      key: 'projectName',
      label: '关联产品信息',
      width: '200px',
      align: 'left',
    },
    {
      key: 'message',
      label: '留言内容',
      width: '300px',
      align: 'left'
    },
    {
      key: 'sendStatus',
      label: '发送状态',
      width: '120px',
      align: 'center',
      type: 'tag',
      formatter: () => '发送成功'
    },
    {
      key: 'createTime',
      label: '发送时间',
      width: '180px',
      align: 'center',
      type: 'date'
    },
    {
      key: 'actions',
      label: '操作',
      width: '100px',
      align: 'center',
      type: 'action',
      actions: [
        {
          key: 'delete',
          label: '删除',
          type: 'danger',
          size: 'small'
        }
      ]
    }
  ],

  // 分页配置
  pagination: {
    show: true,
    current: 1,
    pageSize: 10,
    total: 0
  },

  // 数据
  data: [],
  loading: true,

  // 表格样式配置
  tableStyle: {
    maxHeight: 420,
    border: false,
    stripe: true,
    showSelection: false
  }
})

// 加载状态
const searchLoading = ref(false)

// 查询参数
const queryParams = reactive({
  keywords: '',
  page: 1,
  pageSize: 10,
  total: 0
})

// 获取当前用户ID
const getCurrentUserId = () => {
  const freedomUserInfo = JSON.parse(localStorage.getItem('freedomUserInfo') || '{}')
  return freedomUserInfo.userInfo?.id
}

// 事件处理函数
const handleFilterChange = (filters: Record<string, any>) => {
  console.log('筛选变化:', filters)
  Object.assign(queryParams, filters)
  queryParams.page = 1
  loadData(true)
}

const handlePageChange = (page: number, pageSize: number) => {
  console.log('分页变化:', page, pageSize)
  queryParams.page = page
  queryParams.pageSize = pageSize
  loadData()
}

// 统一的操作按钮处理
const handleActionClick = (actionKey: string, row: any) => {
  switch (actionKey) {
    case 'delete':
      handleDeleteMessage(row)
      break
    default:
      console.log('未知操作:', actionKey, row)
  }
}

const handleDeleteMessage = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条消息吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await newApi.deleteMessage(row.id)

    if (response.success) {
      ElMessage.success('删除成功')
      loadData()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败，请稍后重试')
    }
  }
}

// 加载数据
const loadData = async (showSearchLoading = false) => {
  tableConfig.value.loading = true

  if (showSearchLoading) {
    searchLoading.value = true
  }

  try {
    const currentUserId = getCurrentUserId()
    if (!currentUserId) {
      ElMessage.error('请先登录')
      return
    }

    const apiParams: any = {
      pageNo: queryParams.page,
      pageSize: queryParams.pageSize,
      sendUserId: currentUserId
    }

    if (queryParams.keywords) {
      apiParams.keywords = queryParams.keywords
    }

    const response = await newApi.getSentMessages(apiParams)

    if (response.success && response.result) {
      const { records, total } = response.result

      const processedData = records.map((item: any, index: number) => ({
        ...item,
        index: (queryParams.page - 1) * queryParams.pageSize + index + 1
      }))

      tableConfig.value.data = processedData

      if (tableConfig.value.pagination) {
        tableConfig.value.pagination.current = queryParams.page
        tableConfig.value.pagination.pageSize = queryParams.pageSize
        tableConfig.value.pagination.total = total || 0
      }

      queryParams.total = total || 0
    } else {
      ElMessage.error(response.message || '获取数据失败')
      tableConfig.value.data = []
      if (tableConfig.value.pagination) {
        tableConfig.value.pagination.total = 0
      }
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败，请稍后重试')
    tableConfig.value.data = []
    if (tableConfig.value.pagination) {
      tableConfig.value.pagination.total = 0
    }
  } finally {
    tableConfig.value.loading = false
    searchLoading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
.outbox-container {
  height: 100%;
}
</style>