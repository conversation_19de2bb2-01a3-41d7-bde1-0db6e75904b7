<template>
  <div class="file-upload-wrapper">
    <!-- 图片上传 -->
    <template v-if="type === 'image'">
      <el-upload
        v-model:file-list="fileList"
        :action="uploadAction"
        list-type="picture-card"
        :auto-upload="true"
        :limit="limit"
        :accept="accept"
        :headers="uploadHeaders"
        :on-success="handleSuccess"
        :on-error="handleError"
        :on-remove="handleRemove"
        :on-preview="handlePreview"
        class="upload-image"
      >
        <el-icon class="upload-icon"><Plus /></el-icon>
      </el-upload>
    </template>

    <!-- 视频上传 -->
    <template v-else-if="type === 'video'">
      <div class="video-upload-container">
        <!-- 上传区域 -->
        <el-upload
          v-model:file-list="internalFileList"
          :action="uploadAction"
          :auto-upload="true"
          :limit="1"
          :accept="accept"
          :headers="uploadHeaders"
          :on-success="handleSuccess"
          :on-error="handleError"
          :show-file-list="false"
          :before-upload="handleBeforeVideoUpload"
          class="upload-video"
        >
          <el-button type="primary">
            <el-icon><VideoPlay /></el-icon>
            视频上传
          </el-button>
        </el-upload>

        <!-- 自定义视频列表 -->
        <div v-if="fileList.length > 0" class="video-list">
          <div
            v-for="file in fileList"
            :key="file.uid"
            class="video-item"
          >
            <div class="video-preview" @click="handlePreview(file)">
              <el-icon class="video-icon"><VideoPlay /></el-icon>
              <div class="video-name">{{ file.name }}</div>
            </div>
            <div class="video-actions">
              <el-button
                type="text"
                size="small"
                @click="handlePreview(file)"
              >
                预览
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="handleRemove(file)"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- 其他文件上传 -->
    <template v-else>
      <el-upload
        v-model:file-list="fileList"
        :action="uploadAction"
        :auto-upload="true"
        :limit="limit"
        :accept="accept"
        :headers="uploadHeaders"
        :on-success="handleSuccess"
        :on-error="handleError"
        :on-remove="handleRemove"
        :on-preview="handlePreview"
        class="upload-other"
      >
        <el-button type="primary">
          <el-icon><Upload /></el-icon>
          {{ tip || '点击上传文件' }}
        </el-button>
      </el-upload>
    </template>

    <!-- 上传提示 -->
    <div v-if="tip" class="upload-tip">{{ tip }}</div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { Plus, VideoPlay, Upload } from '@element-plus/icons-vue'
import type { UploadFile } from 'element-plus'
import { ElMessage } from 'element-plus'

// 定义组件名称
defineOptions({
  name: 'FileUpload'
})

// 定义Props
interface Props {
  modelValue?: UploadFile[]  // v-model绑定的文件列表
  type?: 'image' | 'video' | 'other'  // 上传类型
  limit?: number  // 文件数量限制
  tip?: string  // 上传提示文字
  accept?: string  // 接受的文件类型
  disabled?: boolean  // 是否禁用
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  type: 'image',
  limit: 10,
  tip: '',
  accept: '',
  disabled: false
})

// 定义Emits
interface Emits {
  (e: 'update:modelValue', value: UploadFile[]): void
  (e: 'change', value: UploadFile[]): void
  (e: 'success', response: any, file: UploadFile): void
  (e: 'error', error: any, file: UploadFile): void
}

const emit = defineEmits<Emits>()

// 内部文件列表
const fileList = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
    emit('change', value)
  }
})

// 视频上传使用的内部文件列表（不显示默认列表）
const internalFileList = ref<UploadFile[]>([])

// 上传配置
const uploadAction = computed(() => {
  const baseURL = import.meta.env.DEV ? '/new-api' : (import.meta.env.VITE_NEW_API_BASE_URL || 'http://************:18080/jeecgboot')
  return `${baseURL}/sys/common/upload`
})

const uploadHeaders = computed(() => {
  const token = localStorage.getItem('freedomToken') || sessionStorage.getItem('freedomToken')
  return token ? { 'x-access-token': token } : {}
})



// 根据类型设置接受的文件格式
const accept = computed(() => {
  if (props.accept) return props.accept
  
  switch (props.type) {
    case 'image':
      return 'image/*'
    case 'video':
      return 'video/*'
    case 'other':
    default:
      return '*'
  }
})



// 上传成功回调
const handleSuccess = (response: any, file: UploadFile) => {
  console.log('文件上传成功:', response, file)

  if (response.success) {
    // 确保文件URL被正确设置
    file.url = response.message

    if (props.type === 'image') {
      // 图片上传：确保文件列表中的对应文件URL被更新
      const targetFile = fileList.value.find(f => f.uid === file.uid)
      if (targetFile) {
        targetFile.url = response.message
        targetFile.status = 'success'
      }
    } else if (props.type === 'video') {
      // 视频上传：手动管理文件列表
      const newFile: UploadFile = {
        name: file.name,
        url: response.message,
        size: file.size,
        status: 'success',
        uid: file.uid || Date.now()
      }

      // 视频只允许一个文件，替换现有文件
      fileList.value = [newFile]

      // 清空内部文件列表，准备下次上传
      internalFileList.value = []
    }

    ElMessage.success('文件上传成功')
    emit('success', response, file)
  } else {
    ElMessage.error(response.message || '文件上传失败')
    emit('error', response, file)
  }
}

// 上传失败回调
const handleError = (error: any, file: UploadFile) => {
  console.error('文件上传失败:', error, file)
  ElMessage.error('文件上传失败')
  emit('error', error, file)
}

// 文件移除回调
const handleRemove = (file: UploadFile) => {
  const newList = fileList.value.filter(item => item.uid !== file.uid)
  fileList.value = newList
}

// 文件预览回调
const handlePreview = (file: UploadFile) => {
  if (file.url) {
    window.open(file.url, '_blank')
  }
}

// 视频上传前的处理
const handleBeforeVideoUpload = (_file: File) => {
  // 如果已经有视频文件，清空内部文件列表以允许新的上传
  if (fileList.value.length > 0) {
    internalFileList.value = []
  }
  return true
}
</script>

<style scoped lang="scss">
.file-upload-wrapper {
  .upload-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #999;
    line-height: 1.4;
  }
}

// 图片上传样式
.upload-image {
  :deep(.el-upload--picture-card) {
    width: 104px;
    height: 104px;
    border-radius: 6px;
    border: 1px dashed #d9d9d9;

    &:hover {
      border-color: #004C66;
    }
  }

  // 让图片列表项与上传框保持一致的尺寸
  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    width: 104px;
    height: 104px;
    border-radius: 6px;
  }

  .upload-icon {
    font-size: 28px;
    color: #8c939d;
  }
}

// 视频上传样式
.video-upload-container {
  .video-list {
    margin-top: 16px;

    .video-item {
      display: flex;
      align-items: center;
      padding: 12px;
      border: 1px solid #e5e5e5;
      border-radius: 6px;
      margin-bottom: 8px;
      background: #fafafa;

      .video-preview {
        display: flex;
        align-items: center;
        flex: 1;
        cursor: pointer;

        .video-icon {
          font-size: 24px;
          color: #004C66;
          margin-right: 12px;
        }

        .video-name {
          font-size: 14px;
          color: #333;
          word-break: break-all;
        }
      }

      .video-actions {
        display: flex;
        gap: 8px;

        .el-button {
          padding: 4px 8px;
          font-size: 12px;
        }
      }
    }
  }
}

// 其他文件上传样式
.upload-other {
  :deep(.el-upload-list) {
    margin-top: 8px;
  }
}

// 禁用状态
.upload-disabled {
  opacity: 0.6;
  pointer-events: none;
}
</style>
