/* // postcss.config.js
module.exports = {
    plugins: {
      'postcss-px-to-viewport': {
        unitToConvert: 'px',
        viewportWidth: 1920, // 设计稿宽度
        unitPrecision: 5,
        propList: ['*'],
        viewportUnit: 'vw',
        fontViewportUnit: 'vw',
        selectorBlackList: ['.ignore-vw', '.el-icon'], // 忽略转换的选择器
        minPixelValue: 1,
        mediaQuery: true, // 允许在媒体查询中转换px
        replace: true,
        exclude: [/node_modules/], // 忽略node_modules文件夹下的文件
        landscape: false, // 是否处理横屏情况
      }
    }
  } */