<template>
  <div class="file-upload-example">
    <h2>FileUpload 组件使用示例</h2>
    
    <!-- 图片上传示例 -->
    <div class="example-section">
      <h3>图片上传</h3>
      <FileUpload
        v-model="imageFiles"
        type="image"
        :limit="5"
        tip="建议尺寸800*800像素，最多上传5张"
        @change="handleImageChange"
        @success="handleSuccess"
        @error="handleError"
      />
      <div class="result">
        <p>当前图片数量: {{ imageFiles.length }}</p>
        <p>JSON数据: {{ convertFilesToJson(imageFiles) }}</p>
      </div>
    </div>
    
    <!-- 视频上传示例 -->
    <div class="example-section">
      <h3>视频上传</h3>
      <FileUpload
        v-model="videoFiles"
        type="video"
        :limit="1"
        tip="建议视频宽高比16:9，时长9~30秒"
        @change="handleVideoChange"
        @success="handleSuccess"
        @error="handleError"
      />
      <div class="result">
        <p>当前视频数量: {{ videoFiles.length }}</p>
        <p>JSON数据: {{ convertFilesToJson(videoFiles) }}</p>
      </div>
    </div>
    
    <!-- 其他文件上传示例 -->
    <div class="example-section">
      <h3>其他文件上传</h3>
      <FileUpload
        v-model="otherFiles"
        type="other"
        :limit="3"
        tip="支持常见文件格式，单个文件不超过50MB"
        accept=".pdf,.doc,.docx,.xls,.xlsx"
        @change="handleFileChange"
        @success="handleSuccess"
        @error="handleError"
      />
      <div class="result">
        <p>当前文件数量: {{ otherFiles.length }}</p>
        <p>JSON数据: {{ convertFilesToJson(otherFiles) }}</p>
      </div>
    </div>
    
    <!-- 禁用状态示例 -->
    <div class="example-section">
      <h3>禁用状态</h3>
      <FileUpload
        v-model="disabledFiles"
        type="image"
        :limit="3"
        tip="此上传组件已禁用"
        :disabled="true"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { UploadFile } from 'element-plus'
import { ElMessage } from 'element-plus'
import FileUpload from './index.vue'
import { convertFilesToJson } from './utils'

// 文件列表
const imageFiles = ref<UploadFile[]>([])
const videoFiles = ref<UploadFile[]>([])
const otherFiles = ref<UploadFile[]>([])
const disabledFiles = ref<UploadFile[]>([])

// 处理图片变化
const handleImageChange = (files: UploadFile[]) => {
  console.log('图片文件变化:', files)
}

// 处理视频变化
const handleVideoChange = (files: UploadFile[]) => {
  console.log('视频文件变化:', files)
}

// 处理其他文件变化
const handleFileChange = (files: UploadFile[]) => {
  console.log('其他文件变化:', files)
}

// 处理上传成功
const handleSuccess = (response: any, file: UploadFile) => {
  console.log('上传成功:', response, file)
  ElMessage.success(`文件 ${file.name} 上传成功`)
}

// 处理上传失败
const handleError = (error: any, file: UploadFile) => {
  console.error('上传失败:', error, file)
  ElMessage.error(`文件 ${file.name} 上传失败`)
}
</script>

<style scoped lang="scss">
.file-upload-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  
  h2 {
    color: #333;
    margin-bottom: 30px;
    text-align: center;
  }
}

.example-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  background: #fafafa;
  
  h3 {
    color: #004C66;
    margin-bottom: 20px;
    font-size: 18px;
  }
  
  .result {
    margin-top: 20px;
    padding: 15px;
    background: #f0f0f0;
    border-radius: 4px;
    
    p {
      margin: 5px 0;
      font-size: 14px;
      color: #666;
      word-break: break-all;
    }
  }
}
</style>
