import { md5 } from 'js-md5'

/**
 * 认证相关工具函数
 * 包含时间戳生成、token生成、参数构建等功能
 */

/**
 * 获取当前时间戳（秒级）
 * @returns {string} 当前时间戳字符串
 */
export function getTime(): string {
  return Math.floor(Date.now() / 1000).toString()
}

/**
 * 生成MD5加密token
 * @returns {string} 加密后的token
 */
export function getToken(): string {
  const time = getTime()
  return md5(md5(time) + 'huiguwegfg')
}

/**
 * 获取请求参数
 * 构建包含token、时间戳等认证信息的参数对象
 * @param {boolean} isSelect - 是否为查询类型请求
 * @returns {object} 包含认证信息的参数对象
 */
export function getParams(isSelect?: boolean): Record<string, any> {
  const time = getTime()
  const token = getToken()
  
  // 从localStorage获取apptoken
  const apptoken = localStorage.getItem('apptoken') || ''
  
  let params: Record<string, any> = {
    token,
    timestamp: time,
    newban: 399
  }
  
  // 如果apptoken存在且不为'undefined'字符串，则添加到参数中
  if (apptoken !== 'undefined') {
    params.apptoken = apptoken
  }
  
  // 如果不是查询类型请求，添加type参数
  if (!isSelect) {
    params.type = 1
  }
  
  return params
}

/**
 * 设置用户token到localStorage
 * @param {string} token - 用户token
 */
export function setAppToken(token: string): void {
  localStorage.setItem('apptoken', token)
}

/**
 * 获取用户token从localStorage
 * @returns {string} 用户token
 */
export function getAppToken(): string {
  return localStorage.getItem('apptoken') || ''
}

/**
 * 清除用户token
 */
export function clearAppToken(): void {
  localStorage.removeItem('apptoken')
}

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export function isLoggedIn(): boolean {
  const token = getAppToken()
  return token !== '' && token !== 'undefined'
}