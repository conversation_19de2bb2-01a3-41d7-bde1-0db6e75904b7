// @ts-ignore
import areaData from "@/assets/js/area";

// 地区选项类型定义 - 兼容 Element Plus CascaderOption
export interface AreaOption extends Record<string, unknown> {
  value: string;
  label: string;
  children?: AreaOption[];
}

/**
 * 初始化地区数据，将原始地区数据转换为级联选择器所需的格式
 * @returns AreaOption[] 格式化后的地区数据
 */
export const initAreaData = (): AreaOption[] => {
  const { province_list, city_list, county_list } = areaData.area;
  const provinces: AreaOption[] = [];

  // 遍历省份
  for (const [provinceCode, provinceName] of Object.entries(province_list)) {
    const province: AreaOption = {
      value: provinceCode,
      label: provinceName as string,
      children: [],
    };

    // 遍历城市
    for (const [cityCode, cityName] of Object.entries(city_list)) {
      if (cityCode.startsWith(provinceCode.substring(0, 2))) {
        const city: AreaOption = {
          value: cityCode,
          label: cityName as string,
          children: [],
        };

        // 遍历区县
        for (const [districtCode, districtName] of Object.entries(
          county_list
        )) {
          if (districtCode.startsWith(cityCode.substring(0, 4))) {
            city.children!.push({
              value: districtCode,
              label: districtName as string,
            });
          }
        }

        if (city.children!.length > 0) {
          province.children!.push(city);
        }
      }
    }

    if (province.children!.length > 0) {
      provinces.push(province);
    }
  }

  return provinces;
};

/**
 * 根据地区名称查找对应的地区代码
 * @param provinceName 省份名称
 * @param cityName 城市名称
 * @param districtName 区县名称
 * @returns string[] | null 返回地区代码数组，如果找不到则返回null
 */
export const getAreaCodesByNames = (
  provinceName: string,
  cityName: string,
  districtName: string
): string[] | null => {
  const { province_list, city_list, county_list } = areaData.area;

  // 查找省份代码
  const provinceCode = Object.keys(province_list).find(code => 
    province_list[code] === provinceName
  );

  if (!provinceCode) {
    return null;
  }

  // 查找城市代码
  const cityCode = Object.keys(city_list).find(code => 
    city_list[code] === cityName && code.startsWith(provinceCode.substring(0, 2))
  );

  if (!cityCode) {
    return null;
  }

  // 查找区县代码
  const districtCode = Object.keys(county_list).find(code => 
    county_list[code] === districtName && code.startsWith(cityCode.substring(0, 4))
  );

  if (!districtCode) {
    return null;
  }

  return [provinceCode, cityCode, districtCode];
};

/**
 * 根据地区代码查找对应的地区名称
 * @param provinceCode 省份代码
 * @param cityCode 城市代码
 * @param districtCode 区县代码
 * @returns string[] | null 返回地区名称数组，如果找不到则返回null
 */
export const getAreaNamesByCodes = (
  provinceCode: string,
  cityCode: string,
  districtCode: string
): string[] | null => {
  const { province_list, city_list, county_list } = areaData.area;

  const provinceName = province_list[provinceCode];
  const cityName = city_list[cityCode];
  const districtName = county_list[districtCode];

  if (!provinceName || !cityName || !districtName) {
    return null;
  }

  return [provinceName, cityName, districtName];
};