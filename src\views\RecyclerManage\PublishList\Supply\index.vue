<template>
  <div class="supply-list-container">
    <DataTable
      :config="tableConfig"
      :search-loading="searchLoading"
      @on-button-click="handleButtonClick"
      @on-filter-change="handleFilterChange"
      @on-tab-change="handleTabChange"
      @on-page-change="handlePageChange"
      @on-sort-change="handleSortChange"
      @on-row-click="handleRowClick"
      @on-row-double-click="handleRowDoubleClick"
      @on-selection-change="handleSelectionChange"
      @on-edit="handleEdit"
      @on-delete="handleDeleteItem"
      @on-view="handleView"
    />

    <!-- 操作按钮浮动工具栏 -->
    <div v-if="selectedRows.length > 0" class="floating-toolbar">
      <el-button type="primary" @click="handleBatchEdit">
        <el-icon><Edit /></el-icon>
        批量编辑
      </el-button>
      <el-button type="danger" @click="handleBatchDelete">
        <el-icon><Delete /></el-icon>
        批量删除
      </el-button>
      <el-button @click="handleBatchExport">
        <el-icon><Download /></el-icon>
        批量导出
      </el-button>
      <span class="selection-info">已选择 {{ selectedRows.length }} 项</span>
    </div>

    <!-- 详情查看弹窗 -->
    <SupplyDemandDetailModal
      v-model="detailModalVisible"
      :record-id="currentRecordId"
      :service-type="4"
      @close="handleDetailClose"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Edit, Delete, Download } from '@element-plus/icons-vue'
import DataTable from '@/components/DataTable/index.vue'
import type { TableConfig } from '@/types/table'
import { supplyDemandApi } from '@/utils/api-new'
import SupplyDemandDetailModal from '@/components/SupplyDemandDetailModal/index.vue'

// 表格配置
const tableConfig = ref<TableConfig>({
  // 不显示按钮筛选栏
  buttonBar: {
    show: false,
    buttons: []
  },

  // 筛选栏配置
  filterBar: {
    show: true,
    filters: [
      {
        key: 'infoTitle',
        label: '供应标题',
        type: 'input',
        placeholder: '请输入供应标题',
        width: '130px'
      },
      {
        key: 'materialTypeName',
        label: '物资类型',
        type: 'input',
        placeholder: '请输入物资类型',
        width: '130px'
      },
      {
        key: 'createTimeRange',
        label: '发布时间',
        type: 'daterange',
        placeholder: '请选择时间范围',
        width: '210px'
      }
    ]
  },

  // 水平导航栏配置
  tabBar: {
    show: true,
    activeKey: 'all',
    tabs: [
      { key: 'all', label: '全部供应' },
      { key: 'draft', label: '草稿' },
      { key: 'pending', label: '待审核' },
      { key: 'approved', label: '已通过' },
      { key: 'rejected', label: '未通过' },
      { key: 'published', label: '已发布' }
    ]
  },

  // 表格列配置（基于参考文件）
  columns: [
    {
      key: 'index',
      label: '序号',
      width: '60px',
      align: 'center'
    },
    {
      key: 'images',
      label: '图片',
      width: '120px',
      align: 'center',
      type: 'image'
    },
    {
      key: 'infoTitle',
      label: '供应标题',
      width: '200px',
      align: 'left'
    },
    {
      key: 'materialTypeName',
      label: '物资类型',
      width: '120px',
      align: 'center'
    },
    {
      key: 'address',
      label: '所在地区',
      width: '180px',
      align: 'center',
      formatter: (_, row) => {
        const province = row.provinceName || ''
        const city = row.cityName || ''
        const district = row.districtName || ''
        return [province, city, district].filter(Boolean).join(' ')
      }
    },
    {
      key: 'status',
      label: '审核状态',
      width: '100px',
      align: 'center',
      type: 'tag',
      formatter: (value) => getStatusText(value)
    },
    {
      key: 'createTime',
      label: '发布时间',
      width: '150px',
      align: 'center',
      type: 'date'
    },
    {
      key: 'actions',
      label: '操作',
      width: '200px',
      align: 'center',
      type: 'action'
    }
  ],

  // 分页配置
  pagination: {
    show: true,
    current: 1,
    pageSize: 10,
    total: 0
  },

  // 数据
  data: [],
  loading: true, // 初始状态为加载中

  // 表格样式配置
  tableStyle: {
    maxHeight: 420, // 设置表格最大高度
    border: false,   // 显示边框
    stripe: true,    // 显示斑马纹
    showSelection: false // 显示选择列
  }
})

// 选中的行
const selectedRows = ref<any[]>([])

// 加载状态
const searchLoading = ref(false)

// 详情弹窗相关
const detailModalVisible = ref(false)
const currentRecordId = ref<string | null>(null)

// 状态处理函数
const getStatusText = (status: number): string => {
  switch (status) {
    case 1:
      return '草稿'
    case 2:
      return '待审核'
    case 3:
      return '审核通过'
    case 4:
      return '审核拒绝'
    case 5:
      return '已发布'
    case 6:
      return '已成交'
    case 7:
      return '已撤拍'
    case 8:
      return '已过期'
    default:
      return '未知'
  }
}

const getStatusColor = (status: number): string => {
  switch (status) {
    case 1:
      return 'default' // 草稿
    case 2:
      return 'processing' // 待审核
    case 3:
      return 'success' // 审核通过
    case 4:
      return 'error' // 审核拒绝
    case 5:
      return 'success' // 已发布
    case 6:
      return 'warning' // 已成交
    case 7:
      return 'default' // 已撤拍
    case 8:
      return 'default' // 已过期
    default:
      return 'default'
  }
}

// 查询参数
const queryParams = reactive({
  infoTitle: '', // 供应标题
  materialTypeName: '', // 物资类型
  createTimeRange: [], // 发布时间范围
  status: 'all', // 状态筛选
  page: 1,
  pageSize: 10,
  total: 0
})



// 事件处理函数
const handleButtonClick = (key: string) => {
  // 按钮筛选已移除，保留函数以避免错误
  console.log('按钮点击:', key)
}

const handleFilterChange = (filters: Record<string, any>) => {
  console.log('筛选变化:', filters)
  Object.assign(queryParams, filters)
  queryParams.page = 1 // 重置到第一页
  loadData(true) // 显示搜索按钮加载状态
}

const handleTabChange = (key: string) => {
  console.log('标签页切换:', key)
  queryParams.status = key

  // 更新标签页状态
  if (tableConfig.value.tabBar) {
    tableConfig.value.tabBar.activeKey = key
  }

  queryParams.page = 1 // 重置到第一页
  loadData()
}

const handlePageChange = (page: number, pageSize: number) => {
  console.log('分页变化:', page, pageSize)
  queryParams.page = page
  queryParams.pageSize = pageSize
  loadData()
}

const handleSortChange = (column: string, order: 'asc' | 'desc' | null) => {
  console.log('排序变化:', column, order)
  // 这里可以添加排序逻辑
  loadData()
}

const handleRowClick = (row: any, index: number) => {
  console.log('行点击:', row, index)
  // 可以跳转到详情页面
}

const handleRowDoubleClick = (row: any, index: number) => {
  console.log('行双击:', row, index)
  // 可以直接编辑
}

const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
  console.log('选择变化:', selection)
}

// 批量操作
const handleBatchEdit = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要编辑的项目')
    return
  }
  ElMessage.info(`批量编辑 ${selectedRows.value.length} 个供应信息`)
}

const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要删除的项目')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个供应信息吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    ElMessage.success('删除成功')
    selectedRows.value = []
    loadData()
  } catch {
    ElMessage.info('已取消删除')
  }
}

const handleBatchExport = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要导出的项目')
    return
  }
  ElMessage.info(`导出 ${selectedRows.value.length} 个供应信息`)
}

// 单项操作
const handleEdit = (row: any) => {
  console.log('编辑供应信息:', row)
  ElMessage.info(`编辑供应信息: ${row.title}`)
}

const handleDeleteItem = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除供应信息"${row.infoTitle || row.title}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 调用删除API
    const response = await supplyDemandApi.deleteSupplyDemand({ id: row.id })

    if (response.success) {
      ElMessage.success('删除成功')
      loadData() // 重新加载数据
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败，请稍后重试')
    }
  }
}

const handleView = (row: any) => {
  console.log('查看供应信息:', row)
  if (!row.id) {
    ElMessage.error('缺少记录ID，无法查看详情')
    return
  }
  currentRecordId.value = row.id
  detailModalVisible.value = true
}

// 关闭详情弹窗
const handleDetailClose = () => {
  detailModalVisible.value = false
  currentRecordId.value = null
}

// 加载数据
const loadData = async (showSearchLoading = false) => {
  // 控制表格加载状态
  tableConfig.value.loading = true

  // 控制搜索按钮加载状态
  if (showSearchLoading) {
    searchLoading.value = true
  }

  try {
    // 构建API请求参数
    const apiParams: any = {
      pageNo: queryParams.page,
      pageSize: queryParams.pageSize,
      type: 4 // 1表示供应信息
    }

    // 添加筛选条件
    if (queryParams.infoTitle) {
      apiParams.infoTitle = queryParams.infoTitle
    }

    if (queryParams.materialTypeName) {
      apiParams.materialTypeName = queryParams.materialTypeName
    }

    // 处理日期范围
    if (queryParams.createTimeRange && queryParams.createTimeRange.length === 2) {
      apiParams.startTime = queryParams.createTimeRange[0]
      apiParams.endTime = queryParams.createTimeRange[1]
    }

    // 处理状态筛选
    if (queryParams.status !== 'all') {
      const statusMap: Record<string, number> = {
        'draft': 1,
        'pending': 2,
        'approved': 3,
        'rejected': 4,
        'published': 5
      }
      apiParams.status = statusMap[queryParams.status]
    }

    // 调用API
    const response = await supplyDemandApi.querySupplyDemandList(apiParams)

    if (response.success && response.result) {
      // 处理返回的数据
      const { records, total } = response.result

      // 为每条记录添加序号和处理图片
      const processedData = records.map((item: any, index: number) => ({
        ...item,
        index: (queryParams.page - 1) * queryParams.pageSize + index + 1,
        images: item.images ? item.images.split(',')[0] : '' // 取第一张图片
      }))

      tableConfig.value.data = processedData

      // 更新分页信息
      if (tableConfig.value.pagination) {
        tableConfig.value.pagination.current = queryParams.page
        tableConfig.value.pagination.pageSize = queryParams.pageSize
        tableConfig.value.pagination.total = total || 0
      }

      queryParams.total = total || 0
    } else {
      ElMessage.error(response.message || '获取数据失败')
      tableConfig.value.data = []
      if (tableConfig.value.pagination) {
        tableConfig.value.pagination.total = 0
      }
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败，请稍后重试')
    tableConfig.value.data = []
    if (tableConfig.value.pagination) {
      tableConfig.value.pagination.total = 0
    }
  } finally {
    tableConfig.value.loading = false
    searchLoading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
.supply-list-container {
  position: relative;
  height: 100%;

  .page-header {
    margin-bottom: 20px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    border-bottom: 1px solid #e5e5e5;

    h2 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 24px;
      font-weight: 600;
    }

    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }

  .floating-toolbar {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid #e5e5e5;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 12px;

    .selection-info {
      color: #666;
      font-size: 14px;
      margin-left: 8px;
    }
  }
}
</style>