<template>
  <div class="auctionDetailc min_wrapper_1500">
    <!-- 遮罩层 -->
    <div v-if="!userInfo" class="content-mask">
      <span class="tips">登录后可查看更多信息</span>
      <span @click="goLogin" class="login-btn">去登录</span>
    </div>
    <!-- <Headers /> -->
    <div class="auctionDetail_c">
      <div class="auctionDetail_c_one">
        <div class="auctionDetail_c_one_left">
          <div class="auctionDetail_c_one_left_s" style="cursor: pointer">
            <span class="auctionDetail_c_one_left_s_span">{{
              biaodInfo.pmh_name
            }}</span>
            <span class="auction_c_one_img">
              {{ biaodInfo.pmh_xingshi == 1 ? "同步竞价" : "线上竞价" }}
            </span>
          </div>
          <div class="auctionDetail_c_one_left_da">
            <div class="auctionDetail_c_one_left_w" @click="goQiye">
              <SvgIcon
                iconName="auctionDetail-name"
                className="one_left_icon"
              />
              {{ biaodInfo.qiyemingcheng }}
              <span
                class="qiye-follow-text"
                :class="{ 'qiye-follow-cancel': isCompanyFollowed }"
                @click="followCompany"
                >{{ isCompanyFollowed ? "取消收藏" : "收藏" }}</span
              >
            </div>
            <div class="auctionDetail_c_one_left_w">
              <SvgIcon
                iconName="auctionDetail-phone"
                className="one_left_icon"
              />
              {{ biaodInfo.qiyephone }}
            </div>
          </div>
        </div>
        <!-- 标的列表横向滚动区域 -->
        <div class="auction-items-scroll-container">
          <div class="auction-items-wrapper">
            <div
              class="auction-item-card"
              v-for="(item, i) in biaodList"
              :key="item.id"
              @click="setBiaodiId(item)"
              :class="{ active: biaodInfo.id == item.id }"
            >
              <!-- 标的图片 -->
              <div class="auction-item-image">
                <img :src="url + item.bd_url" :alt="item.bd_title" />

                <!-- 状态标签 -->
                <div
                  class="status-tag"
                  :class="{
                    'status-ongoing':
                      item.bd_status == 2 || item.bd_status == 3,
                    'status-upcoming': item.bd_status == 1,
                    'status-ended':
                      item.bd_status == 4 ||
                      item.bd_status == 8 ||
                      item.bd_status == 5,
                    'status-paused': item.bd_status == 7 || item.bd_status == 6,
                  }"
                >
                  <span v-if="item.bd_status == 2 || item.bd_status == 3"
                    >正在进行</span
                  >
                  <span v-else-if="item.bd_status == 1">即将开始</span>
                  <span v-else-if="item.bd_status == 4">已流拍</span>
                  <span v-else-if="item.bd_status == 8">已撤拍</span>
                  <span v-else-if="item.bd_status == 7">已暂停</span>
                  <span v-else-if="item.bd_status == 6">审批中</span>
                  <span v-else-if="item.bd_status == 5">已成交</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="auctionDetail_c_two_wrapper">
        <div class="auctionDetail_c_two">
          <div
            ref="containerRef"
            class="auctionDetail_c_two_left"
            :class="{ 'masked-content': !userInfo }"
          >
            <div class="">
              <!-- 面包屑导航和观看数 -->
              <div class="header-section">
                <div class="breadcrumb">
                  <span class="auction-title" @click="handleGoBack"
                    >拍卖详情</span
                  >
                  <span class="separator">></span>
                  <span class="current">{{ biaodInfo.bd_title }}</span>
                </div>
                <div class="view-count">
                  <div
                    class="collect-info"
                    @click="biaodishoucangClick(isShoucang ? 0 : 1)"
                    style="cursor: pointer"
                  >
                    <SvgIcon
                      iconName="freedom-propertyDetail-star"
                      className="star-icon"
                      :style="`color: ${isShoucang ? '#ffa500' : '#999'}`"
                    />
                    <span
                      class="qiye-shoucang-text"
                      :class="{ 'qiye-shoucang-cancel': isShoucang }"
                      >{{ isShoucang ? "取消收藏" : "收藏" }}</span
                    >
                  </div>
                  <div class="view-info">
                    <SvgIcon
                      iconName="freedom-propertyDetail-eye"
                      className="eye-icon"
                    />
                    <span>{{ biaodInfo.bd_weiguan }}</span>
                  </div>
                </div>
              </div>
              <div class="auctionDetail_c_two_left_two">
                <div class="auctionDetail_c_two_left_two_left">
                  <div class="auctionDetail_c_two_left_two_leftssssss">
                    <div class="auctionDetail_c_two_left_two_left_o">
                      <img
                        :src="url + biaodiImgs[biaodiImgsIndex]"
                        @error="(e: Event) => (e.target as HTMLImageElement).src = biaodiImgs[biaodiImgsIndex] ? 'https://oss.yunpaiwang.com/' + biaodiImgs[biaodiImgsIndex] : ''"
                      />
                      <div
                        class="auctionDetail_c_two_left_two_left_omaskTop"
                        @mouseenter="handleImageMouseEnter"
                        @mousemove="handleImageMouseMove"
                        @mouseleave="handleImageMouseLeave"
                      ></div>
                      <div
                        v-show="isImageHovered"
                        class="auctionDetail_c_two_left_two_left_omaskToptop"
                        :style="{
                          transform: `translate(${maskPosition.x}px, ${maskPosition.y}px)`,
                          width: maskSize + 'px',
                          height: maskSize + 'px',
                        }"
                      ></div>
                    </div>
                    <div
                      v-show="isImageHovered"
                      class="auctionDetail_c_two_left_two_left_omaskTopright"
                    >
                      <img
                        :style="{
                          transform: `translate(-${maskPosition.translateX}px, -${maskPosition.translateY}px)`,
                          transformOrigin: 'top left',
                        }"
                        class="auctionDetail_c_two_left_two_left_omaskToprightrightImg"
                        :src="url + biaodiImgs[biaodiImgsIndex]"
                        alt=""
                        @error="(e: Event) => (e.target as HTMLImageElement).src = biaodiImgs[biaodiImgsIndex] ? 'https://oss.yunpaiwang.com/' + biaodiImgs[biaodiImgsIndex] : ''"
                      />
                    </div>
                  </div>
                  <!-- 图片列表容器 - 水平滚动 -->
                  <div class="image-list-container-horizontal">
                    <!-- 向左滚动箭头 -->
                    <div
                      v-if="canScrollLeft"
                      class="scroll-arrow scroll-left"
                      @click="scrollLeft"
                    >
                      <div class="arrow-overlay"></div>
                      <SvgIcon
                        iconName="freedom-propertyDetail-down"
                        className="scroll-icon scroll-icon-left"
                      />
                    </div>

                    <!-- 图片列表 -->
                    <div
                      class="image-list-horizontal"
                      ref="imageListHorizontalRef"
                      @mousemove="handleScrollAreaMouseMove"
                      @mouseleave="handleScrollAreaMouseLeave"
                    >
                      <div
                        v-for="(item, i) in biaodiImgs"
                        :key="i"
                        class="image-item-horizontal"
                        :class="{ active: biaodiImgsIndex === i }"
                        @mousemove="setIndex(i)"
                      >
                        <img
                          :src="url + item"
                          :alt="`图片${i + 1}`"
                          @error="(e: Event) => (e.target as HTMLImageElement).src = item ? 'https://oss.yunpaiwang.com/' + item : ''"
                        />
                      </div>
                    </div>

                    <!-- 向右滚动箭头 -->
                    <div
                      v-if="canScrollRight"
                      class="scroll-arrow scroll-right"
                      @click="scrollRight"
                    >
                      <div class="arrow-overlay"></div>
                      <SvgIcon
                        iconName="freedom-propertyDetail-down"
                        className="scroll-icon scroll-icon-right"
                      />
                    </div>
                  </div>
                </div>
                <div class="auctionDetail_c_two_left_two_right">
                  <div class="auctionDetail_c_two_left_two_right_one">
                    <!-- 开拍前倒计时 -->
                    <div
                      v-if="biaodInfo.bd_status == 1 && !isPaimaishi"
                      class="auctionDetail_c_two_left_two_right_one_w"
                    >
                      <div class="auctionDetail_c_two_left_two_right_one_w_b">
                        <span
                          class="auctionDetail_c_two_left_two_right_one_w_bs"
                          >距离开始&emsp;</span
                        >
                        <div class="time">
                          倒计时:<span
                            class="auctionDetail_c_two_left_two_right_one_w_bsb"
                            >{{
                              info.dayDiff.toString().padStart(2, "0")
                            }}</span
                          >天<span
                            class="auctionDetail_c_two_left_two_right_one_w_bsb"
                            >{{ info.hours.toString().padStart(2, "0") }}</span
                          >时<span
                            class="auctionDetail_c_two_left_two_right_one_w_bsb"
                            >{{
                              info.minutes.toString().padStart(2, "0")
                            }}</span
                          >分<span
                            class="auctionDetail_c_two_left_two_right_one_w_bsb"
                            >{{
                              info.seconds.toString().padStart(2, "0")
                            }}</span
                          >秒
                        </div>
                      </div>
                    </div>
                    <!-- 竞价进行中倒计时 -->
                    <div
                      v-if="
                        (biaodInfo.bd_status == 2 ||
                          biaodInfo.bd_status == 3) &&
                        !isPaimaishi
                      "
                      class="auctionDetail_c_two_left_two_right_one_w"
                    >
                      <div class="auctionDetail_c_two_left_two_right_one_w_b">
                        <span
                          class="auctionDetail_c_two_left_two_right_one_w_bs"
                        >
                          &emsp;{{
                            biaodInfo.bd_status == 2 ? "自由竞价" : "限时竞价"
                          }}&emsp;
                        </span>
                        <div class="time">
                          倒计时:<span
                            class="auctionDetail_c_two_left_two_right_one_w_bsb"
                            >{{
                              info.dayDiff.toString().padStart(2, "0")
                            }}</span
                          >天
                          <span
                            class="auctionDetail_c_two_left_two_right_one_w_bsb"
                            >{{ info.hours.toString().padStart(2, "0") }}</span
                          >时<span
                            class="auctionDetail_c_two_left_two_right_one_w_bsb"
                            >{{
                              info.minutes.toString().padStart(2, "0")
                            }}</span
                          >分<span
                            class="auctionDetail_c_two_left_two_right_one_w_bsb"
                            >{{
                              info.seconds.toString().padStart(2, "0")
                            }}</span
                          >秒
                        </div>
                      </div>
                    </div>
                    <!-- 成交状态显示结束时间 -->
                    <div
                      v-if="biaodInfo.bd_status == 5 && !isPaimaishi"
                      class="auctionDetail_c_two_left_two_right_one_w"
                    >
                      <div class="auctionDetail_c_two_left_two_right_one_w_b">
                        <span
                          class="auctionDetail_c_two_left_two_right_one_w_bs finish-time"
                          >结束时间&nbsp;&nbsp;</span
                        >
                        <span
                          class="auctionDetail_c_two_left_two_right_one_w_b_time"
                        >
                          <span>{{
                            formatEvent(biaodInfo.end_time_name)[0]
                          }}</span
                          >年<span>{{
                            formatEvent(biaodInfo.end_time_name)[1]
                          }}</span
                          >月<span>{{
                            formatEvent(biaodInfo.end_time_name)[2]
                          }}</span
                          >日<span>{{
                            formatEvent(biaodInfo.end_time_name)[3]
                          }}</span
                          >时<span>{{
                            formatEvent(biaodInfo.end_time_name)[4]
                          }}</span
                          >分<span>{{
                            formatEvent(biaodInfo.end_time_name)[5]
                          }}</span
                          >秒
                        </span>
                      </div>
                    </div>
                    <!-- 审批中显示标的审批中 -->
                    <div
                      v-if="biaodInfo.bd_status == 6"
                      class="auctionDetail_c_two_left_two_right_one_w"
                    >
                      <div class="auctionDetail_c_two_left_two_right_one_w_b">
                        <span
                          class="auctionDetail_c_two_left_two_right_one_w_b_time"
                          >&emsp;标的审批中&emsp;</span
                        >
                      </div>
                    </div>
                  </div>

                  <!-- 价格信息和操作区域 -->
                  <div
                    class="auctionDetail_c_two_left_two_right_one_thre"
                    style="
                      position: relative;
                      z-index: 2;
                      overflow: hidden;
                      padding: 20px;
                    "
                  >
                    <!-- 价格显示区域 -->
                    <template v-if="isTeshu">
                      <!-- 起拍价显示（开拍前/撤拍/流拍状态） -->
                      <div
                        v-if="
                          biaodInfo.bd_status == 1 ||
                          biaodInfo.bd_status == 8 ||
                          biaodInfo.bd_status == 4
                        "
                        class="auctionDetail_c_two_left_two_right_one_thre_one price-bidder-row"
                      >
                        <div class="jiage">
                          <div class="jiage_title">起拍价</div>
                          <div class="price-display">
                            <div class="price-amount">
                              <span class="price-symbol">￥</span>
                              <span class="price-integer">{{
                                Math.floor(Number(biaodInfo.bd_qipaijia))
                              }}</span>
                              <span class="price-decimal"
                                >.{{
                                  (
                                    (Number(biaodInfo.bd_qipaijia) -
                                      Math.floor(
                                        Number(biaodInfo.bd_qipaijia)
                                      )) *
                                    100
                                  )
                                    .toFixed(0)
                                    .padStart(2, "0")
                                }}</span
                              >
                              <span class="price-unit"
                                >元/{{ biaodInfo.bd_danwei }}</span
                              >
                            </div>
                          </div>
                        </div>
                        <div class="bidder-info">
                          <div class="bidder-title">出价人</div>
                          <div class="bidder-value">暂无</div>
                        </div>
                      </div>
                      <!-- 当前价显示（竞价中/延时/暂停/审批状态） -->
                      <div
                        v-if="
                          biaodInfo.bd_status == 2 ||
                          biaodInfo.bd_status == 3 ||
                          biaodInfo.bd_status == 7 ||
                          biaodInfo.bd_status == 6
                        "
                        class="auctionDetail_c_two_left_two_right_one_thre_one price-bidder-row"
                      >
                        <div class="jiage">
                          <div class="jiage_title">当前价</div>
                          <div class="price-display">
                            <div class="price-amount">
                              <span class="price-symbol">￥</span>
                              <span class="price-integer">{{
                                Math.floor(Number(dangqianjiaInit))
                              }}</span>
                              <span class="price-decimal"
                                >.{{
                                  (
                                    (Number(dangqianjiaInit) -
                                      Math.floor(Number(dangqianjiaInit))) *
                                    100
                                  )
                                    .toFixed(0)
                                    .padStart(2, "0")
                                }}</span
                              >
                              <span class="price-unit"
                                >元/{{ biaodInfo.bd_danwei }}</span
                              >
                            </div>
                          </div>
                        </div>
                        <!-- 普通竞价出价人显示 -->
                        <div
                          v-if="
                            biaodInfo.pmh_type !== 3 && biaodInfo.pmh_type !== 5
                          "
                          class="bidder-info"
                        >
                          <div class="bidder-title">出价人</div>
                          <div class="bidder-value">
                            <span v-if="chujiailist.length == 0">暂无</span>
                            <span
                              v-if="chujiailist.length != 0"
                              class="bidder-name"
                            >
                              {{ chujiailist[0].jingpaihaopai }}
                            </span>
                          </div>
                        </div>
                        <!-- 盲拍出价人显示 -->
                        <div
                          v-if="
                            biaodInfo.pmh_type === 3 || biaodInfo.pmh_type === 5
                          "
                          class="bidder-info"
                        >
                          <div class="bidder-title">出价人</div>
                          <div class="bidder-value">
                            <span v-if="chujiailist.length == 0">暂无</span>
                            <span
                              v-if="chujiailist.length != 0"
                              class="bidder-name"
                            >
                              {{ bd_jdnum }}
                            </span>
                          </div>
                        </div>
                      </div>
                      <!-- 成交价显示（成交状态） -->
                      <div
                        v-if="biaodInfo.bd_status == 5"
                        class="auctionDetail_c_two_left_two_right_one_thre_one price-bidder-row"
                      >
                        <div class="jiage">
                          <div class="jiage_title">成交价</div>
                          <div class="price-display">
                            <div class="price-amount">
                              <span class="price-symbol">￥</span>
                              <span class="price-integer">{{
                                Math.floor(Number(dangqianjiaInit))
                              }}</span>
                              <span class="price-decimal"
                                >.{{
                                  (
                                    (Number(dangqianjiaInit) -
                                      Math.floor(Number(dangqianjiaInit))) *
                                    100
                                  )
                                    .toFixed(0)
                                    .padStart(2, "0")
                                }}</span
                              >
                              <span class="price-unit"
                                >元/{{ biaodInfo.bd_danwei }}</span
                              >
                            </div>
                          </div>
                        </div>
                        <!-- 普通竞价成交人显示 -->
                        <div
                          v-if="
                            biaodInfo.pmh_type !== 3 &&
                            biaodInfo.pmh_type !== 4 &&
                            biaodInfo.pmh_type !== 5
                          "
                          class="bidder-info"
                        >
                          <div class="bidder-title">成交人</div>
                          <div class="bidder-value">
                            <span v-if="chujiailist.length == 0">暂无</span>
                            <span
                              v-if="chujiailist.length != 0"
                              class="bidder-name"
                            >
                              {{ chujiailist[0].jingpaihaopai }}
                            </span>
                          </div>
                        </div>
                        <!-- 盲拍成交人显示 -->
                        <div
                          v-if="
                            biaodInfo.pmh_type === 3 ||
                            biaodInfo.pmh_type === 4 ||
                            biaodInfo.pmh_type === 5
                          "
                          class="bidder-info"
                        >
                          <div class="bidder-title">成交人</div>
                          <div class="bidder-value">
                            <span class="bidder-name">{{ bd_jdnum }}</span>
                          </div>
                        </div>
                      </div>
                    </template>
                    <!-- 无权查看 -->
                    <template v-else>
                      <div
                        class="auctionDetail_c_two_left_two_right_one_thre_one"
                      >
                        <div>无权查看</div>
                      </div>
                    </template>

                    <!-- 操作按钮区域 -->
                    <!-- 审批中状态 -->
                    <template
                      v-if="biaodInfo.bd_status == 6 && baomingIndex != 0"
                    >
                      <div class="registration-tip-row">
                        <div
                          class="auctionDetail_c_two_left_two_right_one_thre_four"
                        >
                          <SvgIcon
                            iconName="auctionDetail-warning"
                            className="warning-icon"
                          />
                          <div>标的状态审批中，请耐心等待审批</div>
                        </div>
                        <div
                          class="auctionDetail_c_two_left_two_right_one_thre_two"
                        >
                          <div>审批中</div>
                        </div>
                      </div>
                    </template>
                    <template
                      v-if="
                        baomingIndex == 0 &&
                        biaodInfo.bd_status != 4 &&
                        biaodInfo.bd_status != 5 &&
                        biaodInfo.bd_status != 8
                      "
                    >
                      <div class="auction_review">
                        <div>
                          <div
                            class="auctionDetail_c_two_left_two_right_one_thre_two auction_review_btn"
                          >
                            <div style="background: rgba(0, 76, 102, 0.1)">
                              报名审核中
                            </div>
                            <div
                              v-if="kanhuoIndex == 0"
                              @click="viewingAgreement"
                              style="margin-left: 10px; cursor: pointer"
                            >
                              确认看货
                            </div>
                            <div
                              v-if="kanhuoIndex == 1"
                              style="margin-left: 10px; background: #e7acac"
                            >
                              已看货
                            </div>
                          </div>
                          <div
                            class="auctionDetail_c_two_left_two_right_one_thre_two auction_review_tips"
                            style="margin-top: 10px"
                            v-if="kanhuoIndex == 1"
                          >
                            <div
                              v-if="!!zhengming"
                              style="width: 210px; background: #e7acac"
                            >
                              缴纳凭证已上传
                            </div>
                            <div
                              v-else
                              @click="zhengmingUpload"
                              style="width: 210px; cursor: pointer"
                            >
                              上传缴纳凭证
                            </div>
                          </div>
                        </div>

                        <div
                          v-if="shoukuanInfo"
                          style="font-size: 12px"
                          class="auction_review_tips"
                        >
                          <div style="line-height: 25px; color: #004c66">
                            请联系客服缴纳保证金本次标的保证金{{
                              biaodInfo.bd_baozhengjin
                            }}元
                          </div>
                          <template v-if="kanhuoIndex == 0">
                            <div style="line-height: 25px">
                              公司：{{ biaodInfo.qiyemingcheng }}
                            </div>
                            <div style="line-height: 25px">
                              银行账号：{{ shoukuanInfo.kaihunum }}
                            </div>
                            <div style="line-height: 25px">
                              开户行：{{ shoukuanInfo.kaihuname }}
                            </div>
                          </template>
                        </div>
                      </div>
                    </template>
                    <template
                      v-if="baomingIndex == 1 && biaodInfo.bd_status == 1"
                    >
                      <div
                        class="auctionDetail_c_two_left_two_right_one_thre_four"
                        style="margin-top: 20px"
                      >
                        <div
                          style="
                            padding: 15px;
                            box-sizing: border-box;
                            background-color: #004c66;
                            color: white;
                            border-radius: 10px;
                          "
                        >
                          保证金审核已通过，请您耐心等待竞价会开始
                        </div>
                      </div>
                    </template>
                    <template
                      v-if="
                        baomingIndex == 1 &&
                        biaodInfo.bd_status != 1 &&
                        biaodInfo.bd_status != 5 &&
                        biaodInfo.bd_status != 4 &&
                        !isPaimaishi &&
                        biaodInfo.bd_status != 8
                      "
                    >
                      <div
                        v-if="
                          biaodInfo.pmh_type == 1 || biaodInfo.pmh_type == 4
                        "
                        class="auctionDetail_c_two_left_two_right_one_thre_chujia"
                      >
                        <div
                          class="auctionDetail_c_two_left_two_right_one_thre_chujias"
                        >
                          <input v-model="jiage" type="number" />
                        </div>
                        <div
                          @click="chujia"
                          class="auctionDetail_c_two_left_two_right_one_thre_chujiass"
                        >
                          提交出价
                        </div>
                      </div>
                      <div
                        v-if="
                          biaodInfo.pmh_type == 1 || biaodInfo.pmh_type == 4
                        "
                        class="auctionDetail_c_two_left_two_right_one_thre_chujia_bottom"
                      >
                        <div @click="zhichu(jiajiadi)">+{{ jiajiadi }}</div>
                        <div @click="zhichu(jiajiazhong)">
                          +{{ jiajiazhong }}
                        </div>
                        <div @click="zhichu(jiajiagao)">+{{ jiajiagao }}</div>
                      </div>
                      <div
                        v-if="biaodInfo.pmh_type == 2"
                        class="auctionDetail_c_two_left_two_right_one_thre_chujia"
                      >
                        <div
                          class="auctionDetail_c_two_left_two_right_one_thre_chujias"
                        >
                          <input v-model="jiage" type="number" />
                        </div>
                        <div
                          @click="jianjiaRequest"
                          class="auctionDetail_c_two_left_two_right_one_thre_chujiass"
                        >
                          提交出价
                        </div>
                      </div>
                      <div
                        v-if="biaodInfo.pmh_type == 2"
                        class="auctionDetail_c_two_left_two_right_one_thre_chujia_bottom"
                      >
                        <div @click="jianjia(jiajiadi)">-{{ jiajiadi }}</div>
                        <div @click="jianjia(jiajiazhong)">
                          -{{ jiajiazhong }}
                        </div>
                        <div @click="jianjia(jiajiagao)">-{{ jiajiagao }}</div>
                      </div>
                      <div
                        v-if="biaodInfo.pmh_type == 3"
                        class="auctionDetail_c_two_left_two_right_one_thre_chujia"
                      >
                        <div
                          class="auctionDetail_c_two_left_two_right_one_thre_chujias"
                        >
                          <input v-model="jiage" type="number" />
                        </div>
                        <div
                          @click="mangpaiChu"
                          class="auctionDetail_c_two_left_two_right_one_thre_chujiass"
                        >
                          提交出价
                        </div>
                      </div>
                      <div
                        v-if="biaodInfo.pmh_type == 5"
                        class="auctionDetail_c_two_left_two_right_one_thre_chujia"
                      >
                        <div
                          class="auctionDetail_c_two_left_two_right_one_thre_chujias"
                        >
                          <input v-model="jiage" type="number" />
                        </div>
                        <div
                          @click="mangpaiChuZhiding"
                          class="auctionDetail_c_two_left_two_right_one_thre_chujiass"
                        >
                          提交出价
                        </div>
                      </div>
                    </template>
                    <template v-if="biaodInfo.bd_status == 5">
                      <div class="auction-finish">
                        <div
                          v-if="biaodInfo.bd_status == 5"
                          class="auctionDetail_c_two_left_two_right_one_thre_four"
                        >
                          <SvgIcon
                            iconName="auctionDetail-warning"
                            className="warning-icon"
                          />

                          <div
                            v-if="
                              baomingIndex == 1 &&
                              String(jingpaihaopai) === String(bd_jdnum)
                            "
                          >
                            恭喜您已中标，请买受人凭《成交通知书》联系拍卖企业支付尾款并办理交割！
                          </div>
                          <div
                            v-else-if="
                              baomingIndex == 1 &&
                              String(jingpaihaopai) !== String(bd_jdnum)
                            "
                          >
                            非常遗憾您本次未中标，期待下次您的参与，谢谢！
                          </div>
                          <div v-else>
                            请买受人联系拍卖公司支付尾款并办理交割！
                          </div>
                        </div>
                        <div
                          class="auctionDetail_c_two_left_two_right_one_thre_fours"
                        >
                          <div class="other-button">标的已成交</div>
                          <div
                            @click="nextBIaodi"
                            v-if="
                              biaodInfo.id !=
                                biaodList[biaodList.length - 1].id &&
                              biaodList.length > 1 &&
                              biaodInfo.pmh_status != 2
                            "
                            class="other-button"
                          >
                            进入下个标的
                          </div>
                        </div>
                      </div>
                    </template>
                    <template v-if="biaodInfo.bd_status == 4">
                      <div class="auction-finish">
                        <div
                          v-if="biaodInfo.bd_status == 4"
                          class="auctionDetail_c_two_left_two_right_one_thre_four"
                        >
                          <SvgIcon
                            iconName="auctionDetail-warning"
                            className="warning-icon"
                          />
                          <div>因无人出价或出价未达到保留价，标的已流拍！</div>
                        </div>
                        <div
                          class="auctionDetail_c_two_left_two_right_one_thre_fours"
                        >
                          <div class="other-button">标的已流拍</div>
                          <div
                            @click="nextBIaodi"
                            v-if="
                              biaodInfo.id !=
                                biaodList[biaodList.length - 1].id &&
                              biaodList.length > 1 &&
                              biaodInfo.pmh_status != 2
                            "
                            class="other-button"
                          >
                            进入下个标的
                          </div>
                        </div>
                      </div>
                    </template>
                    <template v-if="biaodInfo.bd_status == 8">
                      <div
                        class="auctionDetail_c_two_left_two_right_one_thre_fours"
                      >
                        <div class="other-button">标的已撤拍</div>
                      </div>
                    </template>
                    <template
                      v-if="
                        isPaimaishi &&
                        (biaodInfo.bd_status == 2 ||
                          biaodInfo.bd_status == 3) &&
                        baomingIndex == 1
                      "
                    >
                      <div
                        class="auctionDetail_c_two_left_two_right_one_thre_fours"
                      >
                        <div class="other-button">等待拍卖师处理</div>
                      </div>
                    </template>
                    <template
                      v-if="biaodInfo.bd_status == 7 && baomingIndex != 0"
                    >
                      <div
                        class="auctionDetail_c_two_left_two_right_one_thre_fours"
                      >
                        <div class="other-button">已暂停竞价等待拍卖师处理</div>
                      </div>
                    </template>
                    <!-- 报名提示和按钮左右布局容器 -->
                    <div
                      v-if="
                        baomingIndex == -1 &&
                        biaodInfo.bd_status != 4 &&
                        biaodInfo.bd_status != 5 &&
                        biaodInfo.bd_status != 6 &&
                        biaodInfo.bd_status != 8
                      "
                      class="registration-tip-row"
                    >
                      <!-- 左侧提示信息区域 -->
                      <div class="tip-section">
                        <!-- 佣金说明 -->
                        <div class="tip-item">
                          <SvgIcon
                            iconName="auctionDetail-warning"
                            className="warning-icon"
                          />
                          <div>{{ biaodInfo.yongjin }}</div>
                        </div>
                        <!-- 个人或企业认证提示 -->
                        <div class="tip-item">
                          <SvgIcon
                            iconName="auctionDetail-warning"
                            className="warning-icon"
                          />
                          <div>
                            个人或企业认证审核成功后方可报名标的，先报名标的交保证金后再出价
                          </div>
                        </div>
                      </div>

                      <!-- 右侧按钮区域 -->
                      <div class="button-section">
                        <!-- 未登录状态 -->
                        <template v-if="!userInfo">
                          <div
                            class="auctionDetail_c_two_left_two_right_one_thre_two login-button-container"
                          >
                            <div @click="goLogin">请先登录</div>
                          </div>
                        </template>

                        <!-- 未报名状态 -->
                        <template v-if="userInfo && !isBao">
                          <div
                            class="auctionDetail_c_two_left_two_right_one_thre_two"
                          >
                            <div @click="baominxieyi" class="apply">
                              <SvgIcon
                                iconName="auctionDetail-apply"
                                className="apply-icon"
                              />
                              报名标的
                            </div>
                          </div>
                        </template>

                        <!-- 报名选择状态 -->
                        <template v-if="userInfo && isBao">
                          <div class="apply-cancel">
                            <div
                              class="auctionDetail_c_two_left_two_right_one_thre_two"
                            >
                              <div @click="gerenBaoming" style="width: 90px">
                                个人报名
                              </div>
                              <div
                                @click="qiyeBaoming"
                                style="margin-left: 5px; width: 90px"
                              >
                                企业报名
                              </div>
                            </div>
                            <div
                              @click="isBao = false"
                              class="auctionDetail_c_two_left_two_right_one_thre_three"
                            >
                              取消
                            </div>
                          </div>
                        </template>
                      </div>
                    </div>
                    <div
                      v-if="biaodInfo.bd_status == 8"
                      class="auctionDetail_c_two_left_two_right_one_thre_four"
                    >
                      <SvgIcon
                        iconName="auctionDetail-warning"
                        className="warning-icon"
                      />
                      <div>撤拍理由：其他</div>
                    </div>
                  </div>
                  <div class="auctionDetail_c_two_left_two_right_onefour">
                    <div>
                      起拍价：<span
                        >{{ biaodInfo.bd_qipaijia
                        }}{{ biaodInfo.qpj_danwie }}</span
                      >
                    </div>
                    <div>
                      保证金：<span>{{ biaodInfo.bd_baozhengjin }}元</span>
                    </div>
                    <div>
                      评估价：<span>{{ biaodInfo.bd_pinggujiage }}</span>
                    </div>
                    <div>
                      保留价：<span>{{ biaodInfo.bd_baoliujia }}</span>
                    </div>
                    <div>
                      加价幅度：<span>{{ biaodInfo.bd_jiajiafudu }}元</span>
                    </div>
                    <div>
                      数量：<span>{{ biaodInfo.bd_num }}</span>
                    </div>
                    <div>
                      单位：<span>{{ biaodInfo.bd_danwei }}</span>
                    </div>
                    <div>
                      拍卖师：<span>{{ biaodInfo.pms_name }}</span>
                    </div>
                    <div>
                      拍卖师证件号：<span>{{ biaodInfo.pms_card }}</span>
                    </div>
                    <div>
                      所在地：<span
                        >{{ areaNames.fullAddress
                        }}{{ biaodInfo.address }}</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 详细信息导航 -->
            <div class="tabs-section">
              <!-- 固定导航栏 -->
              <div
                class="tabs-header"
                ref="tabsHeaderRef"
                :class="{ 'tabs-header-fixed': isTabsFixed }"
                :style="tabsHeaderStyle"
              >
                <div
                  class="tab-item"
                  :class="{ active: activeTab === 'announcement' }"
                  @click="scrollToSection('announcement')"
                >
                  <span>竞价公告</span>
                </div>
                <div
                  class="tab-item"
                  :class="{ active: activeTab === 'introduction' }"
                  @click="scrollToSection('introduction')"
                >
                  <span>标的介绍</span>
                </div>
                <div
                  class="tab-item"
                  :class="{ active: activeTab === 'statement' }"
                  @click="scrollToSection('statement')"
                >
                  <span>重要声明</span>
                </div>
                <div
                  class="tab-item"
                  :class="{ active: activeTab === 'records' }"
                  @click="scrollToSection('records')"
                >
                  <span>竞价记录({{ chujiailist.length }})</span>
                </div>
                <div
                  class="tab-item"
                  :class="{ active: activeTab === 'notice' }"
                  @click="scrollToSection('notice')"
                >
                  <span>竞价须知</span>
                </div>
              </div>

              <!-- 占位元素，防止固定定位时内容跳动 -->
              <div v-if="isTabsFixed" class="tabs-header-placeholder"></div>

              <!-- 内容区域 -->
              <div class="tabs-content" ref="tabsContentRef">
                <!-- 竞价公告内容 -->
                <div
                  class="content-section"
                  id="announcement"
                  ref="announcementRef"
                >
                  <div class="section-content">
                    <span class="section-title" ref="announcementTitleRef">
                      竞价公告：
                    </span>
                    <div
                      class="section-body"
                      v-html="biaodInfo.pmh_gonggao || ''"
                    ></div>
                  </div>
                </div>

                <!-- 标的介绍内容 -->
                <div
                  class="content-section"
                  id="introduction"
                  ref="introductionRef"
                >
                  <div class="section-content">
                    <span class="section-title" ref="introductionTitleRef">
                      标的介绍
                    </span>
                    <div
                      class="section-body"
                      v-html="biaodInfo.bd_jieshao || ''"
                    ></div>
                    <div class="section-images">
                      <el-image
                        v-for="(item, i) in biaodiImgs"
                        :key="i"
                        :src="url + item"
                        alt=""
                        fit="cover"
                        :preview-src-list="previewImgs"
                      />
                    </div>
                  </div>
                </div>

                <!-- 重要声明内容 -->
                <div class="content-section" id="statement" ref="statementRef">
                  <div class="section-content">
                    <span class="section-title" ref="statementTitleRef">
                      重要声明
                    </span>
                    <div
                      class="section-body"
                      v-html="biaodInfo.pmh_shengming || ''"
                    ></div>
                  </div>
                </div>

                <!-- 竞价记录内容 -->
                <div class="content-section" id="records" ref="recordsRef">
                  <div class="section-content">
                    <span class="section-title" ref="recordsTitleRef">
                      竞价记录({{ chujiailist.length }})
                    </span>
                    <div class="section-body">
                      <table class="chujialist">
                        <tbody>
                          <tr>
                            <td>状态</td>
                            <td>时间</td>
                            <td>编号</td>
                            <td>价格</td>
                          </tr>
                          <tr v-for="(item, i) in chujiailist" :key="i">
                            <template v-if="i == 0">
                              <td style="color: #ff0000; font-size: 13px">
                                领先
                              </td>
                              <td style="color: #ff0000; font-size: 13px">
                                {{ formatDate(item.addtime) }}
                              </td>
                              <td style="color: #ff0000; font-size: 13px">
                                {{ item.jingpaihaopai }}
                              </td>
                              <td style="color: #ff0000; font-size: 13px">
                                {{ item.dangqianjia }}
                              </td>
                            </template>
                            <template v-else>
                              <td style="font-size: 13px">出局</td>
                              <td style="font-size: 13px">
                                {{ formatDate(item.addtime) }}
                              </td>
                              <td style="font-size: 13px">
                                {{ item.jingpaihaopai }}
                              </td>
                              <td style="font-size: 13px">
                                {{ item.dangqianjia }}
                              </td>
                            </template>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>

                <!-- 竞价须知内容 -->
                <div class="content-section" id="notice" ref="noticeRef">
                  <div class="section-content">
                    <span class="section-title" ref="noticeTitleRef"
                      >竞价须知</span
                    >
                    <div
                      class="section-body"
                      v-html="biaodInfo.pmh_xuzhi || ''"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div
            class="auctionDetail_c_two_right"
            ref="rightPanelRef"
            :style="rightPanelStyle"
          >
            <div class="auctionDetail_c_two_right_a">
              <div class="auctionDetail_c_two_right_a_a">拍卖师发言</div>
              <div class="auctionDetail_c_two_right_a_b">
                <LocalScrollbar>
                  <div v-for="(item, i) in fayanList">
                    <div class="auctionDetail_c_two_right_a_b_div">
                      <div>{{ item.type_name }}</div>
                      <div>{{ item.addtime_name }}</div>
                    </div>
                    <div class="auctionDetail_c_two_right_a_b_divs">
                      {{ item.content }}
                    </div>
                  </div>
                </LocalScrollbar>
              </div>
            </div>
            <div class="auctionDetail_c_two_right_b">
              <div class="auctionDetail_c_two_right_b_title">
                <div class="auctionDetail_c_two_right_b_titlea">状态</div>
                <div class="auctionDetail_c_two_right_b_titleb">出价人</div>
                <div class="auctionDetail_c_two_right_b_titlec">价格</div>
              </div>
              <div class="auctionDetail_c_two_right_b_titles">
                <LocalScrollbar>
                  <div
                    v-for="(item, i) in chujiailist"
                    class="auctionDetail_c_two_right_b_title"
                    style="background: none; color: #000000"
                  >
                    <template v-if="i == 0">
                      <!-- <img
                        class="auctionDetail_c_two_right_b_title_img"
                        src="../assets/auction/hongqi.png"
                      /> -->
                      <div
                        class="auctionDetail_c_two_right_b_titlea"
                        style="color: #ff0000"
                      >
                        领先
                      </div>
                      <div
                        class="auctionDetail_c_two_right_b_titleb"
                        style="color: #ff0000"
                      >
                        {{ item.jingpaihaopai }}
                      </div>
                      <div
                        class="auctionDetail_c_two_right_b_titlec"
                        style="color: #ff0000"
                      >
                        {{ item.dangqianjia }}
                      </div>
                    </template>
                    <template v-else>
                      <div class="auctionDetail_c_two_right_b_titlea">出局</div>
                      <div class="auctionDetail_c_two_right_b_titleb">
                        {{ item.jingpaihaopai }}
                      </div>
                      <div class="auctionDetail_c_two_right_b_titlec">
                        {{ item.dangqianjia }}
                      </div>
                    </template>
                  </div>
                </LocalScrollbar>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- <div class="auctionDetail_c_three">
        <div class="auctionDetail_c_three_one">
          <div
            @click="tabIndex = 2"
            :class="
              tabIndex == 2
                ? 'auctionDetail_c_three_oness'
                : 'auctionDetail_c_three_ones'
            "
          >
            竞价公告
          </div>
          <div
            @click="tabIndex = 4"
            :class="
              tabIndex == 4
                ? 'auctionDetail_c_three_oness'
                : 'auctionDetail_c_three_ones'
            "
          >
            标的介绍
          </div>
          <div
            @click="tabIndex = 0"
            :class="
              tabIndex == 0
                ? 'auctionDetail_c_three_oness'
                : 'auctionDetail_c_three_ones'
            "
          >
            重要声明
          </div>
          <div
            @click="tabIndex = 1"
            :class="
              tabIndex == 1
                ? 'auctionDetail_c_three_oness'
                : 'auctionDetail_c_three_ones'
            "
          >
            竞价记录({{ chujiailist.length }})
          </div>
          <div
            @click="tabIndex = 3"
            :class="
              tabIndex == 3
                ? 'auctionDetail_c_three_oness'
                : 'auctionDetail_c_three_ones'
            "
          >
            竞价须知
          </div>
        </div>
        <div
          v-if="tabIndex == 0"
          class="auctionDetail_c_three_two"
          v-html="biaodInfo.pmh_shengming || ''"
        ></div>
        <div v-if="tabIndex == 1" class="auctionDetail_c_three_two">
          <table class="chujialist">
            <tbody>
              <tr>
                <td>状态</td>
                <td>时间</td>
                <td>编号</td>
                <td>价格</td>
              </tr>
              <tr v-for="(item, i) in chujiailist">
                <template v-if="i == 0">
                  <td style="color: #ff0000; font-size: 13px">领先</td>
                  <td style="color: #ff0000; font-size: 13px">
                    {{ formatDate(item.addtime) }}
                  </td>
                  <td style="color: #ff0000; font-size: 13px">
                    {{ item.jingpaihaopai }}
                  </td>
                  <td style="color: #ff0000; font-size: 13px">
                    {{ item.dangqianjia }}
                  </td>
                </template>
                <template v-else>
                  <td style="font-size: 13px">出局</td>
                  <td style="font-size: 13px">
                    {{ formatDate(item.addtime) }}
                  </td>
                  <td style="font-size: 13px">{{ item.jingpaihaopai }}</td>
                  <td style="font-size: 13px">{{ item.dangqianjia }}</td>
                </template>
              </tr>
            </tbody>
          </table>
        </div>
        <div
          v-if="tabIndex == 2"
          class="auctionDetail_c_three_two"
          v-html="biaodInfo.pmh_gonggao || ''"
        ></div>
        <div
          v-if="tabIndex == 3"
          class="auctionDetail_c_three_two"
          v-html="biaodInfo.pmh_xuzhi || ''"
        ></div>
        <div
          v-if="tabIndex == 4"
          class="auctionDetail_c_three_two"
          style="padding-bottom: 10px"
          v-html="biaodInfo.bd_jieshao || ''"
        ></div>
        <div
          v-if="tabIndex == 4"
          class="auctionDetail_c_three_two"
          style="clear: both; text-align: center"
        >
          <img
            v-for="(item, i) in biaodiImgs"
            :src="url + item"
            alt=""
            style="max-width: 100%"
          />
        </div>
      </div> -->
    </div>

    <!-- <FootersBottom /> -->

    <!-- 协议弹窗组件 -->
    <AgreementModal
      v-model="showAgreementModal"
      :title="agreementTitle"
      :content="agreementContent"
      :countdown-time="6"
      :agreementType="agreementType"
      :userInfo="userInfo"
      :targetTitle="biaodInfo.pmh_name + '-' + biaodInfo.bd_title"
      @confirm="handleAgreementConfirm"
      @cancel="handleAgreementCancel"
    />

    <!-- 上传凭证弹窗组件 -->
    <UploadModal
      v-model="showUploadModal"
      :loading="uploadLoading"
      @confirm="handleUploadConfirm"
      @cancel="handleUploadCancel"
    />

    <!-- 音频播放元素 -->
    <audio ref="audioRef" preload="auto">
      <!-- <source src="/audio/notification.mp3" type="audio/mpeg">
      <source src="/audio/notification.wav" type="audio/wav"> -->
      您的浏览器不支持音频播放。
    </audio>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  computed,
  watch,
  onMounted,
  onUnmounted,
  nextTick,
} from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus"; // ElImage 由 unplugin-element-plus 自动引入
import { useUserStore } from "@/stores/user";
import { useWebSocket } from "@/composables/useWebSocket";
import SvgIcon from "@/components/SvgIcon.vue";
import LocalScrollbar from "@/components/LocalScrollbar.vue";
import AgreementModal from "./components/AgreementModal.vue";
import UploadModal from "./components/UploadModal.vue";
import { getAreaNamesByCodes } from "@/views/Profile/components/verification/areaUtils";
import {
  auctionApi,
  biddingApi,
  userApi,
  newsApi,
  companyApi,
} from "../../utils/api";
import type {
  BiaodItem,
  ChujiaiItem,
  FayanItem,
  CountdownInfo,
  ShoukuanInfo,
  BiaodInfo,
} from "../../types/auctionDetail";

// 定义组件名称
defineOptions({
  name: "AuctionDetail",
});

// 路由实例
const router = useRouter();
const route = useRoute();

// 用户store
const userStore = useUserStore();

// 获取路由参数
const pmhId = ref<number>(Number(route.query.pmhId) || 0);
const targetId = ref<number>(Number(route.query.id) || 0);

// 计算属性：判断是否登录
const isLoggedIn = computed(() => userStore.isLoggedIn);

// 响应式数据定义
// 标的是否收藏
const isShoucang = ref<number>(0);
// 拍卖id
const paimaihuiId = ref<number>(1);

// 图片放大镜相关变量
const isImageHovered = ref<boolean>(false);
const maskSize = 150; // 蒙层尺寸
const maskPosition = ref<{
  x: number;
  y: number;
  translateX: number;
  translateY: number;
}>({
  x: 0,
  y: 0,
  translateX: 0,
  translateY: 0,
});
const info = reactive<CountdownInfo>({
  dayDiff: "0",
  hours: "12",
  minutes: "30",
  seconds: "45",
});

// 出价相关数据
const jiage = ref<number>(101000); // 当前输入的价格
const dangqianjiaInit = ref<number>(100000); // 当前最高价
const jiajiadi = ref<number>(1000); // 低加价幅度
const jiajiazhong = ref<number>(2000); // 中加价幅度
const jiajiagao = ref<number>(3000); // 高加价幅度

// 报名相关数据 - 模拟登录后的状态
const baomingIndex = ref<number>(-1); // -1未报名 0审核中 1已通过
const kanhuoIndex = ref<number>(-1); // -1未看货 0未看货 1已看货
const zhengming = ref<string>(""); // 缴纳凭证
const bm_id = ref<number>(0); // 报名ID
const shoukuanInfo = ref<ShoukuanInfo | null>(null); // 收款信息
const jingpaihaopai = ref<string>(""); // 竞拍号牌
const jingpaihaopais = ref<string>(""); // 竞拍号牌前缀
const isBao = ref<boolean>(false); // 是否显示报名选择
const isTeshu = ref<boolean>(true); // 是否有权限查看

// 协议弹窗相关
const showAgreementModal = ref<boolean>(false); // 是否显示协议弹窗
const agreementContent = ref<string>(""); // 协议内容
const agreementTitle = ref<string>(""); // 协议标题
const agreementType = ref<string>(""); // 协议类型：'registration' 报名协议，'viewing' 看货协议

// 上传凭证弹窗相关
const showUploadModal = ref<boolean>(false); // 是否显示上传凭证弹窗
const uploadLoading = ref<boolean>(false); // 上传加载状态

// WebSocket 相关功能
const {
  websock,
  isOnline,
  audioRef,
  initWebSocket,
  websocketsend,
  closeWebSocket,
} = useWebSocket();

// 用户信息 - 根据登录状态动态获取
const userInfo = computed(() => {
  if (isLoggedIn.value) {
    return userStore.userInfo;
  }
  return null;
});
const bd_jdnum = ref<number>(0); // 竞价人数
const mangpaichujiaLnajie = ref<number>(0); // 盲拍出价临界值
const isChuMangNum = ref<number>(0); // 盲拍出价次数

const slideIndex = ref<number>(0);
const tabIndex = ref<number>(2);
const biaodiId = ref<number>(1);

// 导航栏相关变量
const activeTab = ref<string>("announcement"); // 当前激活的导航项
const isTabsFixed = ref<boolean>(false); // 导航栏是否固定
const tabsHeaderRef = ref<HTMLElement | null>(null); // 导航栏DOM引用
const tabsHeaderOriginalTop = ref<number>(0); // 导航栏原始位置
const containerRef = ref<HTMLElement | null>(null);
const tabsHeaderStyle = ref<Record<string, string>>({});

// 右侧面板固定定位相关变量
const rightPanelRef = ref<HTMLElement | null>(null); // 右侧面板DOM引用
const isRightPanelFixed = ref<boolean>(false); // 右侧面板是否固定
const rightPanelOriginalTop = ref<number>(0); // 右侧面板原始位置
const rightPanelStyle = ref<Record<string, string>>({});

function updateTabsHeaderStyle() {
  if (isTabsFixed.value && containerRef.value) {
    const rect = containerRef.value.getBoundingClientRect();
    tabsHeaderStyle.value = {
      position: "fixed",
      top: "92px",
      left: rect.left + "px",
      width: rect.width - 1 + "px",
      zIndex: "1001",
      background: "#fff",
    };
  } else {
    tabsHeaderStyle.value = {};
  }
}

/**
 * 更新右侧面板样式
 */
function updateRightPanelStyle() {
  if (isRightPanelFixed.value && rightPanelRef.value) {
    const rect = rightPanelRef.value.getBoundingClientRect();
    rightPanelStyle.value = {
      position: "fixed",
      top: "126px", // 距离顶部20px
      right: "calc((100vw - 1280px) / 2)", // 保持在原来的右侧位置
      width: "306px",
      marginLeft: "20px",

      zIndex: "999",
    };
  } else {
    rightPanelStyle.value = {};
  }
}

onMounted(() => {
  window.addEventListener("resize", updateTabsHeaderStyle);
  watch(isTabsFixed, () => {
    nextTick(updateTabsHeaderStyle);
  });
});
onUnmounted(() => {
  window.removeEventListener("resize", updateTabsHeaderStyle);
});

// 标的信息
const biaodInfo = reactive<BiaodInfo>({
  id: 0,
  pmh_id: 0,
  pmh_name: "",
  pmh_xingshi: 2,
  pmh_type: 1,
  pmh_status: 1,
  qiyemingcheng: "",
  qiyephone: "",
  bd_title: "",
  bd_status: 1,
  bd_qipaijia: "0",
  qpj_danwie: "元",
  bd_baozhengjin: "0",
  bd_pinggujiage: "0元",
  bd_baoliujia: "0元",
  bd_jiajiafudu: "0",
  bd_num: "0",
  bd_danwei: "件",
  pms_name: "",
  pms_card: "",
  address: "",
  bd_weiguan: 0,
  pmh_shengming: "",
  pmh_gonggao: "",
  pmh_xuzhi: "",
  bd_jieshao: "",
  yongjin: "",
  parentid: 0,
  province: 0,
  city: 0,
  district: 0,
  sheng_time_name: "", // 开始时间
  end_time_name: "", // 结束时间
});

// 标的图片列表
const biaodiImgs = ref<string[]>([]);

// 计算属性：将省市区代码转换为名称
const areaNames = computed(() => {
  // 如果省市区代码都存在且不为0，则进行转换
  if (biaodInfo.province && biaodInfo.city && biaodInfo.district) {
    const names = getAreaNamesByCodes(
      String(biaodInfo.province),
      String(biaodInfo.city),
      String(biaodInfo.district)
    );

    if (names) {
      return {
        provinceName: names[0], // 省份名称
        cityName: names[1], // 城市名称
        districtName: names[2], // 区县名称
        fullAddress: names.join(""), // 完整地址
      };
    }
  }

  // 如果转换失败或代码为空，返回默认值
  return {
    provinceName: "",
    cityName: "",
    districtName: "",
    fullAddress: "",
  };
});

// 计算属性：将标的图片地址转化为预览图片地址
const previewImgs = computed(() => {
  return biaodiImgs.value.map((item) => {
    return url.value + item;
  });
});

// 获取标的详情
const getBiaodDetail = async () => {
  try {
    const response = await auctionApi.getUnauditedAuctionDetail({
      id: targetId.value.toString(),
    });

    if (response.code === 1 && response.data) {
      const data = response.data;

      // 如果返回数据中包含 pmh_id，更新 pmhId
      if (data.pmh_id) {
        pmhId.value = data.pmh_id;
      }

      // 更新标的信息
      Object.assign(biaodInfo, {
        id: data.id,
        pmh_id: data.pmh_id || 0,
        pmh_name: data.pmh_name || "",
        pmh_xingshi: data.pmh_xingshi || 2,
        pmh_type: data.pmh_type || 1,
        pmh_status: data.pmh_status || 1,
        qiyemingcheng: data.qiyemingcheng || "",
        qiyephone: data.qiyephone || "",
        bd_title: data.bd_title || "",
        bd_status: data.bd_status || 1,
        bd_qipaijia: data.bd_qipaijia || "0",
        qpj_danwie: data.qpj_danwie || "元",
        bd_baozhengjin: data.bd_baozhengjin || "0",
        bd_pinggujiage: data.bd_pinggujiage || "0元",
        bd_baoliujia: data.bd_baoliujia || "0元",
        bd_jiajiafudu: data.bd_jiajiafudu || "0",
        bd_num: data.bd_num || "0",
        bd_danwei: data.bd_danwei || "件",
        pms_name: data.pms_name || "",
        pms_card: data.pms_card || "",
        address: data.address || "",
        bd_weiguan: data.bd_weiguan || 0,
        pmh_shengming: data.pmh_shengming || "",
        pmh_gonggao: data.pmh_gonggao || "",
        pmh_xuzhi: data.pmh_xuzhi || "",
        bd_jieshao: data.bd_jieshao || "",
        yongjin: data.yongjin || "",
        sheng_time: data.sheng_time || "", // 开始时间
        end_time: data.end_time || "", // 结束时间
        parentid: data.parentid || 0,
        province: data.province || 0,
        city: data.city || 0,
        district: data.district || 0,
        sheng_time_name: data.sheng_time_name || "", // 开始时间
        end_time_name: data.end_time_name || "", // 结束时间
      });

      // 启动倒计时
      startCountdown();

      // 更新图片列表
      if (data.bd_images) {
        biaodiImgs.value = data.bd_images.split(",");
      }

      // 初始化出价相关数据
      jiage.value = parseFloat(data.bd_qipaijia || "0");
      dangqianjiaInit.value = parseFloat(data.bd_qipaijia || "0");
      jiajiadi.value = parseFloat(data.bd_jiajiafudu || "0");
    }
  } catch (error) {
    console.error("获取标的详情失败:", error);
    ElMessage.error("获取标的详情失败");
  }
};

// 格式化时间 年/月/日
const formatEvent = (date: string): Array<string> => {
  const dateArr = date.split(" ");
  const dateArr1 = dateArr[0].split("-");
  const dateArr2 = dateArr[1].split(":");
  const newArr = [...dateArr1, ...dateArr2];

  return newArr;
};

const biaodiImgsIndex = ref<number>(0);
const url = ref<string>("https://huigupaimai.oss-cn-beijing.aliyuncs.com/");

// 水平图片列表滚动相关变量
const imageListHorizontalRef = ref<HTMLElement | null>(null);
const canScrollLeft = ref<boolean>(false);
const canScrollRight = ref<boolean>(false);

// 自动滚动相关变量
const autoScrollTimer = ref<number | null>(null);
const isAutoScrolling = ref<boolean>(false);

// 出价记录列表
const chujiailist = ref<ChujiaiItem[]>([]);

// 获取出价记录
const getChujiaiList = async () => {
  try {
    const response = await biddingApi.getBidRecords({
      bd_id: targetId.value,
      page: 1,
    });

    if (response.code === 1 && response.data) {
      chujiailist.value = response.data;
    }
  } catch (error) {
    console.error("获取出价记录失败:", error);
  }
};

// 发言列表
const fayanList = ref<FayanItem[]>([]);

// 获取发言记录
const getFayanList = async () => {
  try {
    const response = await biddingApi.getSpeechRecords({
      bd_id: targetId.value,
      pmh_id: pmhId.value,
      page: 1,
    });

    if (response.code === 1 && response.data) {
      fayanList.value = response.data;
    }
  } catch (error) {
    console.error("获取发言记录失败:", error);
  }
};

// 标的列表
const biaodList = ref<BiaodItem[]>([]);

// 获取竞价会标的列表
const getBiaodList = async () => {
  try {
    const response = await auctionApi.getUnauditedAuctionSessionList({
      pmh_id: pmhId.value,
    });

    if (response.code === 1 && response.data) {
      biaodList.value = response.data;
    }
  } catch (error) {
    console.error("获取标的列表失败:", error);
  }
};

const biaodListIndex = ref<number>(0);
const biaodListIndexs = ref<number>(0);
const isPaimaishi = ref<boolean>(false);

// 格式化日期函数（替代Vue2的filters）
const formatDate = (value: number): string => {
  value = value * 1000;
  const date = new Date(value);
  const y = date.getFullYear();
  let MM: string | number = date.getMonth() + 1;
  MM = MM < 10 ? "0" + MM : MM;
  let d: string | number = date.getDate();
  d = d < 10 ? "0" + d : d;
  let h: string | number = date.getHours();
  h = h < 10 ? "0" + h : h;
  let m: string | number = date.getMinutes();
  m = m < 10 ? "0" + m : m;
  let s: string | number = date.getSeconds();
  s = s < 10 ? "0" + s : s;
  return y + "-" + MM + "-" + d + " " + h + ":" + m + ":" + s;
};

// 方法定义

// WebSocket 相关方法
/**
 * 获取WebSocket心跳数据
 * @returns 心跳数据对象
 */
const getHeartbeatData = (): { fayanId: number; chujiaiId: number } => {
  return {
    fayanId: fayanList.value.length === 0 ? 0 : fayanList.value[0].id,
    chujiaiId: chujiailist.value.length === 0 ? 0 : chujiailist.value[0].id,
  };
};

/**
 * WebSocket消息处理回调函数
 * @param data WebSocket接收到的消息数据
 */
const handleWebSocketMessage = (data: any): void => {
  try {
    // console.log("WebSocket收到消息:", data);

    if (data) {
      // 处理盲拍类型（pmh_type == 4）
      if (biaodInfo.pmh_type == 4) {
        if (biaodInfo.bd_status !== data.bd_info.bd_status) {
          // 标的状态发生变化，重新获取标的信息
          getBiaodDetail();
        }
      }

      // 处理一次性竞价类型（pmh_type == 3 || pmh_type == 5）
      if (biaodInfo.pmh_type == 3 || biaodInfo.pmh_type == 5) {
        if (isChuMangNum.value < data.bd_info.lunshu) {
          // 轮数发生变化，重新获取出价记录
          getChujiaiList();
        }

        if (biaodInfo.bd_status !== data.bd_info.bd_status) {
          // 标的状态发生变化，重新获取出价记录
          getChujiaiList();
        }

        isChuMangNum.value = data.bd_info.lunshu;
      }

      // 更新标的状态
      biaodInfo.bd_status = data.bd_info.bd_status;

      // 更新时间信息（非结束状态）
      if (
        data.bd_info.bd_status != 4 &&
        data.bd_info.bd_status != 5 &&
        data.bd_info.bd_status != 8
      ) {
        biaodInfo.end_time = data.fuwutime;
        biaodInfo.sheng_time = data.bd_info.sheng_time;
      }

      // 处理发言消息
      if (data.fayan) {
        // 更新发言列表
        data.fayan.forEach((el: any) => {
          fayanList.value.unshift(el);
        });
      }

      // 处理出价消息
      if (data.chujia) {
        // 更新出价列表
        if (chujiailist.value === null || chujiailist.value.length === 0) {
          chujiailist.value = [];
        }

        data.chujia.forEach((el: any) => {
          chujiailist.value.unshift(el);
          // 更新当前价格（非盲拍类型）
          if (biaodInfo.pmh_type !== 3 && biaodInfo.pmh_type !== 5) {
            dangqianjiaInit.value = parseFloat(el.dangqianjia);
          }
        });
      }
    }
  } catch (error) {
    console.error("WebSocket消息处理失败:", error);
  }
};

/**
 * 跳转到企业页面
 */
const goQiye = (): void => {
  // console.log("跳转到企业页面");
};

/**
 * 标的列表左滑动
 */
const oneTitleLeftClick = (): void => {
  if (biaodListIndex.value > -4) {
    biaodListIndex.value--;
  }
};

/**
 * 标的列表右滑动
 */
const oneTitlerightClick = (): void => {
  if (biaodListIndex.value < 0) {
    biaodListIndex.value++;
  }
};

/**
 * 设置标的信息
 * @param item 标的项目
 */
const setBiaodiId = (item: BiaodItem): void => {
  // 更新当前标的ID和竞价会ID
  targetId.value = item.id;
  pmhId.value = item.pmh_id;

  // 点击跳转当前页面
  router.replace({
    name: "auctionDetail",
    query: { id: item.id, pmhId: item.pmh_id },
  });

  // 重新获取页面数据
  initPageData();
};

/**
 * 标的列表左滑动（第二个）
 */
const oneTitleLeftClicks = (): void => {
  if (biaodListIndexs.value > -2) {
    biaodListIndexs.value--;
  }
};

/**
 * 标的列表右滑动（第二个）
 */
const oneTitlerightClicks = (): void => {
  if (biaodListIndexs.value < 0) {
    biaodListIndexs.value++;
  }
};

/**
 * 设置图片索引
 * @param index 图片索引
 */
const setIndex = (index: number): void => {
  biaodiImgsIndex.value = index;
};

/**
 * 标的收藏点击事件
 * @param type 收藏类型 0收藏 1取消收藏
 */
const biaodishoucangClick = async (type: number): Promise<void> => {
  // console.log("type", type);

  try {
    const response = await auctionApi.favoriteTarget({
      member_id: userInfo.value ? userInfo.value.id : 0,
      bd_id: targetId.value,
      isquxiao: type,
    });

    if (response.code === 1) {
      isShoucang.value = type === 0 ? 0 : 1; // 收藏状态：0已收藏，1未收藏

      ElMessage.success(response.data);
    } else {
      ElMessage.error(
        response.msg || (type === 0 ? "收藏失败" : "取消收藏失败")
      );
    }
  } catch (error) {
    console.error("收藏操作失败:", error);
    ElMessage.error(type === 0 ? "收藏失败" : "取消收藏失败");
  }
};

/**
 * 图片鼠标移入处理
 */
const handleImageMouseEnter = (): void => {
  isImageHovered.value = true;
};

/**
 * 图片鼠标移出处理
 */
const handleImageMouseLeave = (): void => {
  isImageHovered.value = false;
  maskPosition.value = { x: 0, y: 0, translateX: 0, translateY: 0 };
};

/**
 * 图片鼠标移动处理
 */
const handleImageMouseMove = (event: MouseEvent): void => {
  if (!isImageHovered.value) return;

  const imageContainer = event.currentTarget as HTMLElement;
  const rect = imageContainer.getBoundingClientRect();

  // 获取鼠标在图片容器内的相对位置
  let mouseX = event.clientX - rect.left;
  let mouseY = event.clientY - rect.top;

  // 计算蒙层左上角位置（以鼠标为中心，减去蒙层一半尺寸）
  let maskX = mouseX - maskSize / 2;
  let maskY = mouseY - maskSize / 2;

  // 边界限制 - 确保蒙层不超出图片区域
  // 左上角限制
  maskX = Math.max(0, maskX);
  maskY = Math.max(0, maskY);

  // 右下角限制 - 确保蒙层不超出图片边界
  // 允许蒙层完全移动到边缘，这样可以确保放大区域能够完全覆盖
  const maxX = Math.max(0, rect.width - maskSize);
  const maxY = Math.max(0, rect.height - maskSize);
  maskX = Math.min(maskX, maxX);
  maskY = Math.min(maskY, maxY);

  // 计算放大图片应该显示的区域（基于蒙层中心点）
  const centerX = maskX + maskSize / 2;
  const centerY = maskY + maskSize / 2;

  // 为了确保放大区域始终被图片占满，我们需要计算实际的图片位移
  // 当蒙层在边界时，我们需要调整位移计算以避免空白区域
  const imageWidth = 412; // 原图容器宽度（与CSS中.auctionDetail_c_two_left_two_left_o的宽度一致）
  const imageHeight = 412; // 原图容器高度（与CSS中.auctionDetail_c_two_left_two_left_o的高度一致）
  const magnifiedWidth = 412; // 放大容器宽度（与CSS中.auctionDetail_c_two_left_two_left_omaskTopright的宽度一致）
  const magnifiedHeight = 412; // 放大容器高度（与CSS中.auctionDetail_c_two_left_two_left_omaskTopright的高度一致）
  const scaledImageWidth = 720; // 放大后图片的实际宽度（与CSS中.auctionDetail_c_two_left_two_left_omaskToprightrightImg的width一致）
  const scaledImageHeight = 720; // 放大后图片的实际高度

  // 计算放大图片的位移，确保显示正确的区域
  // 基于蒙层中心点在原图中的比例来计算放大图片的位移
  const ratioX = centerX / imageWidth;
  const ratioY = centerY / imageHeight;

  // 计算放大图片的位移
  let translateX = ratioX * scaledImageWidth - magnifiedWidth / 2;
  let translateY = ratioY * scaledImageHeight - magnifiedHeight / 2;

  // 限制位移范围，确保放大区域始终被图片占满，避免出现空白
  const maxTranslateX = scaledImageWidth - magnifiedWidth;
  const maxTranslateY = scaledImageHeight - magnifiedHeight;

  translateX = Math.max(0, Math.min(translateX, maxTranslateX));
  translateY = Math.max(0, Math.min(translateY, maxTranslateY));

  maskPosition.value = {
    x: maskX,
    y: maskY,
    translateX: translateX,
    translateY: translateY,
  };
};

/**
 * 返回拍卖列表页面
 */
const handleGoBack = (): void => {
  router.go(-1);
};

/**
 * 滚动到指定区域
 * @param sectionId 区域ID
 */
const scrollToSection = (sectionId: string): void => {
  activeTab.value = sectionId;
  const element = document.getElementById(sectionId);
  if (element && tabsHeaderRef.value) {
    const headerHeight = tabsHeaderRef.value.offsetHeight;
    const elementRect = element.getBoundingClientRect();
    const elementScrollTop =
      window.pageYOffset || document.documentElement.scrollTop;
    const elementOffsetTop = elementRect.top + elementScrollTop;

    // 计算滚动位置，考虑固定导航栏的高度
    const scrollTop = elementOffsetTop - headerHeight - 20;

    window.scrollTo({
      top: scrollTop,
      behavior: "smooth",
    });
  }
};

/**
 * 处理滚动事件
 */
const handleScroll = (): void => {
  if (!tabsHeaderRef.value) return;

  const currentScrollTop =
    window.pageYOffset || document.documentElement.scrollTop;

  // 处理导航栏吸顶效果
  // 当滚动位置超过导航栏原始位置时，启用固定定位
  if (currentScrollTop > tabsHeaderOriginalTop.value - 100) {
    isTabsFixed.value = true;
  } else {
    isTabsFixed.value = false;
  }

  // 处理右侧面板固定效果
  // 当滚动位置超过右侧面板原始位置时，启用固定定位
  if (
    rightPanelRef.value &&
    currentScrollTop >= rightPanelOriginalTop.value - 100
  ) {
    isRightPanelFixed.value = true;
    updateRightPanelStyle();
  } else {
    isRightPanelFixed.value = false;
    rightPanelStyle.value = {};
  }

  const headerHeight = tabsHeaderRef.value.offsetHeight;
  // 如果导航栏已经固定，需要考虑固定导航栏的高度偏移
  const extraOffset = isTabsFixed.value ? headerHeight : 0;
  const scrollTop = currentScrollTop + headerHeight + extraOffset + 50;

  // 更新激活的导航项
  const sections = [
    { id: "announcement", element: document.getElementById("announcement") },
    { id: "introduction", element: document.getElementById("introduction") },
    { id: "statement", element: document.getElementById("statement") },
    { id: "records", element: document.getElementById("records") },
    { id: "notice", element: document.getElementById("notice") },
  ];

  for (let i = sections.length - 1; i >= 0; i--) {
    const section = sections[i];
    if (section.element) {
      // 计算元素相对于页面的偏移量
      const elementRect = section.element.getBoundingClientRect();
      const elementScrollTop =
        window.pageYOffset || document.documentElement.scrollTop;
      const elementOffsetTop = elementRect.top + elementScrollTop;

      if (scrollTop >= elementOffsetTop) {
        activeTab.value = section.id;
        break;
      }
    }
  }
};

/**
 * 检查水平滚动状态
 */
const checkHorizontalScrollStatus = (): void => {
  if (imageListHorizontalRef.value) {
    const element = imageListHorizontalRef.value;

    // 确保元素已经渲染完成
    if (element.scrollWidth === 0 || element.clientWidth === 0) {
      // 如果元素尺寸为0，延迟重新检查
      setTimeout(() => {
        checkHorizontalScrollStatus();
      }, 50);
      return;
    }

    canScrollLeft.value = element.scrollLeft > 0;
    canScrollRight.value =
      element.scrollLeft < element.scrollWidth - element.clientWidth;

    // 调试信息
    // 调试信息已移除
  } else {
    // 如果元素不存在，重置状态
    canScrollLeft.value = false;
    canScrollRight.value = false;
  }
};

/**
 * 向左滚动
 */
const scrollLeft = (): void => {
  if (imageListHorizontalRef.value) {
    imageListHorizontalRef.value.scrollBy({
      left: -120, // 每次滚动120px
      behavior: "smooth",
    });
    setTimeout(checkHorizontalScrollStatus, 300);
  }
};

/**
 * 向右滚动
 */
const scrollRight = (): void => {
  if (imageListHorizontalRef.value) {
    imageListHorizontalRef.value.scrollBy({
      left: 120, // 每次滚动120px
      behavior: "smooth",
    });
    setTimeout(checkHorizontalScrollStatus, 300);
  }
};

/**
 * 自动向左滚动（只滚动一次）
 */
const autoScrollLeft = (): void => {
  if (
    !imageListHorizontalRef.value ||
    isAutoScrolling.value ||
    !canScrollLeft.value
  )
    return;

  isAutoScrolling.value = true;
  const scrollDistance = 120; // 每次滚动距离

  imageListHorizontalRef.value.scrollBy({
    left: -scrollDistance,
    behavior: "smooth",
  });

  // 滚动完成后重置状态
  setTimeout(() => {
    checkHorizontalScrollStatus();
    isAutoScrolling.value = false;
  }, 400);
};

/**
 * 自动向右滚动（只滚动一次）
 */
const autoScrollRight = (): void => {
  if (
    !imageListHorizontalRef.value ||
    isAutoScrolling.value ||
    !canScrollRight.value
  )
    return;

  isAutoScrolling.value = true;
  const scrollDistance = 120; // 每次滚动距离

  imageListHorizontalRef.value.scrollBy({
    left: scrollDistance,
    behavior: "smooth",
  });

  // 滚动完成后重置状态
  setTimeout(() => {
    checkHorizontalScrollStatus();
    isAutoScrolling.value = false;
  }, 400);
};

/**
 * 停止自动滚动
 */
const stopAutoScroll = (): void => {
  if (autoScrollTimer.value) {
    clearTimeout(autoScrollTimer.value);
    autoScrollTimer.value = null;
  }
  isAutoScrolling.value = false;
};

/**
 * 检查鼠标是否在滚动区域的边缘
 */
const isMouseAtScrollEdge = (
  mouseX: number
): { isLeftEdge: boolean; isRightEdge: boolean } => {
  if (!imageListHorizontalRef.value) {
    return { isLeftEdge: false, isRightEdge: false };
  }

  const container = imageListHorizontalRef.value;
  const containerRect = container.getBoundingClientRect();
  const containerLeft = containerRect.left;
  const containerRight = containerRect.right;
  const edgeThreshold = 80; // 边缘触发区域宽度

  // 计算鼠标相对于容器的位置
  const relativeX = mouseX - containerLeft;
  const containerWidth = containerRect.width;

  return {
    isLeftEdge: relativeX <= edgeThreshold && canScrollLeft.value,
    isRightEdge:
      relativeX >= containerWidth - edgeThreshold && canScrollRight.value,
  };
};

/**
 * 滚动区域鼠标移动事件处理
 */
const handleScrollAreaMouseMove = (event: MouseEvent): void => {
  // 停止之前的自动滚动
  stopAutoScroll();

  // 检查鼠标是否在滚动区域的边缘
  const { isLeftEdge, isRightEdge } = isMouseAtScrollEdge(event.clientX);

  // 开发环境调试信息已移除

  if (isLeftEdge) {
    // 鼠标在左边缘且可以向左滚动时，启动向左自动滚动
    // console.log("准备向左自动滚动..."); 已移除
    autoScrollTimer.value = window.setTimeout(() => {
      // console.log("开始向左自动滚动"); 已移除
      autoScrollLeft();
    }, 100);
  } else if (isRightEdge) {
    // 鼠标在右边缘且可以向右滚动时，启动向右自动滚动
    // console.log("准备向右自动滚动..."); 已移除
    autoScrollTimer.value = window.setTimeout(() => {
      // console.log("开始向右自动滚动"); 已移除
      autoScrollRight();
    }, 100);
  }
};

/**
 * 滚动区域鼠标移出事件处理
 */
const handleScrollAreaMouseLeave = (): void => {
  // 停止自动滚动
  stopAutoScroll();
};

/**
 * 登录跳转
 */
const goLogin = (): void => {
  router.push({
    path: "/Login",
    query: {
      isReject: "false",
    },
  });
};

/**
 * 报名协议 - 修改为设置协议类型
 */
const baominxieyi = async (): Promise<void> => {
  if (!userInfo.value) {
    ElMessage.error("请先登录");
    goLogin();
    return;
  }

  try {
    // 通过getNewsDetail接口获取报名协议内容
    const response = await newsApi.getNewsDetail({
      id: 165, // 报名协议的新闻ID
    });

    if (response.code === 1 && response.data) {
      // 显示报名协议弹窗
      agreementTitle.value = "报名协议";
      agreementContent.value =
        response.data.content || "<p>请仔细阅读报名协议内容...</p>";
      agreementType.value = "registration"; // 标识为报名协议
      showAgreementModal.value = true;
    } else {
      ElMessage.error("获取协议内容失败");
    }
  } catch (error) {
    console.error("获取报名协议失败:", error);
    ElMessage.error("获取协议内容失败，请稍后重试");
  }
};

// 开始协议倒计时
/**
 * 处理协议确认事件 - 修改为根据协议类型执行不同逻辑
 */
const handleAgreementConfirm = async (): Promise<void> => {
  if (agreementType.value === "registration") {
    // 报名协议确认
    isBao.value = true;
  } else if (agreementType.value === "viewing") {
    // 看货协议确认，调用看货API
    try {
      // 调试信息已移除

      const response = await auctionApi.viewTarget({
        bd_id: targetId.value,
        member_id: userInfo.value ? userInfo.value.id : 0,
        bm_id: bm_id.value, // 报名ID
        type: 1, // 看货类型，根据实际需求设置
      });

      if (response.code === 1) {
        ElMessage.success("看货确认成功");
        kanhuoIndex.value = 1; // 设置为已看货状态
      } else {
        ElMessage.error(response.msg || "看货确认失败");
      }
    } catch (error) {
      console.error("看货确认失败:", error);
      ElMessage.error("看货确认失败，请稍后重试");
    }
  }

  // 重置协议类型
  agreementType.value = "";
};

/**
 * 处理协议取消事件
 */
const handleAgreementCancel = (): void => {
  // 重置协议类型
  agreementType.value = "";
  // 协议取消时的处理逻辑
};

/**
 * 个人报名
 */
const gerenBaoming = async (): Promise<void> => {
  try {
    const response = await auctionApi.registerTarget({
      bd_id: targetId.value,
      member_id: userInfo.value ? userInfo.value.id : 0,
      type: 1, // 1表示个人报名
    });

    if (response.code === 1) {
      ElMessage.success("个人报名提交成功，请等待审核");
      isBao.value = false;
      baomingIndex.value = 0; // 设置为审核中状态

      // 可以添加定时检查审核状态的逻辑
      checkRegistrationStatus();
    } else {
      ElMessage.error(response.msg || "报名失败");
    }
  } catch (error) {
    console.error("个人报名失败:", error);
    ElMessage.error("报名失败，请稍后重试");
  }
};

/**
 * 企业报名
 */
const qiyeBaoming = async (): Promise<void> => {
  try {
    const response = await auctionApi.registerTarget({
      bd_id: targetId.value,
      member_id: userInfo.value ? userInfo.value.id : 0,
      type: 2, // 2表示企业报名
    });

    if (response.code === 1) {
      ElMessage.success("企业报名提交成功，请等待审核");
      isBao.value = false;
      baomingIndex.value = 0; // 设置为审核中状态

      // 可以添加定时检查审核状态的逻辑
      checkRegistrationStatus();
    } else {
      ElMessage.error(response.msg || "报名失败");
    }
  } catch (error) {
    console.error("企业报名失败:", error);
    ElMessage.error("报名失败，请稍后重试");
  }
};

// 检查报名状态
const checkRegistrationStatus = async () => {
  // 如果用户未登录，直接重置状态
  if (!userInfo.value) {
    baomingIndex.value = -1;
    kanhuoIndex.value = -1;
    zhengming.value = "";
    bm_id.value = 0;
    shoukuanInfo.value = null;
    return;
  }

  try {
    const response = await userApi.getRegistrationInfo({
      bd_id: targetId.value,
      member_id: userInfo.value.id,
      parentid: biaodInfo.parentid,
    });

    if (response.code === 1 && response.data) {
      const data = response.data;
      baomingIndex.value = data.status || 0;
      kanhuoIndex.value = data.kanhuo;
      zhengming.value = data.zhengming || "";
      bm_id.value = data.bm_id || 0;

      // 获取收款信息
      if (data.kaihunum && data.kaihuname) {
        shoukuanInfo.value = {
          kaihunum: data.kaihunum,
          kaihuname: data.kaihuname,
        };
      }

      if (data.status === 1) {
        // 审核通过
        jingpaihaopai.value = data.jingpaihaopai || "";
        jingpaihaopais.value = data.jingpaihaopais || "";
      }
    } else {
      // 未报名或获取失败时重置状态
      baomingIndex.value = -1;
      kanhuoIndex.value = -1;
      zhengming.value = "";
      bm_id.value = 0;
      shoukuanInfo.value = null;
    }
  } catch (error) {
    console.error("获取报名状态失败:", error);
    // 错误时重置状态
    baomingIndex.value = -1;
    kanhuoIndex.value = -1;
    zhengming.value = "";
    bm_id.value = 0;
    shoukuanInfo.value = null;
  }
};

/**
 * 确认看货 - 修改为先显示看货协议弹窗
 */
const viewingAgreement = async (): Promise<void> => {
  if (!userInfo.value) {
    ElMessage.error("请先登录");
    goLogin();
    return;
  }

  try {
    // 通过getNewsDetail接口获取看货协议内容
    const response = await newsApi.getNewsDetail({
      id: 201, // 看货协议的新闻ID
    });

    if (response.code === 1 && response.data) {
      // 显示看货协议弹窗
      agreementTitle.value = "看货确认声明";
      agreementContent.value =
        response.data.content || "<p>请仔细阅读看货确认声明内容...</p>";
      agreementType.value = "viewing"; // 标识为看货协议
      showAgreementModal.value = true;
    } else {
      ElMessage.error("获取看货协议内容失败");
    }
  } catch (error) {
    console.error("获取看货协议失败:", error);
    ElMessage.error("获取协议内容失败，请稍后重试");
  }
};

/**
 * 打开上传缴纳凭证弹窗
 */
const zhengmingUpload = (): void => {
  showUploadModal.value = true;
};

/**
 * 处理上传确认事件
 * @param imageUrl 上传的图片地址
 */
const handleUploadConfirm = async (imageUrl: string): Promise<void> => {
  uploadLoading.value = true;
  try {
    const response = await auctionApi.uploadPaymentProof({
      bd_id: targetId.value,
      member_id: userInfo.value ? userInfo.value.id : 0,
      bm_id: bm_id.value,
      zhengming: imageUrl, // 使用上传的图片地址
    });

    if (response.code === 1) {
      ElMessage.success("凭证提交成功");
      zhengming.value = "已上传";
      showUploadModal.value = false;
    } else {
      ElMessage.error(response.msg || "凭证提交失败");
    }
  } catch (error) {
    console.error("凭证提交失败:", error);
    ElMessage.error("凭证提交失败，请稍后重试");
  } finally {
    uploadLoading.value = false;
  }
};

/**
 * 处理上传取消事件
 */
const handleUploadCancel = (): void => {
  // 上传取消时的处理逻辑
};

/**
 * 加价操作
 * @param jiade 加价金额
 */
const zhichu = (jiade: number): void => {
  const linshijia = Number(dangqianjiaInit.value * 100 + jiade * 100).toFixed(
    0
  );
  jiage.value = Number(linshijia) / 100;
};

/**
 * 减价操作
 * @param jiade 减价金额
 */
const jianjia = (jiade: number): void => {
  const linshijia = Number(dangqianjiaInit.value * 100 - jiade * 100).toFixed(
    0
  );
  jiage.value = Number(linshijia) / 100;
};

/**
 * 提交出价
 */
const chujia = async (): Promise<void> => {
  const chadejia =
    Number(Number(jiage.value * 100).toFixed(0)) -
    Number(Number(dangqianjiaInit.value * 100).toFixed(0));
  const linshi_jiajiadi = Number(jiajiadi.value * 100).toFixed(0);
  if (jiage.value <= dangqianjiaInit.value) {
    ElMessage.error("出价不能小于最高价");
    return;
  }
  if (Number(chadejia) % Number(linshi_jiajiadi) != 0) {
    ElMessage.error("加价必须是" + jiajiadi.value + "的整倍数");
    return;
  }
  const msg = `您确定要以${jiage.value}元的价格竞价此标的吗？`;
  try {
    await ElMessageBox.confirm(msg, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "再想想",
      type: "warning",
    });

    // 调用出价接口
    const response = await auctionApi.bidTarget({
      bd_id: targetId.value,
      member_id: userInfo.value ? userInfo.value.id : 0,
      chujia: jiage.value - dangqianjiaInit.value,
      dangqianjia: jiage.value,
    });

    if (response.code === 1) {
      dangqianjiaInit.value = jiage.value;
      ElMessage.success("出价成功");
      // 刷新出价记录
      getChujiaiList();
    } else {
      ElMessage.error(response.msg || "出价失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("出价失败:", error);
      ElMessage.error("出价失败，请稍后重试");
    }
  }
};

/**
 * 减价请求
 */
const jianjiaRequest = async (): Promise<void> => {
  if (jiage.value >= dangqianjiaInit.value) {
    ElMessage.error("出价不能高于当前价");
  }
  const chadejia =
    Number(Number(dangqianjiaInit.value * 100).toFixed(0)) -
    Number(Number(jiage.value * 100).toFixed(0));
  const linshi_jiajiadi = Number(jiajiadi.value * 100).toFixed(0);
  if (Number(chadejia) % Number(linshi_jiajiadi) != 0) {
    ElMessage.error("减价必须是" + jiajiadi.value + "的整倍数");
  }
  const msg = `您确定要以${jiage.value}元的价格竞价此标的吗？`;
  try {
    await ElMessageBox.confirm(msg, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "再想想",
      type: "warning",
    });
    // 这里应该调用API提交减价
    // console.log("提交减价: ", jiage.value);
    dangqianjiaInit.value = jiage.value;
    ElMessage.success("出价成功");
  } catch {
    // 用户取消
  }
};

/**
 * 盲拍出价
 */
const mangpaiChu = async (): Promise<void> => {
  if (jiage.value <= dangqianjiaInit.value * 1) {
    ElMessage.error("出价不能低于当前价");
  }
  const msg = `您确定要以${jiage.value}元的价格竞价此标的吗？`;
  try {
    await ElMessageBox.confirm(msg, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "再想想",
      type: "warning",
    });
    // 这里应该调用API提交盲拍出价
    // console.log("提交盲拍出价: ", jiage.value);
    ElMessage.success("出价成功");
  } catch {
    // 用户取消
  }
};

/**
 * 指定价盲拍出价
 */
const mangpaiChuZhiding = async (): Promise<void> => {
  if (jiage.value < Number(biaodInfo.bd_qipaijia)) {
    ElMessage.error("出价不能低于起拍价");
  }
  const msg = `您确定要以${jiage.value}元的价格竞价此标的吗？`;
  try {
    await ElMessageBox.confirm(msg, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "再想想",
      type: "warning",
    });
    // 这里应该调用API提交指定价盲拍出价
    // console.log("提交指定价盲拍出价: ", jiage.value);
    ElMessage.success("出价成功");
  } catch {
    // 用户取消
  }
};

/**
 * 进入下个标的
 */
const nextBIaodi = (): void => {
  const biaodiid = biaodInfo.id;
  let indexs = 0;
  biaodList.value.forEach((el, index) => {
    if (el.id == biaodiid) {
      indexs = index;
    }
  });
  if (indexs < biaodList.value.length - 1) {
    setBiaodiId(biaodList.value[indexs + 1]);
    ElMessage.success("已切换到下个标的");
  }
};

// 倒计时相关逻辑
let countdownTimer: number | null = null; // 倒计时定时器

/**
 * 计算倒计时
 * @param targetTime 目标时间戳（毫秒）
 */
const calculateCountdown = (targetTime: number): void => {
  const now = Date.now();
  const diff = targetTime - now;

  if (diff <= 0) {
    // 倒计时结束
    info.dayDiff = "0";
    info.hours = "00";
    info.minutes = "00";
    info.seconds = "00";
    return;
  }

  // 计算天、时、分、秒
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((diff % (1000 * 60)) / 1000);

  // 更新倒计时数据
  info.dayDiff = days.toString();
  info.hours = hours.toString().padStart(2, "0");
  info.minutes = minutes.toString().padStart(2, "0");
  info.seconds = seconds.toString().padStart(2, "0");
};

/**
 * 启动倒计时
 */
const startCountdown = (): void => {
  // 清除现有定时器
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }

  // 根据标的状态设置目标时间
  let targetTime: number;

  if (biaodInfo.bd_status === 1) {
    // 即将开始状态：使用开始时间
    if (biaodInfo.sheng_time) {
      targetTime = new Date(biaodInfo.sheng_time.replace(/-/g, "/")).getTime();
    } else {
      return; // 没有开始时间则不启动倒计时
    }
  } else if (biaodInfo.bd_status === 2 || biaodInfo.bd_status === 3) {
    // 正在进行状态：使用结束时间
    if (biaodInfo.end_time) {
      targetTime = new Date(biaodInfo.end_time.replace(/-/g, "/")).getTime();
    } else {
      return; // 没有结束时间则不启动倒计时
    }
  } else {
    // 其他状态不需要倒计时
    return;
  }

  // 立即计算一次
  calculateCountdown(targetTime);

  // 每秒更新一次倒计时
  countdownTimer = setInterval(() => {
    calculateCountdown(targetTime);
  }, 1000);
};

/**
 * 停止倒计时
 */
const stopCountdown = (): void => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
    countdownTimer = null;
  }
};

// ========== 模拟数据区域开始 - 用于开发测试，后期接入真实接口时可删除 ==========

/**
 * 更新登录后的模拟数据
 * @param {boolean} isLogin - 是否登录
 */
const updateMockData = (isLogin: boolean) => {
  if (isLogin) {
    // 登录后模拟数据 - 从未报名状态开始
    baomingIndex.value = -1; // 未报名状态
    kanhuoIndex.value = -1; // 未看货
    jingpaihaopai.value = ""; // 竞拍号牌
    jingpaihaopais.value = ""; // 竞拍号牌前缀
    bd_jdnum.value = 8; // 竞价人数（较少，因为刚开始）
    bm_id.value = 0; // 报名ID
    isShoucang.value = 1; // 已收藏

    // 模拟收款信息
    shoukuanInfo.value = {
      kaihunum: "****************", // 银行账号
      kaihuname: "中国银行", // 开户行
      accountName: "测试拍卖公司", // 账户名称
      amount: 10000, // 金额
    };

    // 更新出价记录，显示更多竞价活动
    chujiailist.value = [
      {
        id: 1001, // 出价记录ID
        jingpaihaopai: "A001",
        dangqianjia: "108000元",
        addtime: Math.floor(Date.now() / 1000) - 30,
      },
      {
        id: 1002, // 出价记录ID
        jingpaihaopai: "B002",
        dangqianjia: "107000元",
        addtime: Math.floor(Date.now() / 1000) - 120,
      },
      {
        id: 1003, // 出价记录ID
        jingpaihaopai: "C003",
        dangqianjia: "106000元",
        addtime: Math.floor(Date.now() / 1000) - 180,
      },
      {
        id: 1004, // 出价记录ID
        jingpaihaopai: "D004",
        dangqianjia: "105000元",
        addtime: Math.floor(Date.now() / 1000) - 240,
      },
      {
        id: 1005, // 出价记录ID
        jingpaihaopai: "E005",
        dangqianjia: "104000元",
        addtime: Math.floor(Date.now() / 1000) - 300,
      },
    ];

    // 更新当前最高价
    dangqianjiaInit.value = 108000;
    jiage.value = 109000;

    // 更新拍卖师发言，显示更活跃的拍卖现场
    fayanList.value = [
      {
        id: 2001, // 发言记录ID
        type_name: "拍卖师",
        addtime_name: new Date().toLocaleTimeString(),
        content: "现在最高价是10.8万元，还有更高的吗？",
      },
      {
        id: 2002, // 发言记录ID
        type_name: "拍卖师",
        addtime_name: new Date(Date.now() - 60000).toLocaleTimeString(),
        content: "A001号竞买人出价10.8万元，有没有更高的？",
      },
      {
        id: 2003, // 发言记录ID
        type_name: "拍卖师",
        addtime_name: new Date(Date.now() - 120000).toLocaleTimeString(),
        content: "竞价非常激烈，现在价格已经超过起拍价",
      },
      {
        id: 2004, // 发言记录ID
        type_name: "拍卖师",
        addtime_name: new Date(Date.now() - 180000).toLocaleTimeString(),
        content: "各位竞买人请注意，拍卖正在进行中",
      },
      {
        id: 2005, // 发言记录ID
        type_name: "拍卖师",
        addtime_name: new Date(Date.now() - 240000).toLocaleTimeString(),
        content: "欢迎大家参与本次拍卖会，现在开始竞价",
      },
    ];
  } else {
    // 未登录时重置数据
    baomingIndex.value = -1;
    kanhuoIndex.value = -1;
    jingpaihaopai.value = "";
    jingpaihaopais.value = "";
    bd_jdnum.value = 0;
    bm_id.value = 0;
    isShoucang.value = 0;
    shoukuanInfo.value = null;

    // 重置出价记录为基础数据
    chujiailist.value = [
      {
        id: 3001, // 出价记录ID
        jingpaihaopai: "A001",
        dangqianjia: "105000元",
        addtime: 1640995200,
      },
      {
        id: 3002, // 出价记录ID
        jingpaihaopai: "B002",
        dangqianjia: "104000元",
        addtime: 1640995100,
      },
      {
        id: 3003, // 出价记录ID
        jingpaihaopai: "C003",
        dangqianjia: "103000元",
        addtime: 1640995000,
      },
    ];

    dangqianjiaInit.value = 100000;
    jiage.value = 101000;

    // 重置拍卖师发言为基础数据
    fayanList.value = [
      {
        id: 4001, // 发言记录ID
        type_name: "拍卖师",
        addtime_name: "14:30:25",
        content: "欢迎大家参与本次拍卖会",
      },
      {
        id: 4002, // 发言记录ID
        type_name: "拍卖师",
        addtime_name: "14:32:10",
        content: "现在开始竞价，起拍价10万元",
      },
    ];
  }
};

// 监听登录状态变化，更新相关数据
watch(
  isLoggedIn,
  (newValue) => {
    updateMockData(newValue);
  },
  { immediate: true }
);

// ========== 模拟数据区域结束 ==========

// 监听图片数据变化，重新检查滚动状态
watch(
  () => biaodiImgs.value,
  () => {
    // 当图片数据变化时，延迟检查滚动状态以确保DOM已更新
    nextTick(() => {
      setTimeout(() => {
        checkHorizontalScrollStatus();
      }, 100);
    });
  },
  { deep: true }
);

// 生命周期钩子
onMounted(() => {
  // 初始化页面数据
  initPageData();

  // 初始化水平滚动状态检查
  nextTick(() => {
    checkHorizontalScrollStatus();

    // 计算导航栏和右侧面板原始位置
    setTimeout(() => {
      if (tabsHeaderRef.value) {
        const rect = tabsHeaderRef.value.getBoundingClientRect();
        const scrollTop =
          window.pageYOffset || document.documentElement.scrollTop;
        tabsHeaderOriginalTop.value = rect.top + scrollTop;
      }

      // 计算右侧面板原始位置
      if (rightPanelRef.value) {
        const rect = rightPanelRef.value.getBoundingClientRect();
        const scrollTop =
          window.pageYOffset || document.documentElement.scrollTop;
        rightPanelOriginalTop.value = rect.top + scrollTop;
      }

      // 添加滚动事件监听
      window.addEventListener("scroll", handleScroll);
    }, 100);
  });
});

// 初始化页面数据
const initPageData = async () => {
  try {
    await getBiaodDetail(), // 获取标的详情（内部会启动倒计时）
      // 并行加载所有数据
      await Promise.all([
        getBiaodList(), // 获取标的列表
        getChujiaiList(), // 获取出价记录
        getFayanList(), // 获取发言记录
        checkRegistrationStatus(), // 检查报名状态
        checkFavoriteStatus(), // 检查收藏状态
        getCompanyFollowStatus(), // 获取企业关注状态
      ]);

    // 初始化WebSocket连接（仅在标的未结束时）
    if (
      biaodInfo.bd_status != 4 &&
      biaodInfo.bd_status != 5 &&
      biaodInfo.bd_status != 8
    ) {
      initWebSocket(
        targetId.value,
        pmhId.value,
        userInfo.value ? userInfo.value.id : 0,
        handleWebSocketMessage,
        getHeartbeatData
      );
    }
  } catch (error) {
    console.error("页面数据初始化失败:", error);
  }
};

// 企业收藏状态
const isCompanyFollowed = ref<boolean>(false);

// 获取企业关注状态
const getCompanyFollowStatus = async () => {
  if (!userStore.isLoggedIn) {
    return;
  }
  const res = await companyApi.checkCompanyFavorite({
    member_id: userStore.userInfo?.id as number,
    qiye_id: biaodInfo.parentid,
  });
  if (res.code === 1) {
    if (res.data === "已收藏") {
      isCompanyFollowed.value = true;
    } else {
      isCompanyFollowed.value = false;
    }
  }
};

// 关注/取消关注
const followCompany = async () => {
  if (!userStore.isLoggedIn) {
    return;
  }
  const res = await companyApi.favoriteCompany({
    member_id: userStore.userInfo?.id as number,
    qiye_id: biaodInfo.parentid,
    isquxiao: isCompanyFollowed.value ? 0 : 1,
    type: 1,
  });
  if (res.code === 1) {
    isCompanyFollowed.value = !isCompanyFollowed.value;
    ElMessage.success(res.data);
  }
};

// 检查收藏状态
const checkFavoriteStatus = async () => {
  if (!userInfo.value) {
    isShoucang.value = 0;
    return;
  }

  try {
    const response = await auctionApi.checkFavoriteStatus({
      bd_id: targetId.value,
      member_id: userInfo.value.id,
    });

    // 根据接口返回的文字判断收藏状态
    // 接口返回"已收藏"表示已收藏，"未收藏"表示未收藏
    const statusText = response.data || response.msg || "";
    isShoucang.value = statusText.includes("已收藏") ? 1 : 0;
    // console.log("isShoucang.value", isShoucang.value); 已移除
  } catch (error) {
    console.error("获取收藏状态失败:", error);
    isShoucang.value = 0;
  }
};

onUnmounted(() => {
  // 组件卸载前清理定时器
  stopCountdown();

  // 清理WebSocket连接
  closeWebSocket();

  // 移除滚动事件监听
  window.removeEventListener("scroll", handleScroll);

  // 停止自动滚动
  stopAutoScroll();
});

// 暴露给模板使用的数据和方法
defineExpose({
  // 数据
  isShoucang,
  paimaihuiId,
  isImageHovered,
  maskPosition,
  maskSize,
  info,
  jiage,
  dangqianjiaInit,
  jiajiadi,
  jiajiazhong,
  jiajiagao,
  baomingIndex,
  kanhuoIndex,
  zhengming,
  bm_id,
  shoukuanInfo,
  jingpaihaopai,
  jingpaihaopais,
  isBao,
  isTeshu,
  userInfo,
  bd_jdnum,
  mangpaichujiaLnajie,
  isChuMangNum,
  slideIndex,
  tabIndex,
  biaodiId,
  // 导航栏相关数据
  activeTab,
  isTabsFixed,
  tabsHeaderRef,
  biaodInfo,
  biaodiImgs,
  biaodiImgsIndex,
  url,
  chujiailist,
  fayanList,
  biaodList,
  biaodListIndex,
  biaodListIndexs,
  isPaimaishi,
  // 方法
  formatDate,
  goQiye,
  oneTitleLeftClick,
  oneTitlerightClick,
  setBiaodiId,
  oneTitleLeftClicks,
  oneTitlerightClicks,
  setIndex,
  biaodishoucangClick,
  handleImageMouseEnter,
  handleImageMouseMove,
  handleImageMouseLeave,
  handleGoBack,
  // 导航栏相关方法
  scrollToSection,
  handleScroll,
  // 右侧面板相关变量和方法
  rightPanelRef,
  isRightPanelFixed,
  rightPanelStyle,
  updateRightPanelStyle,
  goLogin,
  baominxieyi,
  gerenBaoming,
  qiyeBaoming,
  viewingAgreement,
  zhengmingUpload,
  zhichu,
  jianjia,
  chujia,
  jianjiaRequest,
  mangpaiChu,
  mangpaiChuZhiding,
  nextBIaodi,
  // 倒计时相关方法
  startCountdown,
  stopCountdown,
  // 水平滚动相关方法
  checkHorizontalScrollStatus,
  scrollLeft,
  scrollRight,
  // 自动滚动相关方法
  autoScrollLeft,
  autoScrollRight,
  stopAutoScroll,
  isMouseAtScrollEdge,
  handleScrollAreaMouseMove,
  handleScrollAreaMouseLeave,
});
</script>

<style lang="scss" scoped>
.min_wrapper_1500 {
  min-width: 1280px;
}

.auctionDetailc {
  background: #f6f6f6;
  padding-bottom: 50px;
}

.auctionDetail_c {
  width: 1280px;
  margin: 0 auto;
  padding-top: 20px;
}

.auctionDetail_c_one {
  height: 205px;
  background: #ffffff;
  display: flex;
  -js-display: flex;
  justify-content: space-between;
  border-radius: 10px;
  padding: 15px 30px 15px 30px;
}

.auctionDetail_c_one_left {
  margin-right: 23px;
  box-sizing: border-box;
  margin-top: 13px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.auctionDetail_c_one_left_s {
  width: 320px;
  font-size: 22px;
  line-height: 1.5;
  color: #333333;
  font-family: "PingFang Bold";
  margin-top: 1px;
  .auctionDetail_c_one_left_s_span {
    font-size: 22px;
  }

  .auction_c_one_img {
    background-color: #004c66;
    text-align: center;
    padding: 3px 6px;
    line-height: 23px;
    border-radius: 4px;
    color: #fff;
    font-family: "PingFang Bold";
    font-size: 12px;
    margin-left: 5px;
    position: relative;
    top: -3px;
    // 禁止换行
    white-space: nowrap;
  }
}

.auctionDetail_c_one_left_da {
  display: flex;
  -js-display: flex;
  flex-direction: column;
  margin-bottom: 10px;
}

.auctionDetail_c_one_left_w {
  font-size: 14px;
  color: #999;
  line-height: 20px;
  cursor: pointer;
  margin-bottom: 5px;
  .one_left_icon {
    width: 13px;
    height: 13px;
  }
  .qiye-follow-text {
    &:hover {
      color: #004c66;
    }
  }
  .qiye-follow-cancel {
    &:hover {
      color: #ef0004;
    }
  }
}

// 标的列表横向滚动容器样式
.auction-items-scroll-container {
  max-width: 780px;
  height: 175px;
  background-color: #f2f2f2;
  border-radius: 10px;
  box-sizing: border-box;
  display: flex;
}

// 标的项目包装器
.auction-items-wrapper {
  display: flex;
  gap: 10px;
  height: 170px;
  align-items: center;
  overflow-x: auto;
  overflow-y: hidden;
  &:first-child {
    padding-left: 10px;
  }
  &:last-child {
    padding-right: 10px;
  }

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    height: 5px;
    margin: 0 10px; // 左右添加10px边距
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    margin: 0 10px; // 轨道左右添加10px边距
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    margin: 0 10px; // 滑块左右添加10px边距

    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }
}

// 单个标的卡片样式
.auction-item-card {
  position: relative;
  width: 216px;
  height: 145px;
  border-radius: 8px;
  background-color: #ffffff;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  &:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
}

// 标的图片容器
.auction-item-image {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 8px;
  transform: scale(1.02);

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
  }

  /* // 图片闪光效果
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -150%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      to right,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.3) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    transform: skewX(-25deg);
    z-index: 1;
    transition: none;
    pointer-events: none;
  } */
}

// 悬停时的图片效果
.auction-item-card:hover .auction-item-image {
  img {
    transform: scale(1.06);
  }

  &::before {
    animation: imageShine 1s ease-in-out;
  }
}

// 定义图片闪光动画
@keyframes imageShine {
  0% {
    left: -150%;
  }
  100% {
    left: 150%;
  }
}

// 状态标签样式
.status-tag {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 60px;
  height: 23px;
  color: white;
  border-top-left-radius: 8px;
  border-bottom-right-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  z-index: 10;
  padding: 0 8px;
  box-sizing: border-box;

  // 正在进行状态
  &.status-ongoing {
    background: linear-gradient(90deg, #a80012, #d4002a);
  }

  // 即将开始状态
  &.status-upcoming {
    background: linear-gradient(90deg, #169173, #20b394);
  }

  // 已结束状态（流拍、撤拍、成交）
  &.status-ended {
    background: linear-gradient(90deg, #919191, #b0b0b0);
  }

  // 暂停/审批状态
  &.status-paused {
    background: linear-gradient(90deg, #ff8c00, #ffa500);
  }

  // 闪光动画效果
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -150%;
    width: 30%;
    height: 100%;
    background: linear-gradient(
      to right,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.3) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    transform: skewX(-25deg);
    z-index: 1;
    transition: none;
    pointer-events: none;
  }
}

// 悬停时的闪光动画
.auction-item-card:hover .status-tag::before {
  animation: statusTagShine 1s ease-in-out;
}

// 定义闪光动画
@keyframes statusTagShine {
  0% {
    left: -150%;
  }
  100% {
    left: 80%;
  }
}

.auctionDetail_c_two {
  margin-top: 19px;
  display: flex;
  -js-display: flex;
}

.auctionDetail_c_two_left {
  width: 973px;
  background: #ffffff;
  border-radius: 10px;
  padding: 15px 20px;
}

.auctionDetail_c_two_left_one {
  padding-top: 19px;
  padding-left: 20px;
  box-sizing: border-box;
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

.auctionDetail_c_two_left_two {
  display: flex;
  -js-display: flex;
  margin-bottom: 25px;
}

.auctionDetail_c_two_left_two_left {
  position: relative;
  flex: 1;
}

.auctionDetail_c_two_left_two_leftssssss {
  width: 412px;
  height: 412px;
  display: flex;
  -js-display: flex;
  align-items: center;
  border-radius: 10px;
}

.auctionDetail_c_two_left_two_left_o {
  width: 412px;
  height: 412px;
  background: #f6f6f6;
  display: flex;
  -js-display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border-radius: 10px;
}

.auctionDetail_c_two_left_two_left_o img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.auctionDetail_c_two_left_two_left_omaskTop {
  width: 412px;
  height: 412px;
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
}

.auctionDetail_c_two_left_two_left_omaskToptop {
  width: 180px;
  height: 180px;
  background-color: rgba($color: #004c66, $alpha: 0.3);
  opacity: 0.4;
  position: absolute;
  top: 0;
  left: 0;
}

.auctionDetail_c_two_left_two_leftss {
  width: 412px;
  height: 412px;
  position: absolute;
  background-color: #ffffff;
  right: -412px;
  top: 0;
  z-index: 222;
  overflow: hidden;
}

.auctionDetail_c_two_left_two_leftss img {
  position: absolute;
  width: 720px;
  height: 720px;
}

.auctionDetail_c_two_left_two_left_omaskTopright {
  width: 412px;
  height: 412px;
  position: relative;
  right: -20px;
  z-index: 10;
  overflow: hidden;
  background-color: #ffffff;
  flex-shrink: 0;
  display: flex;
  -js-display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
}

.auctionDetail_c_two_left_two_left_omaskToprightrightImg {
  display: block;
  width: 720px;
  height: auto;
  position: absolute;
  top: 0;
  left: 0;
  transform: scale(2);
}

.auctionDetail_c_two_left_two_left_s {
  width: 412px;
  height: 96px;
  display: flex;
  -js-display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
}

.auctionDetail_c_two_left_two_left_s img {
  width: 18px;
  height: 35px;
}

.auctionDetail_c_two_left_two_left_s_divs {
  width: 100%;
  display: flex;
  align-items: center;
  transition: all 0.3s;
}

.auctionDetail_c_two_left_two_left_s_div {
  display: flex;
  align-items: center;
  overflow: hidden;
}

.auctionDetail_c_two_left_two_left_s_div img {
  width: 96px;
  height: 96px;
  margin-right: 10px;
  flex-shrink: 0;
  border-radius: 10px;
}

// 水平图片列表滚动容器样式
.image-list-container-horizontal {
  position: relative;
  width: 412px;
  height: 96px;
  display: flex;
  align-items: center;
  margin-top: 10px;

  .scroll-arrow {
    position: absolute;
    width: 24px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;

    &.disabled {
      opacity: 0.3;
      cursor: not-allowed;
      pointer-events: none;
    }

    &.scroll-left {
      background: linear-gradient(
        270deg,
        rgba(255, 255, 255, 0) 0%,
        rgb(255, 255, 255) 50%
      );
      left: -12px;
    }

    &.scroll-right {
      background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        rgb(255, 255, 255) 50%
      );
      right: -12px;
    }

    .arrow-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 1;
      transition: opacity 0.3s ease;
    }

    /* &:hover .arrow-overlay {
      opacity: 1;
    } */

    .scroll-icon {
      width: 24px;
      height: 24px;
      color: #666;
      transition: color 0.3s ease;
    }

    // 左箭头图标旋转
    &.scroll-left .scroll-icon-left {
      transform: rotate(90deg);
    }

    // 右箭头图标旋转
    &.scroll-right .scroll-icon-right {
      transform: rotate(-90deg);
    }

    &:hover .scroll-icon {
      color: #004c66;
    }
  }
}

.image-list-horizontal {
  width: 100%;
  height: 96px;
  overflow-x: auto;
  overflow-y: hidden;
  display: flex;
  align-items: center;
  scroll-behavior: smooth;
  padding: 0 20px 0 0;
  box-sizing: border-box;
  position: relative;

  // 隐藏滚动条
  &::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;
  scrollbar-width: none;

  // 左边缘触发区域
  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 80px;
    height: 100%;
    background: linear-gradient(
      90deg,
      rgba(0, 76, 102, 0.05) 0%,
      transparent 100%
    );
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
  }

  // 右边缘触发区域
  &::after {
    content: "";
    position: absolute;
    right: 20px;
    top: 0;
    width: 80px;
    height: 100%;
    background: linear-gradient(
      270deg,
      rgba(0, 76, 102, 0.05) 0%,
      transparent 100%
    );
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
  }

  // 鼠标悬停时显示边缘提示
  &:hover::before,
  &:hover::after {
    opacity: 1;
  }

  .image-item-horizontal {
    width: 96px;
    height: 96px;
    margin-right: 10px;
    flex-shrink: 0;
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s ease;

    &:last-child {
      margin-right: 0;
    }

    &.active {
      border-color: #004c66;
      // box-shadow: 0 0 0 1px #004c66;
    }

    &:hover {
      border-color: #004c66;
    }

    // 当图片在可视区域边缘时的特殊样式
    &.edge-hover {
      position: relative;

      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 76, 102, 0.1);
        border-radius: 8px;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
      }

      &:hover::after {
        opacity: 1;
      }
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    &:hover img {
      transform: scale(1);
    }
  }
}

// 面包屑导航和观看数样式
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
  padding-bottom: 15px;
  border-bottom: 1px solid;
  border-image: linear-gradient(to right, #dddddd, #ffffff);
  border-image-slice: 1;

  .breadcrumb {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #666;

    .auction-title {
      color: #004c66;
      cursor: pointer;
      transition: color 0.3s ease;

      &:hover {
        color: #00334d;
      }
    }

    .separator {
      margin: 0 8px;
      color: #999;
    }

    .current {
      color: #333;
      font-weight: 500;
      max-width: 400px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .view-count {
    display: flex;
    align-items: center;
    gap: 20px;
    font-size: 14px;
    color: #666;

    .collect-info,
    .view-info {
      display: flex;
      align-items: center;
      gap: 5px;

      .star-icon,
      .eye-icon {
        width: 16px;
        height: 16px;
      }

      .eye-icon {
        color: #666;
      }
    }

    .collect-info {
      cursor: pointer;
      transition: color 0.3s ease;

      &:hover {
        color: #004c66;
      }

      /* .qiye-shoucang-text {
        color: #999;
      } */
      .qiye-shoucang-cancel {
        &:hover {
          color: #ef0004;
        }
      }
    }
  }
}

.auctionDetail_c_two_left_two_left_w {
  display: flex;
  -js-display: flex;
  align-items: center;
  margin-top: 20px;
}

.auctionDetail_c_two_left_two_left_w > div {
  display: flex;
  -js-display: flex;
  align-items: center;
  font-size: 14px;
  margin-right: 10px;
  width: 110px;
}

.auctionDetail_c_two_left_two_left_w > div img {
  margin-right: 5px;
}

.auctionDetail_c_two_left_two_right {
  width: 501px;
  margin-left: 20px;
}

.auctionDetail_c_two_left_two_right_one {
  width: 501px;
  height: 48px;
  border-radius: 10px;
  background: linear-gradient(90deg, #ff001b 0%, #a80012 100%);
  padding: 6px 18px;
}

.auctionDetail_c_two_left_two_right_one_w_b {
  width: 100%;
  height: 36px;
  display: flex;
  -js-display: flex;
  align-items: center;
  font-size: 15px;
  color: white;
  box-sizing: border-box;
  font-family: "PingFang Bold";
  .time {
    font-size: 18px;
    font-family: "PingFang Medium";
  }
}

.auctionDetail_c_two_left_two_right_one_w_c {
  width: 100%;
  display: flex;
  -js-display: flex;
  align-items: center;
}

.auctionDetail_c_two_left_two_right_one_w_bs {
  font-size: 18px;
  font-family: "PingFang Bold";
  color: #fff;
  border-right: 1px solid #fff;
  margin-right: 75px;
}

.finish-time {
  margin-right: 55px;
}

.auctionDetail_c_two_left_two_right_one_w_b_time {
  font-size: 18px;
  color: #fff;
  font-family: "PingFang Medium";
  span {
    font-size: 24px;
    font-family: "DIN Bold";
    color: #ffd700;
    margin: 0 1px;
  }
}

.auctionDetail_c_two_left_two_right_one_w_bsb {
  font-size: 24px;
  font-family: "DIN Bold";
  color: #fffca5;
  margin: 0 1px;
}

.apply-cancel {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-top: 5px;
  margin-left: 20px;
}

.auctionDetail_c_two_left_two_right_one_thre_two {
  display: flex;
  -js-display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  flex: 1;
  .apply {
    width: 159px;
    height: 48px;
    line-height: 48px;
    margin-top: 13px;
    margin-left: 42px;
  }
}

.auction_review {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-around;
  margin-top: 20px;
  .auction_review_tips {
    width: 249px;
  }
}

.auctionDetail_c_two_left_two_right_one_thre_two > div {
  width: 120px;
  height: 36px;
  background: #004c66;
  border-radius: 4px;
  text-align: center;
  line-height: 36px;
  font-size: 16px;
  font-weight: 400;
  color: #ffffff;
  cursor: pointer;
  .apply-icon {
    width: 14px;
    height: 13px;
  }
}

.auctionDetail_c_two_left_two_right_one_thre_three {
  width: 186px;
  height: 26px;
  background: #ffffff;
  border-radius: 4px;
  text-align: center;
  line-height: 26px;
  margin: 0 auto;
  font-size: 12px;
  color: #333333;
  cursor: pointer;
}

.auctionDetail_c_two_left_two_right_one_thre_chujia {
  display: flex;
  -js-display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}

.auctionDetail_c_two_left_two_right_one_thre_chujias {
  width: 160px;
  height: 40px;
  background: #ffffff;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
}

.auctionDetail_c_two_left_two_right_one_thre_chujiass {
  width: 104px;
  height: 40px;
  background: #004c66;
  border-radius: 4px;
  text-align: center;
  line-height: 40px;
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
  margin-left: 15px;
  cursor: pointer;
}

.auctionDetail_c_two_left_two_right_one_thre_chujia_bottom {
  display: flex;
  -js-display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
}

.auctionDetail_c_two_left_two_right_one_thre_chujia_bottom div {
  min-width: 86px;
  height: 30px;
  background: #004c66;
  border-radius: 4px;
  padding: 0 10px;
  box-sizing: border-box;
  text-align: center;
  line-height: 30px;
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
  margin: 0 5px;
  cursor: pointer;
}

.auctionDetail_c_two_left_two_right_one_thre_chujias input {
  width: 160px;
  height: 40px;
  padding-left: 15px;
  box-sizing: border-box;
}

.auctionDetail_c_two_left_two_right_one_thre_four {
  display: flex;
  -js-display: flex;
  font-size: 12px;
  font-weight: 400;
  color: #004c66;
  flex: 1;
}

.auctionDetail_c_two_left_two_right_one_thre_fours {
  display: flex;
  -js-display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 400;
  color: #004c66;
  .other-button {
    width: 159px;
    height: 48px;
    background: #f7f7f7;
    border: 1px solid #c1c1c1;
    border-radius: 4px;
    text-align: center;
    line-height: 48px;
    color: #999999;
    font-size: 16px;
  }
}

.auctionDetail_c_two_left_two_right_one_tw {
  display: flex;
  -js-display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  margin-top: 15px;
  padding: 0 10px;
  box-sizing: border-box;
}

.auctionDetail_c_two_left_two_right_one_tw span {
  color: #004c66;
}

.auctionDetail_c_two_left_two_right_one_thre {
  width: 501px;
  min-height: 218px;
  background: #e6eef0;
  margin-top: 10px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  .auction-finish {
    display: flex;
    .auctionDetail_c_two_left_two_right_one_thre_four {
      margin-top: 40px;
    }
    .auctionDetail_c_two_left_two_right_one_thre_fours {
      margin-top: 30px;
    }
  }
}

.auctionDetail_c_two_left_two_right_one_thre_one {
  display: flex;
  -js-display: flex;
  align-items: flex-end;
  justify-content: space-between;
  padding-bottom: 16px;
  box-sizing: border-box;
  font-size: 14px;
  font-family: "DIN Bold";
  color: #333333;
  border-bottom: 1px solid;
  // 中间向两侧渐变

  border-image: linear-gradient(
      to right,
      rgba(221, 221, 221, 0) 0%,
      #dddddd 50%,
      rgba(221, 221, 221, 0) 100%
    )
    1;
  border-image-slice: 1;

  .jiage {
    display: flex;
    flex-direction: column;
  }
}

.auctionDetail_c_two_left_two_right_one_thre_ones {
  min-width: 97px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  background: url(../assets/auction/chujiaback.png) no-repeat;
  font-size: 13px;
  font-weight: 400;
  color: #666666;
  margin-left: 5px;
  display: flex;
  flex-direction: column;
}

.auctionDetail_c_two_left_two_right_one_thre_oneType {
  min-width: 97px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  background-color: #ffffff;
  font-size: 13px;
  font-weight: 400;
  color: #666666;
  margin-left: 5px;
  padding: 0 5px;
  border: 1px solid #cccccc;
}

/* 价格显示样式 - 参考AuctionCard.vue */
.jiage {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.jiage_title {
  font-size: 14px;
  color: #666666;
  margin-bottom: 8px;
  font-family: "PingFang Medium";
}

.price-display {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.price-amount {
  display: flex;
  align-items: baseline;
  color: #004c66;
  line-height: 1.15;
  font-family: "DIN Bold";
  margin-bottom: 4px;
}

.price-symbol {
  font-family: "PingFang Bold";
  font-size: 24px;
  margin-right: 2px;
}

.price-integer {
  font-size: 40px;
}

.price-decimal {
  font-size: 24px;
}

.price-unit {
  font-size: 18px;
  color: #004c66;
  font-family: "PingFang Bold";
  margin-left: 5px;
}

/* 价格和出价人同行布局样式 */
.price-bidder-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

/* 出价人信息区域样式 */
.bidder-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  height: 100%;
  min-width: 120px;
  height: 70px;
}

/* 出价人标题样式 */
.bidder-title {
  font-size: 14px;
  color: #666666;
  margin-bottom: 8px;
  font-family: "PingFang Medium";
}

/* 出价人值样式 */
.bidder-value {
  font-size: 18px;
  color: #004c66;
  font-family: "PingFang Bold";
}

/* 出价人姓名高亮样式 */
.bidder-name {
  color: #004c66;
  font-weight: bold;
}

.auctionDetail_c_two_left_two_right_one_w_bsb {
  color: #ffd700;
  font-family: "DIN Bold";
}

.auctionDetail_c_two_left_two_right_onefour {
  display: flex;
  -js-display: flex;
  align-items: center;
  flex-wrap: wrap;
  font-size: 14px;
  color: #999;
  margin-top: 20px;
}

.auctionDetail_c_two_left_two_right_onefour > div {
  margin-bottom: 22px;
  min-width: 50%;
}

.auctionDetail_c_two_left_two_right_onefour > div span {
  color: #000;
}

.auctionDetail_c_two_right {
  width: 306px;
  height: 600px;
  margin-left: 20px;
  display: flex;
  -js-display: flex;
  flex-direction: column;
  gap: 20px;
  transition: all 0.3s ease;

  // 固定定位时的样式调整
  &[style*="position: fixed"] {
    backdrop-filter: blur(10px);
    border-radius: 10px;

    .auctionDetail_c_two_right_a,
    .auctionDetail_c_two_right_b {
      margin-left: 20px;
      width: 284px;
    }
  }
}

.auctionDetail_c_two_right_a {
  height: 415px;
  background: #ffffff;
  padding: 20px;
  box-sizing: border-box;
  border-radius: 10px;
}

.auctionDetail_c_two_right_a_a {
  font-size: 16px;
  color: #333333;
  margin-bottom: 15px;
  font-family: "PingFang Bold";
}

.auctionDetail_c_two_right_a_b {
  height: 220px;
  /* overflow-y: auto; 移除原生滚动条，使用自定义滚动条 */
}

.auctionDetail_c_two_right_a_b_div {
  display: flex;
  -js-display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 12px;
  color: #666666;
}

.auctionDetail_c_two_right_a_b_divs {
  font-size: 14px;
  color: #333333;
  line-height: 1.4;
  margin-bottom: 15px;
}

.auctionDetail_c_two_right_b {
  height: 415px;
  background: #ffffff;
  padding: 20px;
  box-sizing: border-box;
  border-radius: 10px;
}

.auctionDetail_c_two_right_b_title {
  display: flex;
  -js-display: flex;
  border-radius: 5px;
  margin-bottom: 10px;
}

.auctionDetail_c_two_right_b_titlea,
.auctionDetail_c_two_right_b_titleb,
.auctionDetail_c_two_right_b_titlec {
  flex: 1;
  font-family: "PingFang Bold";
  color: #333;
}

.auctionDetail_c_two_right_b_titles {
  height: 240px;
  /* overflow-y: auto; 移除原生滚动条，使用自定义滚动条 */
}

.auctionDetail_c_two_right_b_title {
  display: flex;
  -js-display: flex;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  align-items: center;
}

.auctionDetail_c_two_right_b_title_img {
  width: 16px;
  height: 16px;
  margin-right: 5px;
}

.auctionDetail_c_three {
  margin-top: 20px;
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.auctionDetail_c_three_one {
  display: flex;
  -js-display: flex;
  background: #f5f5f5;
}

.auctionDetail_c_three_ones {
  flex: 1;
  padding: 15px;
  text-align: center;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.auctionDetail_c_three_ones:hover {
  background: #e8e8e8;
}

.auctionDetail_c_three_oness {
  flex: 1;
  padding: 15px;
  text-align: center;
  cursor: pointer;
  background: white;
  border-bottom: 3px solid #004c66;
  color: #004c66;
  font-weight: bold;
}

.auctionDetail_c_three_two {
  padding: 20px;
  min-height: 300px;
}

.chujialist {
  width: 100%;
  border-collapse: collapse;
}

.chujialist th,
.chujialist td {
  padding: 10px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.chujialist th {
  background: #f5f5f5;
  font-weight: bold;
}

// 响应式设计
@media (max-width: 1200px) {
  .auctionDetail_c {
    width: 100%;
    padding: 0 20px;
  }

  .auctionDetail_c_two_left {
    height: auto;
  }

  .auctionDetail_c_two_right {
    width: 100%;
    margin-left: 0;
    margin-top: 20px;
  }
}

@media (max-width: 768px) {
  .min_wrapper_1500 {
    min-width: auto;
  }

  .auctionDetail_c_one {
    height: auto;
    flex-direction: column;
    padding: 20px;
  }

  .auctionDetail_c_one_left {
    width: 100%;
    height: auto;
    padding-left: 0;
    margin-bottom: 20px;
  }

  .auction-items-scroll-container {
    width: 100%;
    height: auto;
    padding: 10px;
  }

  .auction-items-wrapper {
    flex-wrap: wrap;
    gap: 10px;
  }

  .auction-item-card {
    width: calc(50% - 5px);
    min-width: 120px;
  }

  .auctionDetail_c_two_left_two {
    flex-direction: column;
    margin-left: 0;
  }

  .auctionDetail_c_two_left_two_right {
    margin-left: 0;
    margin-top: 20px;
  }

  .auctionDetail_c_two_right {
    flex-direction: column;
  }

  .auctionDetail_c_three_one {
    flex-wrap: wrap;
  }

  .auctionDetail_c_three_ones,
  .auctionDetail_c_three_oness {
    flex: 1 1 50%;
  }
}

// 遮罩层效果样式
.masked-content {
  position: relative;
}

.content-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  z-index: 100;
  // pointer-events: none;
  display: flex;
  justify-content: center;
  padding-top: 100px;
  .login-btn {
    height: 20px;
    border-radius: 5px;
    // 添加下划线
    text-decoration: underline;
    cursor: pointer;
    &:hover {
      color: #004c66;
    }
  }
}

/* .masked-content .login-button-container {
  position: relative;
  z-index: 1;
  pointer-events: auto;
} */

.masked-content > *:not(.content-mask) {
  pointer-events: none;
}

.masked-content > *:not(.content-mask) * {
  pointer-events: none;
}

/* .masked-content .login-button-container {
  pointer-events: auto !important;
}

.masked-content .login-button-container * {
  pointer-events: auto !important;
} */

/* 报名提示和按钮左右布局样式 */
.registration-tip-row {
  display: flex;
  align-items: flex-start;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  margin-top: 13px;
  gap: 20px;
}

.registration-tip-row .tip-section {
  display: flex;
  flex-direction: column;
  gap: 6px;
  width: 238px;
}

.registration-tip-row .tip-item {
  display: flex;
  align-items: flex-start;
  gap: 5px;
  color: #004c66;
  font-size: 12px;
}

.warning-icon {
  width: 12px;
  height: 12px;
  flex-shrink: 0;
  margin-top: 2.5px;
  margin-right: 5px;
}

.registration-tip-row .button-section {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

/* 响应式设计 - 报名区域 */
@media (max-width: 768px) {
  .registration-tip-row {
    flex-direction: column;
    gap: 20px;
    padding: 15px;
  }

  .registration-tip-row .button-section {
    min-width: auto;
    width: 100%;
  }
}

/* 导航栏样式 */
.tabs-section {
  .tabs-header {
    display: flex;
    border-bottom: 1px solid;
    border-image: linear-gradient(
        to right,
        rgba(221, 221, 221, 1),
        rgba(255, 255, 255, 1)
      )
      1;
    margin-bottom: 20px;
    background-color: #fff;
    z-index: 1001;

    // 吸顶效果样式
    &.tabs-header-fixed {
      /* 只保留美化，不要写 position/left/width */
      margin-bottom: 0;
      padding-left: 20px;
      /* position/left/width 由 style 绑定控制 */
    }

    .tab-item {
      display: flex;
      align-items: center;
      padding: 15px 0;
      margin-right: 20px;
      cursor: pointer;
      color: #666;
      border-bottom: 2px solid transparent;
      transition: all 0.3s;
      font-size: 16px;

      &.active {
        color: #004c66;
        border-bottom-color: #004c66;
      }

      &:hover {
        color: #004c66;
      }

      .tab-icon {
        width: 18px;
        height: 18px;
        margin-right: 8px;
      }
    }
  }

  // 占位元素样式，防止固定定位时内容跳动
  .tabs-header-placeholder {
    height: 57px; // 与tabs-header的高度保持一致（15px padding * 2 + 文字高度 + 边框）
    margin-bottom: 20px;
  }

  .tabs-content {
    border-radius: 10px 0 10px 0;
    .section-content {
      margin: 21px 0;
      .section-title {
        display: block;
        font-family: "PingFang Bold";
        font-size: 20px;
        color: #333;
        margin-bottom: 20px;
      }
    }
  }

  .tab-content {
    padding: 30px;
    background: #fff;
    border-radius: 0 0 8px 8px;
    min-height: 300px;
  }
}

:deep(.section-body) {
  font-family: "PingFang Medium" !important;
  p {
    text-indent: 32px;
    line-height: 2;
  }
  img{
    width: 95%;
    object-fit: cover;
  }
}

.section-images{
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  .el-image{
    width: 49%;
    height: 300px;
    border-radius: 10px;
  }
}

/* ========== 响应式设计 - 针对用户缩放125%和150% ========== */

/* 通用响应式优化 */
@media screen and (max-width: 1279px) {
  /* 确保内容不会溢出 */
  * {
    box-sizing: border-box;
  }

  /* 图片响应式 */
  img {
    max-width: 100%;
    height: auto;
  }

  /* 表格响应式 */
  table {
    width: 100%;
    table-layout: fixed;
  }

  /* 文本溢出处理 */
  .text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* 按钮响应式 */
  button,
  .btn {
    min-height: 36px;
    padding: 8px 12px;
    font-size: 14px;
  }

  /* 输入框响应式 */
  input[type="text"],
  input[type="number"] {
    min-height: 36px;
    padding: 8px 12px;
    font-size: 14px;
  }
}

/* 125%缩放适配 (1024px - 1279px) */
@media screen and (max-width: 1279px) and (min-width: 1024px) {
  .min_wrapper_1500 {
    min-width: 1024px;
  }

  .auctionDetail_c {
    width: 1024px;
    padding: 0 20px;
    box-sizing: border-box;
  }

  /* 右侧面板调整 */
  .auctionDetail_c_two_wrapper {
    display: block;
  }

  .auctionDetail_c_two {
    flex-direction: column;
    gap: 20px;
  }

  /* 顶部区域调整 */
  .auctionDetail_c_one {
    height: auto;
    min-height: 180px;
    padding: 15px 20px;
    flex-direction: column;
    gap: 15px;
  }

  .auctionDetail_c_one_left {
    margin-right: 0;
    margin-top: 0;
    width: 100%;
  }

  .auctionDetail_c_one_left_s {
    width: 100%;
    font-size: 20px;
    margin-bottom: 10px;
  }

  /* 标的列表横向滚动容器 */
  .auction-items-scroll-container {
    max-width: 100%;
    height: 160px;
  }

  .auction-items-wrapper {
    height: 155px;
  }

  .auction-item-card {
    width: 180px;
    height: 120px;
  }

  /* 主内容区域 */
  .auctionDetail_c_two_left {
    width: 100%;
    padding: 15px;
  }

  /* 图片和信息区域 */
  .auctionDetail_c_two_left_two {
    flex-direction: column;
    gap: 20px;
  }

  .auctionDetail_c_two_left_two_leftssssss,
  .auctionDetail_c_two_left_two_left_o {
    width: 350px;
    height: 350px;
  }

  .auctionDetail_c_two_left_two_left_omaskTop {
    width: 350px;
    height: 350px;
  }

  /* 价格信息区域 */
  .auctionDetail_c_two_left_two_right {
    width: 100%;
    max-width: none;
  }

  /* 价格显示调整 */
  .price-amount {
    font-size: 28px;
  }

  .price-symbol {
    font-size: 20px;
  }

  .price-unit {
    font-size: 14px;
  }

  /* 按钮区域调整 */
  .registration-tip-row {
    flex-direction: column;
    gap: 15px;
    padding: 15px;
  }

  .registration-tip-row .tip-section {
    width: 100%;
  }

  /* 面包屑导航调整 */
  .header-section {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .breadcrumb {
    font-size: 14px;
  }

  .view-count {
    flex-direction: row;
    gap: 15px;
  }

  /* 导航栏调整 */
  .tabs-header {
    flex-wrap: wrap;
    gap: 10px;
  }

  .tab-item {
    font-size: 14px;
    margin-right: 15px;
    padding: 12px 0;
  }

  /* 竞价记录表格调整 */
  .chujialist {
    font-size: 13px;
  }

  .chujialist td {
    padding: 8px 6px;
  }
}

/* 150%缩放适配 (768px - 1023px) */
@media screen and (max-width: 1023px) and (min-width: 768px) {
  .min_wrapper_1500 {
    min-width: 768px;
  }

  .auctionDetail_c {
    width: 100%;
    max-width: 768px;
    padding: 0 15px;
    box-sizing: border-box;
  }

  /* 右侧面板完全垂直布局 */
  .auctionDetail_c_two_wrapper {
    display: block;
  }

  .auctionDetail_c_two {
    flex-direction: column;
    gap: 15px;
  }

  /* 顶部区域大幅调整 */
  .auctionDetail_c_one {
    height: auto;
    min-height: 160px;
    padding: 15px;
    flex-direction: column;
    gap: 15px;
  }

  .auctionDetail_c_one_left {
    margin: 0;
    width: 100%;
  }

  .auctionDetail_c_one_left_s {
    width: 100%;
    font-size: 18px;
    line-height: 1.4;
  }

  .auctionDetail_c_one_left_s_span {
    font-size: 18px;
  }

  .auction_c_one_img {
    font-size: 11px;
    padding: 2px 5px;
  }

  /* 标的列表调整 */
  .auction-items-scroll-container {
    max-width: 100%;
    height: 140px;
  }

  .auction-items-wrapper {
    height: 135px;
    gap: 8px;
  }

  .auction-item-card {
    width: 150px;
    height: 100px;
  }

  /* 主内容区域 */
  .auctionDetail_c_two_left {
    width: 100%;
    padding: 15px;
  }

  /* 图片和信息完全垂直布局 */
  .auctionDetail_c_two_left_two {
    flex-direction: column;
    gap: 20px;
  }

  .auctionDetail_c_two_left_two_leftssssss,
  .auctionDetail_c_two_left_two_left_o {
    width: 300px;
    height: 300px;
    margin: 0 auto;
  }

  .auctionDetail_c_two_left_two_left_omaskTop {
    width: 300px;
    height: 300px;
  }

  /* 图片列表容器调整 */
  .image-list-container-horizontal {
    max-width: 300px;
    margin: 0 auto;
  }

  /* 价格信息区域 */
  .auctionDetail_c_two_left_two_right {
    width: 100%;
    max-width: none;
  }

  /* 价格显示进一步调整 */
  .price-amount {
    font-size: 24px;
  }

  .price-symbol {
    font-size: 18px;
  }

  .price-integer {
    font-size: 24px;
  }

  .price-decimal {
    font-size: 18px;
  }

  .price-unit {
    font-size: 12px;
  }

  /* 倒计时区域调整 */
  .auctionDetail_c_two_left_two_right_one_w {
    padding: 10px;
  }

  .auctionDetail_c_two_left_two_right_one_w_bs {
    font-size: 14px;
  }

  .auctionDetail_c_two_left_two_right_one_w_bsb {
    font-size: 16px;
    padding: 2px 4px;
  }

  /* 按钮和提示区域 */
  .registration-tip-row {
    flex-direction: column;
    gap: 15px;
    padding: 15px;
  }

  .registration-tip-row .tip-section {
    width: 100%;
  }

  .tip-item {
    font-size: 11px;
  }

  /* 出价区域调整 */
  .auctionDetail_c_two_left_two_right_one_thre_chujia {
    flex-direction: column;
    gap: 10px;
  }

  .auctionDetail_c_two_left_two_right_one_thre_chujias input {
    font-size: 16px;
    padding: 8px;
  }

  .auctionDetail_c_two_left_two_right_one_thre_chujiass {
    font-size: 14px;
    padding: 10px 15px;
  }

  /* 导航栏大幅调整 */
  .tabs-header {
    flex-wrap: wrap;
    gap: 5px;
    padding: 10px 0;
  }

  .tab-item {
    font-size: 13px;
    margin-right: 10px;
    padding: 10px 0;
  }

  /* 面包屑导航调整 */
  .header-section {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .breadcrumb {
    font-size: 13px;
  }

  .auction-title {
    font-size: 13px;
  }

  .current {
    font-size: 13px;
  }

  .view-count {
    flex-direction: row;
    gap: 12px;
  }

  .collect-info,
  .view-info {
    font-size: 12px;
  }

  /* 竞价记录表格调整 */
  .chujialist {
    font-size: 12px;
  }

  .chujialist td {
    padding: 6px 4px;
  }

  /* 详细信息区域调整 */
  .auctionDetail_c_two_left_two_right_onefour {
    font-size: 12px;
    line-height: 1.6;
  }

  .auctionDetail_c_two_left_two_right_onefour > div {
    margin-bottom: 8px;
  }
}

/* 极小屏幕适配 (小于768px) - 针对更高缩放比例 */
@media screen and (max-width: 767px) {
  .min_wrapper_1500 {
    min-width: auto;
  }

  .auctionDetail_c {
    width: 100%;
    padding: 0 10px;
    box-sizing: border-box;
  }

  /* 顶部区域极简化 */
  .auctionDetail_c_one {
    height: auto;
    min-height: 120px;
    padding: 10px;
    flex-direction: column;
    gap: 10px;
  }

  .auctionDetail_c_one_left_s {
    font-size: 16px;
    line-height: 1.3;
  }

  .auctionDetail_c_one_left_s_span {
    font-size: 16px;
  }

  .auction_c_one_img {
    font-size: 10px;
    padding: 1px 4px;
  }

  /* 标的列表进一步压缩 */
  .auction-items-scroll-container {
    height: 120px;
  }

  .auction-items-wrapper {
    height: 115px;
    gap: 6px;
  }

  .auction-item-card {
    width: 120px;
    height: 80px;
  }

  /* 主内容区域 */
  .auctionDetail_c_two_left {
    padding: 10px;
  }

  /* 图片区域最小化 */
  .auctionDetail_c_two_left_two_leftssssss,
  .auctionDetail_c_two_left_two_left_o {
    width: 250px;
    height: 250px;
    margin: 0 auto;
  }

  .auctionDetail_c_two_left_two_left_omaskTop {
    width: 250px;
    height: 250px;
  }

  /* 图片列表容器 */
  .image-list-container-horizontal {
    max-width: 250px;
    margin: 0 auto;
  }

  /* 价格显示最小化 */
  .price-amount {
    font-size: 20px;
  }

  .price-symbol {
    font-size: 16px;
  }

  .price-integer {
    font-size: 20px;
  }

  .price-decimal {
    font-size: 14px;
  }

  .price-unit {
    font-size: 11px;
  }

  /* 导航栏最小化 */
  .tabs-header {
    flex-wrap: wrap;
    gap: 3px;
    padding: 8px 0;
  }

  .tab-item {
    font-size: 12px;
    margin-right: 8px;
    padding: 8px 0;
  }

  /* 按钮区域最小化 */
  .auctionDetail_c_two_left_two_right_one_thre_two {
    font-size: 12px;
    padding: 8px 12px;
  }

  /* 面包屑导航最小化 */
  .header-section {
    flex-direction: column;
    gap: 6px;
    align-items: flex-start;
  }

  .breadcrumb {
    font-size: 12px;
  }

  .auction-title {
    font-size: 12px;
  }

  .current {
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
  }

  .view-count {
    flex-direction: row;
    gap: 10px;
  }

  .collect-info,
  .view-info {
    font-size: 11px;
  }

  /* 竞价记录表格最小化 */
  .chujialist {
    font-size: 11px;
  }

  .chujialist td {
    padding: 4px 3px;
  }

  /* 详细信息最小化 */
  .auctionDetail_c_two_left_two_right_onefour {
    font-size: 11px;
    line-height: 1.5;
  }

  .auctionDetail_c_two_left_two_right_onefour > div {
    margin-bottom: 6px;
  }
}

/* ========== 固定定位元素响应式处理 ========== */
@media screen and (max-width: 1279px) {
  /* 导航栏固定定位调整 */
  .tabs-header-fixed {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    z-index: 1000 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  /* 右侧面板在小屏幕下不使用固定定位 */
  .auctionDetail_c_two_right {
    position: static !important;
    width: 100% !important;
    margin-top: 20px;
  }
}

/* ========== 内容遮罩层响应式处理 ========== */
@media screen and (max-width: 1279px) {
  .content-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    gap: 20px;
  }

  .content-mask .tips {
    font-size: 16px;
    color: #666;
    text-align: center;
  }

  .content-mask .login-btn {
    background: #004c66;
    color: white;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
  }

  .content-mask .login-btn:hover {
    background: #003a52;
  }
}

@media screen and (max-width: 767px) {
  .content-mask .tips {
    font-size: 14px;
    padding: 0 20px;
  }

  .content-mask .login-btn {
    padding: 10px 20px;
    font-size: 13px;
  }
}
</style>
