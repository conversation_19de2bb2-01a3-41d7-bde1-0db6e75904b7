# 行业资讯列表页面

## 概述

行业资讯列表页面是一个纯展示型的数据列表页面，用于查看和导出行业资讯数据。该页面不提供数据的增删改操作，专注于数据的查看和导出功能。

## 页面位置

- **页面文件**: `src/views/RecyclerManage/IndustryInfoList/index.vue`
- **路由路径**: `/recyclerManage/industryInfoList`
- **父级页面**: 回收商管理模块

## 功能特性

### ✅ 支持的功能

1. **数据展示**
   - 物资种类（字典显示）
   - 品类/细类
   - 地区信息（省份、城市、区县）
   - 价格信息（高价、低价、均价）
   - 变化信息（涨跌额、涨跌幅）
   - 时间信息（资讯日期、创建时间）

2. **筛选功能**
   - 物资种类：下拉选择筛选
   - 城市：输入框筛选
   - 资讯日期：日期范围筛选

3. **导出功能**
   - 位于水平导航栏右侧
   - 支持根据筛选条件导出
   - Excel格式文件下载

4. **分页功能**
   - 支持分页查询
   - 可调整每页显示数量
   - 显示总数据量

5. **交互功能**
   - 行点击事件（仅日志记录）
   - 行双击事件（仅日志记录）
   - 搜索加载状态显示

### ❌ 不支持的功能

1. **数据操作**
   - ❌ 新增行业资讯
   - ❌ 编辑行业资讯
   - ❌ 删除行业资讯
   - ❌ 批量删除
   - ❌ 导入数据

2. **选择功能**
   - ❌ 行选择
   - ❌ 全选/反选
   - ❌ 批量操作

## 表格配置

### 列配置

| 列名 | 字段名 | 宽度 | 对齐方式 | 类型 | 说明 |
|------|--------|------|----------|------|------|
| 序号 | index | 60px | center | - | 自动生成 |
| 物资种类 | materialType_dictText | 120px | center | - | 字典显示 |
| 品类/细类 | category | 120px | center | - | - |
| 省份 | province | 100px | center | - | - |
| 城市 | city | 100px | center | - | - |
| 区县 | district | 100px | center | - | - |
| 高价 | highPrice | 100px | center | money | 货币格式 |
| 低价 | lowPrice | 100px | center | money | 货币格式 |
| 均价 | avgPrice | 100px | center | money | 货币格式 |
| 涨跌额 | changeAmount | 100px | center | - | 带正负号 |
| 涨跌幅 | changeRate | 100px | center | - | 带正负号和% |
| 资讯日期 | infoDate | 120px | center | date | 日期格式 |
| 创建时间 | createTime | 160px | center | date | 日期格式 |

### 样式配置

```typescript
tableStyle: {
  maxHeight: 435,        // 表格最大高度
  border: false,         // 不显示边框
  stripe: true,          // 显示斑马纹
  showSelection: false   // 不显示选择列
}
```

## API接口

### 数据查询

- **接口地址**: `/hgy/waste/hgyIndustryInfo/list`
- **请求方法**: GET
- **请求参数**:
  ```typescript
  {
    pageNo: number,           // 页码
    pageSize: number,         // 每页数量
    materialType?: string,    // 物资种类
    city?: string,           // 城市
    infoDate_begin?: string, // 开始日期
    infoDate_end?: string    // 结束日期
  }
  ```

### 数据导出

- **接口地址**: `/hgy/waste/hgyIndustryInfo/exportXls`
- **请求方法**: GET
- **响应类型**: Blob (Excel文件)

## 数据格式化

### 价格显示

使用 `type: 'money'` 自动格式化为货币显示：
- 示例：`3100.00` → `¥3,100.00`

### 涨跌显示

**涨跌额**：
```typescript
formatter: (value) => {
  if (!value && value !== 0) return '-'
  const num = Number(value)
  if (num > 0) return `+${num}`
  return num.toString()
}
```

**涨跌幅**：
```typescript
formatter: (value) => {
  if (!value && value !== 0) return '-'
  const num = Number(value)
  if (num > 0) return `+${num}%`
  return `${num}%`
}
```

## 使用示例

### 基本使用

```vue
<template>
  <div class="industry-info-list-page">
    <DataTable
      :config="tableConfig"
      :search-loading="searchLoading"
      @on-button-click="handleButtonClick"
      @on-filter-change="handleFilterChange"
      @on-page-change="handlePageChange"
    />
  </div>
</template>
```

### 导出功能

```typescript
const handleExport = async () => {
  try {
    const blob = await industryInfoApi.exportIndustryInfo(exportParams)
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `行业资讯表_${new Date().toISOString().slice(0, 10)}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    ElMessage.error('导出失败，请稍后重试')
  }
}
```

## 注意事项

1. **只读模式**: 该页面为只读模式，不提供任何数据修改功能
2. **导出功能**: 导出按钮位于水平导航栏右侧，支持根据筛选条件导出
3. **数据格式**: 价格和涨跌数据有特殊的格式化显示
4. **分页查询**: 支持分页查询，默认每页10条数据
5. **筛选条件**: 支持物资种类、城市和日期范围筛选

## 更新历史

- **v1.0**: 初始版本，包含完整的增删改查功能
- **v1.1**: 移除按钮栏，将导出按钮移至水平导航栏
- **v1.2**: 移除所有编辑、删除、查看操作，改为纯展示页面
