<template>
  <div class="bidding">
    <!-- 轮播图部分 -->
    <div class="carousel-container">
      <Carousel :items="carouselItems" :autoplay="true" :interval="5000" />
    </div>

    <!-- 推荐标的板块 -->
    <div class="section">
      <div class="section-title">
        <div class="title-container">
          <SvgIcon iconName="bidding-recommend" className="title-icon" />
          <span>推荐标的</span>
        </div>
        <div class="more-container" @click="handleMoreClick('', '')">
          <span>更多</span>
          <SvgIcon iconName="auction-arrows-right" className="more-icon" />
        </div>
      </div>
      <div class="content-container">
        <div v-if="loading" class="loading">
          <div class="loading-text">加载中...</div>
        </div>
        <div v-else-if="auctionItems.length > 0" class="auction-cards">
          <AuctionCard
            v-for="item in auctionItems"
            :key="item.id"
            v-bind="item"
            @click="handleCardClick"
          />
        </div>
        <div v-else class="placeholder">
          <div class="placeholder-text">暂无推荐标的</div>
        </div>
      </div>
    </div>

    <!-- 成交案例板块 -->
    <div class="section">
      <div class="section-title">
        <div class="title-container">
          <SvgIcon iconName="bidding-case" className="title-icon" />
          <p>成交案例</p>
        </div>
        <div
          class="more-container"
          @click="handleMoreClick('status', 'completed')"
        >
          <span>更多</span>
          <SvgIcon iconName="auction-arrows-right" className="more-icon" />
        </div>
      </div>
      <div class="content-container">
        <div v-if="loading" class="loading">
          <div class="loading-text">加载中...</div>
        </div>
        <div v-else-if="accomplishItems.length > 0" class="auction-cards">
          <AuctionCard
            v-for="item in accomplishItems"
            :key="item.id"
            v-bind="item"
            @click="handleCardClick"
          />
        </div>
        <div v-else class="placeholder">
          <div class="placeholder-text">暂无成交案例</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import AuctionCard from "@/components/AuctionCard.vue";
import Carousel from "@/components/Carousel.vue";
import type { ListItem } from "@/types/auction";
import SvgIcon from "@/components/SvgIcon.vue";
import { useAuctions } from "@/composables/useAuctions";

const router = useRouter();

// 使用拍卖数据hooks
const { auctionItems, accomplishItems, loading, initAuctions } = useAuctions();

// 定义轮播图数据
const carouselItems = ref([
  {
    image: 'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/banner-bidding_1754963452117.jpg', // 使用导入的图片变量
    title: "",
  },
  {
    image:
      "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250603/17489456557790.png",
    title: "推广banner图展示位",
  },
  {
    image:
      "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250613/17497843321079.png",
    title: "推广banner图展示位",
  },
]);

// 组件挂载完成生命周期
onMounted(async () => {
  // 初始化拍卖数据
  await initAuctions();
});

// 处理卡片点击事件
const handleCardClick = (value: { productId: string; pmhId: string }) => {
  // 跳转到拍品详情页
  router.push({
    name: "auctionDetail",
    query: { id: value.productId, pmhId: value.pmhId, crumbsTitle: '竞价交易' },
  });
};

// 处理更多点击事件
const handleMoreClick = (name: string, type: string) => {
  // 跳转到标的信息页
  router.push({
    name: "biddingInfo",
    query: { name: name, type: type },
  });
};
</script>

<style lang="scss" scoped>
.bidding {
  margin: 0 auto;
  max-width: 1280px;
  margin-top: 20px;

  .carousel-container {
    margin-bottom: 20px;
  }

  .section {
    margin-bottom: 20px;
    background-color: #fff;
    border-radius: 10px;
    padding: 15px 20px 20px 20px;

    .section-title {
      margin-bottom: 7px;
      padding-bottom: 10px;
      border-bottom: 1px solid;
      border-image: linear-gradient(to right, #dddddd, #ffffff) 1;
      display: flex;
      align-items: center;
      .title-container {
        flex: 1;
        font-size: 18px;
        font-family: "PingFang Bold";
        color: #333333;
        display: flex;
        align-items: center;
        .title-icon {
          width: 24px;
          height: 26px;
          margin-right: 5px;
        }
      }
      .more-container {
        flex: 0 0 auto;
        display: flex;
        align-items: center;
        cursor: pointer;
        color: #999;
        span {
          font-size: 14px;
          font-family: "PingFang Regular";
          margin-right: 5px;
        }
        .more-icon {
          width: 6px;
          height: 10px;
        }
      }
    }
  }

  .auction-cards {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
  }

  .auction-list {
    display: flex;
    flex-direction: column;

    .item-divider {
      width: 100%;
      height: 1px;
      background-color: #dddddd;
      margin: 20px 0;
    }
  }

  .placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    border-radius: 10px;
    background-color: #f5f5f5;
  }

  .placeholder-text {
    font-size: 16px;
    color: #999999;
  }

  .loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    border-radius: 10px;
    background-color: #f9f9f9;
  }

  .loading-text {
    font-size: 16px;
    color: #666666;
    animation: pulse 1.5s ease-in-out infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
}
</style>
